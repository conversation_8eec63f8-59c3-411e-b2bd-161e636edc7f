{"name": "costcompass", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@clerk/clerk-expo": "^2.13.2", "@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/datetimepicker": "^8.4.1", "@react-navigation/bottom-tabs": "^7.3.15", "@react-navigation/native": "^7.1.11", "@react-navigation/stack": "^7.3.4", "expo": "~53.0.11", "expo-document-picker": "^13.1.5", "expo-file-system": "^18.1.10", "expo-linear-gradient": "^14.1.5", "expo-mail-composer": "^14.1.4", "expo-secure-store": "^14.2.3", "expo-sharing": "^13.1.5", "expo-sqlite": "^15.2.12", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.3", "react-native-chart-kit": "^6.12.0", "react-native-fs": "^2.20.0", "react-native-html-to-pdf": "^0.12.0", "react-native-paper": "^5.14.5", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.1", "react-native-share": "^12.1.0", "react-native-svg": "^15.12.0", "react-native-vector-icons": "^10.2.0", "react-native-web": "^0.20.0", "victory-native": "^41.17.4", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}