<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta httpEquiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <title>CostCompass</title>
    <style>
        html, body, #root {
            height: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
            overflow-y: auto;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background-color: #f5f5f5;
        }
        
        /* Ensure scrollable content */
        * {
            -webkit-overflow-scrolling: touch;
        }
        
        /* Fix for React Native ScrollView on web */
        [data-focusable="true"] {
            outline: none;
        }
    </style>
</head>
<body>
    <div id="root"></div>
</body>
</html>
