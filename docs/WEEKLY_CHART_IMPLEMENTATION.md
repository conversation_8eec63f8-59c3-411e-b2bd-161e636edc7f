# Weekly Chart Implementation

## Overview
Updated the benchmark vs actual comparison chart to display weekly data on the x-axis instead of monthly data, providing more granular analysis over a 12-week period.

## Key Changes Made

### 1. Time Period Adjustment
- **Previous**: 3 months with monthly aggregation
- **Current**: 12 weeks with weekly aggregation
- **Benefit**: More detailed trend analysis and pattern recognition

### 2. Week Calculation System
```javascript
// Helper function to get week key (YYYY-WW format)
const getWeekKey = (date) => {
  const startOfYear = new Date(date.getFullYear(), 0, 1);
  const pastDaysOfYear = (date - startOfYear) / 86400000;
  const weekNumber = Math.ceil((pastDaysOfYear + startOfYear.getDay() + 1) / 7);
  return `${date.getFullYear()}-W${String(weekNumber).padStart(2, '0')}`;
};

// Helper function to get week label (e.g., "Jan W1", "Jan W2")
const getWeekLabel = (date) => {
  const monthName = date.toLocaleDateString('en-US', { month: 'short' });
  const startOfMonth = new Date(date.getFullYear(), date.getMonth(), 1);
  const weekOfMonth = Math.ceil((date.getDate() + startOfMonth.getDay()) / 7);
  return `${monthName} W${weekOfMonth}`;
};
```

### 3. Data Aggregation Logic
- **Weekly Grouping**: Groups actual spend entries by week using ISO week numbering
- **Average Calculation**: Computes average actual spending per week
- **12-Week Range**: Covers approximately 3 months with weekly granularity
- **Missing Week Handling**: Shows weeks with no data as zero values

### 4. Chart Configuration Updates
```javascript
// Updated chart properties
width: Dimensions.get('window').width - 48  // Optimized for weekly labels
height: 240                                 // Increased height for better readability
propsForDots: { r: '4' }                   // Smaller dots for more data points
formatXLabel: (value) => value.length > 6 ? value.substring(0, 6) : value
```

## X-Axis Label Format

### Label Structure
- **Format**: `{Month} W{WeekNumber}`
- **Examples**: 
  - "Jan W1" = First week of January
  - "Feb W3" = Third week of February
  - "Dec W4" = Fourth week of December

### Label Generation
```javascript
for (let i = 11; i >= 0; i--) {
  const date = new Date();
  date.setDate(date.getDate() - (i * 7)); // Go back i weeks
  const weekKey = getWeekKey(date);
  const weekLabel = getWeekLabel(date);
  weeks.push(weekLabel);
}
```

## Benefits of Weekly Granularity

### 📊 **Enhanced Analysis**
- **Short-term Trends**: Identify weekly spending patterns
- **Seasonal Variations**: Detect weekly cycles and anomalies
- **Immediate Feedback**: See recent performance more clearly
- **Detailed Tracking**: Monitor week-to-week improvements

### 📈 **Better Decision Making**
- **Quick Adjustments**: React to weekly variances faster
- **Pattern Recognition**: Identify recurring weekly patterns
- **Budget Control**: More frequent checkpoints for budget adherence
- **Performance Monitoring**: Track weekly goal achievement

### 🎯 **User Experience**
- **Recent Focus**: Emphasizes recent performance over long-term averages
- **Actionable Insights**: Provides data for immediate action
- **Detailed View**: Shows granular performance without overwhelming
- **Trend Clarity**: Makes short-term trends more visible

## Technical Implementation

### Data Processing Flow
1. **Fetch Data**: Get all actual spends for the benchmark
2. **Filter Period**: Include only last 12 weeks of data
3. **Calculate Weeks**: Generate week keys and labels for each entry
4. **Group by Week**: Aggregate entries by week identifier
5. **Calculate Averages**: Compute average spending per week
6. **Fill Gaps**: Ensure all 12 weeks are represented
7. **Format Data**: Prepare data structure for chart rendering

### Performance Considerations
- **Efficient Grouping**: Uses optimized week calculation algorithms
- **Memory Usage**: Processes only relevant 12-week data subset
- **Responsive Design**: Chart adapts to screen width for optimal viewing
- **Smooth Rendering**: Bezier curves provide smooth line interpolation

## Chart Display Examples

### Sample Weekly Data
```
Week Labels: ['Nov W1', 'Nov W2', 'Nov W3', 'Nov W4', 'Dec W1', 'Dec W2', 'Dec W3', 'Dec W4', 'Jan W1', 'Jan W2', 'Jan W3', 'Jan W4']
Benchmark:   [25.00,   25.00,   25.00,   25.00,   28.00,   28.00,   28.00,   28.00,   30.00,   30.00,   30.00,   30.00]
Actual:      [30.00,   22.00,   0,       32.00,   28.00,   0,       35.00,   25.00,   0,       33.00,   27.00,   0]
```

### Visual Representation
- **Blue Line**: Benchmark amounts (may change over time)
- **Green Line**: Actual spending (shows weekly variations)
- **Data Points**: Individual week performance markers
- **Gaps**: Weeks with no data appear as zero values

The weekly chart implementation provides users with detailed, actionable insights into their spending patterns while maintaining the visual clarity and professional appearance of the original design.
