# Troubleshooting User Management Issues

## Problem: Can't see other user details in Management function

### Quick Diagnosis Steps

1. **Check if you're logged in as admin:**
   - Go to Profile screen
   - Look for "Administrator" badge
   - If not admin, you won't see Management tab

2. **Check if Management tab is visible:**
   - Look at bottom navigation
   - Management tab (⚙️) should be visible only to admins
   - If not visible, user is not admin

3. **Check User Management screen:**
   - Tap Management tab → User Management
   - Look at Debug Info card (orange background)
   - Check the numbers: Users Found and Filtered Users

### Common Issues & Solutions

#### Issue 1: No Management Tab Visible
**Cause:** User is not admin
**Solution:** 
- Login with admin account (`<EMAIL>` / `admin123`)
- Or make current user admin via database

#### Issue 2: Management Tab Visible but No Users Shown
**Cause:** No other users exist in database
**Solution:**
- Use "Create Sample Users" button in Debug Info card
- Or manually add users via "Add User" button

#### Issue 3: Users Exist but Not Displaying
**Cause:** Database or filtering issue
**Solution:**
- Check Debug Info for user counts
- Clear search filter
- Restart app

#### Issue 4: Database Issues
**Cause:** Database not initialized properly
**Solution:**
- Clear app data and restart
- Check console logs for errors

### Debug Information Available

The User Management screen now shows a Debug Info card with:
- Current user name and email
- Admin status (Yes/No)
- Company name and ID
- Number of users found
- Number of filtered users
- Button to create sample users

### Creating Test Users

Use the "Create Sample Users" button to add:
- John Doe (regular user)
- Jane Smith (regular user)  
- Manager User (admin user)

All with password: `password123`

### Expected Behavior

When working correctly, you should see:
1. **User Statistics Card** with counts
2. **Search bar** and controls
3. **List of users** with details:
   - Name and email
   - Last login time
   - Status badges (Admin, Inactive, You)
   - Action buttons for each user

### Admin Actions Available

For each user (except yourself):
- ✏️ **Edit Profile**: Change name/email
- 🔑 **Reset Password**: Set new password
- 👤+ **Make Admin**: Grant admin rights
- 👤- **Remove Admin**: Revoke admin rights
- 👤✓ **Activate**: Enable account
- 👤✗ **Deactivate**: Disable account
- 🗑️ **Delete**: Remove user

### Console Logs to Check

Look for these in console:
```
MainTabs - Current user: [user object]
MainTabs - Is admin: true/false
Loading users for company: [company info]
Loaded users: [array of users]
Number of users found: [number]
```

### Manual Database Check

If issues persist, check database directly:
```sql
SELECT * FROM users WHERE company_id = [your_company_id];
```

### Reset Steps

If nothing works:
1. Logout
2. Clear app data
3. Login with `<EMAIL>` / `admin123`
4. Go to Management → User Management
5. Use "Create Sample Users" button
6. Verify users appear

### Contact Support

If issues persist after trying these steps, provide:
- Debug Info card details
- Console log output
- Steps you've tried
- Expected vs actual behavior
