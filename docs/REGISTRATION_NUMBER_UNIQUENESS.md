# Company Registration Number Uniqueness Validation

## Overview
Implemented comprehensive validation to ensure company registration numbers are unique across all companies in the system. This prevents conflicts and ensures data integrity for company identification.

## Problem Addressed
Previously, users could create companies with duplicate registration numbers, which could lead to:
- **Confusion in company identification**
- **Compliance issues** with regulatory requirements
- **Data integrity problems**
- **Potential legal conflicts**

## Solution Implemented

### 1. Database-Level Validation
Added `checkRegistrationNumberExists()` method to DatabaseService:

```javascript
async checkRegistrationNumberExists(registrationNumber, excludeCompanyId = null) {
  // Skip check if registration number is empty
  if (!registrationNumber || !registrationNumber.trim()) {
    return false;
  }

  const trimmedRegistration = registrationNumber.trim();

  // Check for existing registration (case-insensitive)
  // Exclude current company ID for updates
  if (this.isWeb) {
    const existing = this.mockData.companies.find(c => 
      c.registration && 
      c.registration.toLowerCase() === trimmedRegistration.toLowerCase() &&
      c.id !== excludeCompanyId
    );
    return !!existing;
  } else {
    let query = 'SELECT id FROM companies WHERE LOWER(registration) = LOWER(?)';
    let params = [trimmedRegistration];
    
    if (excludeCompanyId) {
      query += ' AND id != ?';
      params.push(excludeCompanyId);
    }
    
    const existing = await this.db.getFirstAsync(query, params);
    return !!existing;
  }
}
```

### 2. Company Creation Validation
Updated `createCompany()` method:

```javascript
async createCompany(companyData) {
  const { accountNumber, name, registration, capacity, currentOccupancy, capacityType } = companyData;

  // Check for duplicate registration number
  if (registration && registration.trim()) {
    const registrationExists = await this.checkRegistrationNumberExists(registration);
    if (registrationExists) {
      throw new Error('A company with this registration number already exists');
    }
  }

  // ... proceed with company creation
}
```

### 3. Company Update Validation
Updated `updateCompany()` method:

```javascript
async updateCompany(id, companyData) {
  const { name, registration, capacity, currentOccupancy, capacityType } = companyData;
  
  // Check for duplicate registration number (excluding current company)
  if (registration && registration.trim()) {
    const registrationExists = await this.checkRegistrationNumberExists(registration, id);
    if (registrationExists) {
      throw new Error('A company with this registration number already exists');
    }
  }

  // ... proceed with company update
}
```

## Validation Features

### 1. Case-Insensitive Comparison
- **"REG123"** and **"reg123"** are treated as the same
- **Prevents case-based duplicates**
- **Consistent validation** across platforms

### 2. Whitespace Handling
- **Trims whitespace** from registration numbers
- **Ignores empty/null** registration numbers
- **Consistent formatting** for comparison

### 3. Update Exclusion
- **Excludes current company** when updating
- **Allows company to keep** its own registration number
- **Prevents false positives** during updates

### 4. Cross-Platform Support
- **Web platform**: Uses in-memory mockData
- **Mobile platform**: Uses SQLite database
- **Consistent behavior** across platforms

## Entry Points Protected

### 1. User Registration (First User)
- **RegisterScreen** → AuthContext.register() → DatabaseService.createCompany()
- **Error message**: "A company with this registration number already exists"
- **User feedback**: Clear error dialog during registration

### 2. Company Profile Editing
- **ProfileScreen** → AuthContext.updateCompany() → DatabaseService.updateCompany()
- **Admin-only access** with proper validation
- **Real-time validation** during save operation

### 3. Company Management Screen
- **CompanyManagementScreen** → AuthContext.updateCompany() → DatabaseService.updateCompany()
- **Dedicated company editing** interface
- **Comprehensive validation** before save

## Error Handling

### User-Friendly Messages
```javascript
// Clear, actionable error messages
"A company with this registration number already exists"
```

### Proper Error Propagation
```javascript
// Database level
throw new Error('A company with this registration number already exists');

// AuthContext level
return { success: false, error: error.message };

// UI level
Alert.alert('Error', result.error || 'Failed to update company details');
```

## Testing Scenarios

### 1. Company Creation Tests
- **Scenario**: Create company with existing registration number
- **Expected**: Error message, creation fails
- **Verification**: No duplicate company created

### 2. Company Update Tests
- **Scenario**: Update company to use existing registration number
- **Expected**: Error message, update fails
- **Verification**: Original registration number preserved

### 3. Case Sensitivity Tests
- **Scenario**: Create "REG123" when "reg123" exists
- **Expected**: Error message, creation fails
- **Verification**: Case-insensitive validation working

### 4. Self-Update Tests
- **Scenario**: Update company keeping same registration number
- **Expected**: Update succeeds
- **Verification**: Company can keep its own registration

### 5. Empty Registration Tests
- **Scenario**: Create/update company with empty registration
- **Expected**: Operation succeeds
- **Verification**: Empty registrations allowed

## Benefits

### 1. Data Integrity
- **Unique identification** for each company
- **Prevents conflicts** in company records
- **Maintains consistency** across the system

### 2. Compliance
- **Regulatory compliance** for unique registration numbers
- **Audit trail** for company identification
- **Legal protection** against duplicate registrations

### 3. User Experience
- **Clear error messages** when duplicates detected
- **Immediate feedback** during data entry
- **Prevents confusion** from duplicate companies

### 4. System Reliability
- **Database-level validation** ensures consistency
- **Cross-platform support** for all environments
- **Robust error handling** prevents system issues

## Implementation Details

### Database Schema
```sql
-- Companies table (registration field)
CREATE TABLE IF NOT EXISTS companies (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  account_number TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  registration TEXT,  -- This field is validated for uniqueness
  capacity INTEGER NOT NULL,
  current_occupancy INTEGER DEFAULT 0,
  capacity_type TEXT NOT NULL CHECK (capacity_type IN ('beds', 'rooms', 'persons')),
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### Validation Logic Flow
1. **User enters registration number**
2. **System trims and validates format**
3. **Database checks for existing registration** (case-insensitive)
4. **If duplicate found**: Error thrown and propagated
5. **If unique**: Operation proceeds normally

## Future Enhancements

### Potential Improvements
1. **Registration format validation** (e.g., specific patterns)
2. **International registration standards** support
3. **Bulk validation** for data imports
4. **Registration history tracking**
5. **Advanced search** by registration number

### Integration Opportunities
1. **Government registration APIs** for validation
2. **Company verification services**
3. **Automated registration lookup**
4. **Compliance reporting** features

The registration number uniqueness validation ensures data integrity and prevents conflicts while providing clear user feedback and maintaining system reliability across all platforms.
