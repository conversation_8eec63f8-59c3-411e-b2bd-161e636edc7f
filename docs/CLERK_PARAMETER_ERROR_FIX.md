# Clerk Parameter Error Fix

## 🎯 **Issue Identified:**

**Error**: `first_name is not a valid parameter for this request`
**Cause**: First and last name fields are not enabled in Clerk Dashboard settings

## ✅ **Immediate Fix Applied:**

### **1. Removed Name Parameters**
```javascript
// OLD (causing error):
const result = await signUp.create({
  emailAddress: email.trim(),
  password,
  firstName: firstName.trim(),  // ❌ Not enabled in dashboard
  lastName: lastName.trim(),    // ❌ Not enabled in dashboard
});

// NEW (working):
const result = await signUp.create({
  emailAddress: email.trim(),
  password,
});
```

### **2. Enhanced Error Debugging**
- Added specific suggestion for `form_param_unknown` errors
- Provides exact dashboard setting to enable
- Shows which parameter is causing the issue

## 🔧 **Dashboard Configuration Required:**

### **To Enable First/Last Name (Optional):**
1. Go to [Clerk Dashboard](https://dashboard.clerk.com)
2. Navigate to **"User & Authentication" → "Email, Phone, Username"**
3. **Enable "First and last name"** toggle
4. Click **"Save"**

### **Current Working Configuration:**
- ✅ Email address (enabled)
- ✅ Password (enabled)
- ❌ First/Last name (disabled - causing error)

## 🧪 **Test the Fix:**

### **Step 1: Test Basic Sign-Up**
1. Try registration with just email and password
2. Should work without the parameter error
3. Check if email verification is sent

### **Step 2: Add Names Later (Optional)**
If you want to collect names:
1. Enable "First and last name" in dashboard
2. Update code to include firstName/lastName again
3. Test with name fields

## 📋 **Complete Working Flow:**

### **Current Flow (Working):**
1. **User Info**: Email + Password only
2. **Company Info**: Company details
3. **Clerk Sign-Up**: Create account with email/password
4. **Email Verification**: Verify email address
5. **Payment**: Process subscription
6. **Database Setup**: Create company and user records

### **Optional Enhanced Flow:**
If you enable names in dashboard:
1. **User Info**: Email + Password + First/Last Name
2. Rest of flow remains the same

## 🚨 **Other Potential Issues to Check:**

### **1. Email Address Settings**
- Ensure "Email address" is enabled
- Verify "Require verification" is enabled
- Check verification strategy is "Email code"

### **2. Password Settings**
- Ensure "Password" is enabled in authentication strategies
- Check minimum password requirements

### **3. Native API (Critical)**
- **Must be enabled** in "Configure" → "Native Applications"
- This is required for Expo apps

## 🎯 **Expected Result:**

After this fix:
- ✅ **Sign-up should work** with email and password
- ✅ **Email verification** should be sent
- ✅ **No more parameter errors**
- ✅ **Clear error messages** if other issues occur

## 🔄 **Next Steps:**

1. **Test the registration** with simplified parameters
2. **Check email verification** works
3. **Complete the payment flow**
4. **Optionally enable names** in dashboard if needed

## 📝 **Code Changes Made:**

### **Files Modified:**
- `src/screens/ClerkSignUpScreen.js` - Removed firstName/lastName parameters
- `src/components/ClerkErrorDebugger.js` - Added specific error suggestions

### **Temporary Solution:**
- Names are not collected during registration
- Can be added later via user profile or dashboard settings
- Focus on core functionality first

## 🎉 **Benefits:**

- ✅ **Eliminates parameter error**
- ✅ **Simpler registration flow**
- ✅ **Faster testing and debugging**
- ✅ **Clear path to add names later**

The registration should now work! Test it and let me know if you get any other errors. 🚀
