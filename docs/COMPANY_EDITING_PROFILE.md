# Company Details Editing in Profile Screen

## Overview
Added company details editing functionality to the ProfileScreen, allowing admin users to update company information directly from their profile. This feature is restricted to admin users only for security and access control.

## Features Added

### 1. Admin-Only Access Control
- **Edit button only visible to admin users**
- **Access validation** prevents non-admin users from editing
- **Clear error message** if non-admin tries to access

### 2. Company Information Editing
- **Company Name** - Required field
- **Registration Number** - Optional field
- **Capacity** - Required numeric field (positive numbers only)
- **Current Occupancy** - Optional numeric field (cannot exceed capacity)
- **Capacity Type** - Radio button selection (Beds/Rooms/Persons)

### 3. Professional Dialog Interface
- **Modal dialog** for editing company details
- **Form validation** with clear error messages
- **Loading states** during save operations
- **Responsive design** that works on all screen sizes

## User Interface Changes

### Company Information Card Enhancement
```javascript
// Before: Static display only
<Text variant="titleLarge">Company Information</Text>

// After: Header with edit button for admins
<View style={styles.sectionHeader}>
  <Text variant="titleLarge">Company Information</Text>
  {user?.is_admin && (
    <Button mode="text" onPress={handleEditCompany} icon="pencil">
      Edit
    </Button>
  )}
</View>
```

### Edit Company Dialog
- **Professional modal dialog** with form fields
- **Input validation** for all fields
- **Radio button selection** for capacity type
- **Save/Cancel actions** with loading indicators

## Access Control Implementation

### Admin Check
```javascript
const handleEditCompany = () => {
  if (!user?.is_admin) {
    Alert.alert('Access Denied', 'Only administrators can edit company details');
    return;
  }
  setEditCompanyDialogVisible(true);
};
```

### Visual Indicators
- **Edit button only appears for admin users**
- **No edit functionality visible to regular users**
- **Clear access denied message** if somehow accessed

## Form Validation

### Validation Rules
1. **Company Name**: Required, cannot be empty
2. **Capacity**: Required, must be positive integer
3. **Current Occupancy**: Optional, cannot be negative, cannot exceed capacity
4. **Registration**: Optional field
5. **Capacity Type**: Must select one option (Beds/Rooms/Persons)

### Error Messages
- "Please enter a company name"
- "Please enter a valid capacity (positive number)"
- "Current occupancy cannot be negative"
- "Current occupancy cannot exceed capacity"

## Technical Implementation

### State Management
```javascript
// Company editing state
const [editCompanyDialogVisible, setEditCompanyDialogVisible] = useState(false);
const [companyLoading, setCompanyLoading] = useState(false);
const [companyName, setCompanyName] = useState('');
const [registration, setRegistration] = useState('');
const [capacity, setCapacity] = useState('');
const [currentOccupancy, setCurrentOccupancy] = useState('');
const [capacityType, setCapacityType] = useState('beds');
```

### Data Initialization
```javascript
// Initialize form data when company changes
useEffect(() => {
  if (company) {
    setCompanyName(company.name || '');
    setRegistration(company.registration || '');
    setCapacity(company.capacity?.toString() || '');
    setCurrentOccupancy(company.current_occupancy?.toString() || '');
    setCapacityType(company.capacity_type || 'beds');
  }
}, [company]);
```

### Save Operation
```javascript
const handleSaveCompany = async () => {
  if (!validateCompanyForm()) return;
  
  setCompanyLoading(true);
  
  try {
    const updateData = {
      name: companyName.trim(),
      registration: registration.trim(),
      capacity: parseInt(capacity),
      currentOccupancy: parseInt(currentOccupancy) || 0,
      capacityType
    };

    const result = await updateCompany(updateData);
    
    if (result.success) {
      setEditCompanyDialogVisible(false);
      Alert.alert('Success', 'Company details updated successfully!');
    } else {
      Alert.alert('Error', result.error || 'Failed to update company details');
    }
  } catch (error) {
    Alert.alert('Error', 'Failed to update company details. Please try again.');
  } finally {
    setCompanyLoading(false);
  }
};
```

## Security Features

### 1. Admin-Only Visibility
- Edit button only shown to admin users
- UI completely hidden from regular users

### 2. Backend Validation
- Uses existing `updateCompany` function from AuthContext
- Leverages existing database validation
- Proper error handling and user feedback

### 3. Access Control
- Function-level admin checks
- Clear error messages for unauthorized access
- No sensitive operations exposed to non-admins

## User Experience

### For Admin Users
1. **See "Edit" button** next to "Company Information" title
2. **Click "Edit"** → Professional dialog opens
3. **Modify company details** with real-time validation
4. **Save changes** → Success confirmation
5. **Updated information** immediately visible in profile

### For Regular Users
1. **See company information** (read-only)
2. **No edit button visible**
3. **Clean, uncluttered interface**
4. **No access to editing functionality**

## Benefits

### 1. Convenience
- **Quick access** to company editing from profile
- **No need to navigate** to separate company management screen
- **Integrated experience** within user profile

### 2. Security
- **Admin-only access** prevents unauthorized changes
- **Proper validation** ensures data integrity
- **Clear access control** with appropriate messaging

### 3. User Experience
- **Professional interface** matching app design
- **Clear form validation** with helpful error messages
- **Responsive design** works on all devices

## Integration with Existing Systems

### AuthContext Integration
- Uses existing `updateCompany` function
- Leverages current authentication state
- Maintains consistency with other company operations

### Database Integration
- Uses existing `DatabaseService.updateCompany` method
- Maintains data integrity and validation
- Consistent with other company management features

## Future Enhancements

### Potential Additions
1. **Audit logging** for company changes
2. **Change history** tracking
3. **Bulk company operations** for multi-company setups
4. **Advanced validation** rules
5. **Company logo upload** functionality

### Integration Opportunities
1. **Notification system** for company changes
2. **Approval workflows** for sensitive changes
3. **Backup/restore** functionality
4. **Export company data** features

## Testing Scenarios

### Admin User Tests
1. **Edit button appears** in company information card
2. **Dialog opens** when edit button clicked
3. **Form validation** works correctly
4. **Save operation** updates company successfully
5. **Success message** appears after save

### Regular User Tests
1. **Edit button not visible** in company information
2. **No access to editing functionality**
3. **Company information displayed** correctly (read-only)

### Validation Tests
1. **Required field validation** (name, capacity)
2. **Numeric validation** (capacity, occupancy)
3. **Business logic validation** (occupancy ≤ capacity)
4. **Error message display** for invalid inputs

The company editing feature provides admin users with convenient access to update company details while maintaining proper security and access controls.
