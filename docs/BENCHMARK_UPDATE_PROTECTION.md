# Benchmark Update Protection

## Problem Statement
When a benchmark amount is updated, it should not affect historical actual spend data or variance calculations. Previously, updating a benchmark would make historical variance calculations appear incorrect because:

1. **Variance was stored correctly** - Calculated with the benchmark amount at the time of entry
2. **But historical display was wrong** - Showed current benchmark amount vs stored variance
3. **Created confusion** - Made it appear that variance calculations were incorrect

## Solution Implemented

### Database Schema Changes
Added `benchmark_amount_at_time` column to `actual_spends` table:

```sql
ALTER TABLE actual_spends ADD COLUMN benchmark_amount_at_time REAL NOT NULL;
```

This stores the benchmark amount that was used for variance calculation at the time the actual spend was recorded.

### Data Integrity Protection
1. **Historical Data Preserved** - Old actual spend records maintain their original variance calculations
2. **Accurate Display** - Historical data table shows the benchmark amount that was used for each entry
3. **New Entries** - Continue to use current benchmark amount for new actual spend entries

### Implementation Details

#### Database Service Changes
- **createActualSpend()**: Now stores `benchmark_amount_at_time` alongside variance
- **Migration Function**: Automatically updates existing data with current benchmark amounts
- **Cross-Platform**: Works on both web (mock data) and mobile (SQLite)

#### UI Changes
- **Historical Data Table**: Shows `spend.benchmark_amount_at_time` instead of current `benchmark.amount`
- **Fallback Handling**: Uses current benchmark amount if historical amount is not available

### Migration Strategy
The system automatically handles existing data:

1. **New Installations**: Create table with new column from start
2. **Existing Installations**: 
   - Detect missing column using `PRAGMA table_info()`
   - Add column with `ALTER TABLE`
   - Populate existing records with current benchmark amounts
   - Future entries use benchmark amount at time of creation

### Benefits
✅ **Historical Accuracy** - Past variance calculations remain unchanged
✅ **Data Integrity** - No loss of historical information
✅ **User Confidence** - Variance calculations always appear correct
✅ **Audit Trail** - Can see what benchmark amount was used for each entry
✅ **Backward Compatible** - Existing installations upgrade seamlessly

## Usage Example

### Before Update
- Benchmark: $25.00/day
- Actual Spend: $30.00/day  
- Variance: +$5.00 (stored in DB)

### After Benchmark Update to $28.00/day
- **Historical Entry Display**:
  - Benchmark: $25.00 (from `benchmark_amount_at_time`)
  - Actual: $30.00
  - Variance: +$5.00 ✅ (still correct)

- **New Entry**:
  - Uses current benchmark: $28.00/day
  - Variance calculated against $28.00

## Technical Notes
- Migration runs automatically on app startup
- Web platform uses mock data migration
- Mobile platform uses SQLite ALTER TABLE
- No user intervention required
- Existing variance calculations remain untouched
