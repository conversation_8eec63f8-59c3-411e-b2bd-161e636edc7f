# Registration Validation Flow - Complete Solution

## Problem Addressed
Previously, the system would:
1. **Create user account first**
2. **Then try to create company**
3. **If company registration exists** → Company creation fails
4. **Result**: Orphaned user account with no company

## Solution Implemented

### 1. Pre-Validation Registration Flow
**New Flow**: Validate company registration BEFORE creating user account

```javascript
// STEP 1: Check if user email exists
const existingUser = await DatabaseService.getUserByEmail(email);
if (existingUser) {
  throw new Error('User with this email already exists');
}

// STEP 2: Validate company registration BEFORE creating user
if (companyData && companyData.registration) {
  const registrationExists = await DatabaseService.checkRegistrationNumberExists(companyData.registration);
  if (registrationExists) {
    throw new Error('A company with this registration number already exists in the system. Please contact the administrator to add you to the existing company account.');
  }
}

// STEP 3: Only create user if all validations pass
const userId = await DatabaseService.createUser({...});
```

### 2. Enhanced Error Messaging
**Clear guidance** for users when registration conflicts occur:

```
"A company with this registration number already exists in the system. 
Please contact the administrator to add you to the existing company account."
```

### 3. Registration Scenarios

#### **Scenario A: Unique Registration (Success)**
1. **User enters**: Company registration "NEWCARE123"
2. **System checks**: Registration doesn't exist
3. **Result**: Account and company created successfully

#### **Scenario B: Duplicate Registration (Prevented)**
1. **User enters**: Company registration "REG123456" (already exists)
2. **System checks**: Registration exists in database
3. **Result**: Registration fails with clear error message
4. **No orphaned accounts**: User account never created

#### **Scenario C: Join Existing Company**
1. **User toggles OFF**: "Create New Company"
2. **System assigns**: User to existing demo company
3. **Result**: User account created, joins existing company

## Technical Implementation

### AuthContext Changes

#### **Pre-Validation Logic**
```javascript
// Handle company creation/validation
if (companyData) {
  console.log('Company data provided, validating registration...');
  
  // CRITICAL: Check company registration BEFORE creating user account
  if (companyData.registration && companyData.registration.trim()) {
    console.log('Checking if registration number exists:', companyData.registration);
    const registrationExists = await DatabaseService.checkRegistrationNumberExists(companyData.registration);
    if (registrationExists) {
      console.log('Company registration already exists:', companyData.registration);
      throw new Error('A company with this registration number already exists in the system. Please contact the administrator to add you to the existing company account.');
    }
    console.log('Registration number is unique, proceeding...');
  }

  // Only proceed with company creation if validation passes
  console.log('Creating new company...');
  const accountNumber = await DatabaseService.generateAccountNumber();
  companyId = await DatabaseService.createCompany({
    accountNumber,
    ...companyData
  });
}
```

#### **Admin Status Logic**
```javascript
// User becomes admin if:
// 1. First user in system, OR
// 2. Creating a new company (even if not first user)
isAdmin: state.isFirstUser || (companyData && !state.isFirstUser)
```

### Database Validation

#### **Registration Check Function**
```javascript
async checkRegistrationNumberExists(registrationNumber, excludeCompanyId = null) {
  // Skip check if registration number is empty
  if (!registrationNumber || !registrationNumber.trim()) {
    return false;
  }

  const trimmedRegistration = registrationNumber.trim();

  // Case-insensitive comparison
  // Exclude current company ID for updates
  if (this.isWeb) {
    const existing = this.mockData.companies.find(c => 
      c.registration && 
      c.registration.toLowerCase() === trimmedRegistration.toLowerCase() &&
      c.id !== excludeCompanyId
    );
    return !!existing;
  } else {
    let query = 'SELECT id FROM companies WHERE LOWER(registration) = LOWER(?)';
    let params = [trimmedRegistration];
    
    if (excludeCompanyId) {
      query += ' AND id != ?';
      params.push(excludeCompanyId);
    }
    
    const existing = await this.db.getFirstAsync(query, params);
    return !!existing;
  }
}
```

## User Interface Enhancements

### Helper Text for Registration Field
```javascript
<TextInput
  label="Company Registration"
  value={companyRegistration}
  onChangeText={setCompanyRegistration}
  mode="outlined"
  style={styles.input}
  disabled={loading}
/>
<Text variant="bodySmall" style={styles.helperText}>
  If your company is already registered in the system, you'll be asked to contact the administrator to be added to the existing account.
</Text>
```

### Clear Error Display
- **Professional error dialogs** with actionable guidance
- **No technical jargon** in user-facing messages
- **Clear next steps** for users when conflicts occur

## Registration Flow Validation

### Validation Order
1. **Email uniqueness** - Prevent duplicate user emails
2. **Form validation** - Required fields, password strength, etc.
3. **Company registration uniqueness** - CRITICAL: Before user creation
4. **User account creation** - Only if all validations pass
5. **Company creation** - Only if user creation succeeds

### Error Handling
```javascript
// Registration validation errors
"User with this email already exists"
"Please fill in all required fields"
"A company with this registration number already exists in the system. Please contact the administrator to add you to the existing company account."

// Success flow
"Account created successfully"
"Welcome to CostCompass"
```

## Benefits of New Flow

### 1. Data Integrity
- **No orphaned user accounts** without companies
- **Atomic operations** - all or nothing approach
- **Consistent database state** at all times

### 2. User Experience
- **Clear error messages** with actionable guidance
- **No confusion** about account status
- **Professional error handling** with next steps

### 3. Administrative Control
- **Prevents unauthorized company creation** with existing registrations
- **Directs users to proper channels** for joining existing companies
- **Maintains company access control**

### 4. System Reliability
- **Prevents data inconsistencies**
- **Reduces support requests** from confused users
- **Maintains referential integrity**

## Testing Scenarios

### Test Case 1: Unique Registration (Should Succeed)
1. **Toggle ON**: "Create New Company"
2. **Enter unique registration**: "TESTCARE2024"
3. **Complete registration**
4. **Expected**: Account and company created successfully

### Test Case 2: Duplicate Registration (Should Fail)
1. **Toggle ON**: "Create New Company"
2. **Enter existing registration**: "REG123456"
3. **Complete registration**
4. **Expected**: Error message, no account created

### Test Case 3: Join Existing Company (Should Succeed)
1. **Toggle OFF**: "Create New Company"
2. **Complete registration** (no company fields)
3. **Expected**: User account created, joins demo company

### Test Case 4: Case Sensitivity (Should Fail)
1. **Toggle ON**: "Create New Company"
2. **Enter**: "reg123456" (lowercase of existing "REG123456")
3. **Complete registration**
4. **Expected**: Error message, case-insensitive validation working

## Error Message Examples

### Registration Conflict
```
Alert Title: "Registration Failed"
Alert Message: "A company with this registration number already exists in the system. Please contact the administrator to add you to the existing company account."
```

### Success Message
```
Alert Title: "Account Created"
Alert Message: "Your account has been created successfully. Welcome to CostCompass!"
```

## Administrative Guidance

### For Users Getting Registration Errors
1. **Contact the administrator** of the existing company
2. **Provide your email address** for account invitation
3. **Administrator can add you** through User Management screen
4. **You'll receive access** to the existing company account

### For Administrators
1. **Use User Management screen** to add users to existing company
2. **Set appropriate permissions** for new users
3. **Assign spending access** as needed
4. **No need for users to create duplicate companies**

The new registration validation flow ensures data integrity while providing clear guidance to users when registration conflicts occur, preventing orphaned accounts and maintaining system consistency.
