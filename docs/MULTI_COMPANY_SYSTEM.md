# Multi-Company User System - Complete Implementation

## Overview
Implemented a comprehensive multi-company/multi-tenant system where users can belong to multiple companies with different roles and permissions in each company.

## Key Features

### 1. **Multi-Company User Access**
- Users can belong to multiple companies
- Different roles (admin/user) per company
- Different permissions per company
- Company-specific data access

### 2. **Company Selection Flow**
- **Single Company**: Auto-login to that company
- **Multiple Companies**: Show company selection screen
- **No Companies**: Error with admin contact message

### 3. **Company Switching**
- Switch between companies from profile screen
- Maintains separate roles/permissions per company
- Real-time role updates when switching

## Database Schema Changes

### New Table: user_companies (Many-to-Many Relationship)
```sql
CREATE TABLE IF NOT EXISTS user_companies (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id INTEGER NOT NULL,
  company_id INTEGER NOT NULL,
  is_admin INTEGER DEFAULT 0,
  permissions TEXT DEFAULT '[]',
  is_active INTEGER DEFAULT 1,
  joined_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  <PERSON><PERSON>EIG<PERSON> KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
  FOREIGN KEY (company_id) REFERENCES companies (id) ON DELETE CASCADE,
  UNIQUE(user_id, company_id)
);
```

### Enhanced Database Methods
```javascript
// Add user to company
await DatabaseService.addUserToCompany(userId, companyId, {
  isAdmin: true,
  permissions: ['view_benchmarks', 'edit_spending'],
  isActive: true
});

// Get user's companies
const userCompanies = await DatabaseService.getUserCompanies(userId);

// Update user role in company
await DatabaseService.updateUserCompanyRole(userId, companyId, {
  isAdmin: false,
  permissions: ['view_benchmarks']
});

// Remove user from company
await DatabaseService.removeUserFromCompany(userId, companyId);
```

## Authentication Flow

### Login Process
```javascript
const login = async (email, password) => {
  // 1. Validate credentials
  const user = await DatabaseService.getUserByEmail(email);
  
  // 2. Get user's companies
  const userCompanies = await DatabaseService.getUserCompanies(user.id);
  
  // 3. Handle company selection
  if (userCompanies.length === 0) {
    throw new Error('No companies found');
  } else if (userCompanies.length === 1) {
    // Auto-select single company
    selectCompany(userCompanies[0]);
  } else {
    // Show company selection screen
    setNeedsCompanySelection(true);
  }
};
```

### Company Selection Logic
```javascript
// AuthContext state management
const initialState = {
  user: null,
  company: null,
  userCompanies: [],
  needsCompanySelection: false,
  // ... other states
};
```

## User Interface Components

### 1. Company Selection Screen
**Location**: `src/screens/CompanySelectionScreen.js`

**Features**:
- Lists all user's companies
- Shows user role (Admin/User) per company
- Shows join date
- Company details (name, registration, capacity)
- One-click company selection

**UI Elements**:
```javascript
// Company card with role badges
<Card>
  <Text>{company.name}</Text>
  <Text>Registration: {company.registration}</Text>
  <Chip icon="shield-account">Admin</Chip>
  <Chip icon="calendar">Joined {joinDate}</Chip>
  <Button onPress={() => selectCompany(company)}>
    Select Company
  </Button>
</Card>
```

### 2. Company Switcher in Profile
**Location**: `src/screens/ProfileScreen.js`

**Features**:
- "Switch" button (only shown if user has multiple companies)
- Modal dialog with company list
- Current company highlighted
- Role indicators per company
- One-click switching

**UI Elements**:
```javascript
// Switch button in company info header
{userCompanies && userCompanies.length > 1 && (
  <Button icon="swap-horizontal" onPress={handleSwitchCompany}>
    Switch
  </Button>
)}

// Company selection dialog
<Dialog visible={companySwitchDialogVisible}>
  {userCompanies.map(company => (
    <Card 
      key={company.id}
      onPress={() => handleCompanySelect(company)}
    >
      <Text>{company.name}</Text>
      {company.user_role.is_admin && <Text>Admin</Text>}
      {company.id === currentCompany.id && <Text>Current</Text>}
    </Card>
  ))}
</Dialog>
```

## Navigation Flow

### App Navigator Logic
```javascript
const AppNavigator = () => {
  const { isLoading, isAuthenticated, needsCompanySelection } = useAuth();

  return (
    <NavigationContainer>
      {!isAuthenticated ? (
        <AuthStack />
      ) : needsCompanySelection ? (
        <CompanySelectionScreen />
      ) : (
        <MainTabs />
      )}
    </NavigationContainer>
  );
};
```

### Flow States
1. **Not Authenticated** → Login/Register screens
2. **Authenticated + Needs Company Selection** → Company selection screen
3. **Authenticated + Company Selected** → Main app tabs

## User Experience Scenarios

### Scenario 1: Single Company User
```
User Login → Auto-select company → Main app
```

### Scenario 2: Multi-Company User
```
User Login → Company selection screen → Select company → Main app
```

### Scenario 3: Company Switching
```
Profile screen → Switch button → Company dialog → Select → Main app updates
```

### Scenario 4: No Companies
```
User Login → Error message → Contact administrator
```

## Role Management

### Per-Company Roles
```javascript
// User can have different roles in different companies
const userCompanies = [
  {
    id: 1,
    name: "Care Home A",
    user_role: {
      is_admin: true,
      permissions: ['all'],
      joined_at: "2024-01-01"
    }
  },
  {
    id: 2,
    name: "Care Home B", 
    user_role: {
      is_admin: false,
      permissions: ['view_benchmarks'],
      joined_at: "2024-02-01"
    }
  }
];
```

### Dynamic Role Updates
```javascript
// When switching companies, user role updates automatically
const selectCompany = async (company) => {
  const userWithRole = {
    ...user,
    is_admin: company.user_role.is_admin,
    permissions: company.user_role.permissions,
  };
  
  dispatch({ type: 'SELECT_COMPANY', payload: { company } });
  // UI automatically updates based on new role
};
```

## Data Isolation

### Company-Specific Data Access
- **Benchmarks**: Filtered by selected company
- **Users**: Only users from selected company visible in management
- **Spending Data**: Company-specific spending records
- **Reports**: Company-specific analytics

### Security Considerations
- Users can only access data from companies they belong to
- Role-based permissions enforced per company
- No cross-company data leakage
- Secure company switching with proper validation

## Migration Strategy

### Existing Users
For existing users with the old single-company system:
1. **Automatic Migration**: Create user_companies entries for existing user-company relationships
2. **Backward Compatibility**: Old company_id field maintained during transition
3. **Gradual Migration**: New features use new system, old features still work

### Database Migration
```javascript
// Migration script (run once)
const migrateExistingUsers = async () => {
  const users = await DatabaseService.getAllUsers();
  
  for (const user of users) {
    if (user.company_id) {
      await DatabaseService.addUserToCompany(user.id, user.company_id, {
        isAdmin: user.is_admin,
        permissions: user.permissions || [],
        isActive: user.is_active
      });
    }
  }
};
```

## Benefits

### 1. **Scalability**
- Support for enterprise users managing multiple facilities
- Flexible role management per company
- Easy addition of new companies

### 2. **User Experience**
- Single login for multiple companies
- Quick company switching
- Clear role indicators
- Intuitive company selection

### 3. **Security**
- Proper data isolation
- Role-based access control
- Secure company switching
- No unauthorized data access

### 4. **Administrative Control**
- Granular permission management
- Per-company user roles
- Easy user-company relationship management
- Audit trail of company access

## Testing Scenarios

### Test Case 1: Single Company User
1. **Login** with user having one company
2. **Expected**: Auto-login to company, no selection screen
3. **Verify**: Main app loads with correct company data

### Test Case 2: Multi-Company User
1. **Login** with user having multiple companies
2. **Expected**: Company selection screen appears
3. **Select company** and verify main app loads
4. **Switch companies** from profile and verify data updates

### Test Case 3: Company Switching
1. **Login** as multi-company user
2. **Navigate** to profile screen
3. **Click "Switch"** button
4. **Select different company**
5. **Verify**: App updates with new company data and role

### Test Case 4: Role Verification
1. **Login** as user with admin role in Company A, user role in Company B
2. **Select Company A**: Verify admin features visible
3. **Switch to Company B**: Verify admin features hidden
4. **Switch back to Company A**: Verify admin features return

The multi-company system provides a comprehensive solution for users who need to manage multiple care facilities while maintaining proper data isolation and role-based access control.
