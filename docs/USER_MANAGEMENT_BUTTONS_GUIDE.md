# User Management Buttons Guide

## Button Icons and Functions Explained

### Action Buttons for Each User

Each user in the list has 5 action buttons on the right side:

#### 1. ✏️ **Edit Profile** (Blue)
- **Icon**: `account-edit`
- **Color**: Blue (#2196F3)
- **Function**: Edit user's name and email address
- **Action**: Opens dialog to modify user profile

#### 2. 🔑 **Reset Password** (Orange)
- **Icon**: `key`
- **Color**: Orange (#FF9800)
- **Function**: Reset user's password
- **Action**: Opens dialog to set new password

#### 3. 👤+ **Admin Toggle** (Green/Red)
- **Icon**: `account-plus` (green) or `account-minus` (red)
- **Color**: 
  - Green (#4CAF50) = Make Admin
  - Red (#f44336) = Remove Admin
- **Function**: Grant or revoke admin privileges
- **Action**: Shows confirmation dialog

#### 4. 👤✓ **Status Toggle** (Green/Orange)
- **Icon**: `account-check` (green) or `account-off` (orange)
- **Color**:
  - Green (#4CAF50) = Activate user
  - Orange (#FF5722) = Deactivate user
- **Function**: Activate or deactivate user account
- **Action**: Shows confirmation dialog

#### 5. 🗑️ **Delete User** (Red)
- **Icon**: `delete`
- **Color**: Red (#f44336)
- **Function**: Permanently delete user
- **Action**: Shows confirmation dialog

## Button Behavior Explanation

### Green Button with Plus (👤+)
**This is the "Make Admin" button**
- Appears when user is NOT an admin
- Green color indicates positive action
- Plus icon means "add admin privileges"
- Click to promote user to admin

### Red Button with Minus (👤-)
**This is the "Remove Admin" button**
- Appears when user IS an admin
- Red color indicates removal action
- Minus icon means "remove admin privileges"
- Click to demote admin to regular user

### Status Toggle Button
- **Green with Check (👤✓)**: Activate inactive user
- **Orange with Off (👤✗)**: Deactivate active user

## Troubleshooting Button Issues

### If Delete Button Not Working:

1. **Check Console Logs**:
   - Look for: "Delete button clicked for user: [name]"
   - Look for: "Deleting user with ID: [id]"

2. **Common Issues**:
   - Trying to delete your own account (blocked)
   - Database connection issues
   - User ID not found

3. **Debug Steps**:
   - Check if confirmation dialog appears
   - Check console for error messages
   - Verify user exists in database

### If Status Toggle Not Working:

1. **Check Console Logs**:
   - Look for: "Toggle status button clicked for user: [name]"
   - Look for: "Toggling user status for ID: [id]"

2. **Common Issues**:
   - Trying to change your own status (blocked)
   - Database update failure
   - Invalid user status

3. **Debug Steps**:
   - Check if confirmation dialog appears
   - Verify current user status
   - Check database connection

## Button States and Conditions

### When Buttons Are Hidden:
- **Bulk Mode**: All action buttons hidden, checkboxes shown
- **Own Account**: No action buttons for your own user account
- **Loading**: Buttons may be disabled during operations

### Button Permissions:
- **Self-Protection**: Cannot delete, deactivate, or change admin status of your own account
- **Admin Only**: Only admins can see and use these buttons
- **Confirmation Required**: All destructive actions require confirmation

## Visual Button Layout

```
User Card:
┌─────────────────────────────────────────────────────┐
│ John Doe                                    ✏️🔑👤✓🗑️ │
│ <EMAIL>                                    │
│ Last login: 12/17/24 2:30PM                        │
│ [Admin] [Inactive]                                  │
└─────────────────────────────────────────────────────┘
```

## Expected Behavior Flow

### Delete User:
1. Click 🗑️ button
2. See confirmation dialog
3. Click "Delete" to confirm
4. User removed from list
5. Success message shown

### Toggle Status:
1. Click 👤✓ or 👤✗ button
2. See confirmation dialog
3. Click "Confirm"
4. User status updated
5. Button icon changes
6. Success message shown

## Testing the Buttons

### Test Delete Function:
1. Create sample users first
2. Try deleting a test user (not yourself)
3. Confirm in dialog
4. Verify user disappears from list

### Test Status Toggle:
1. Find an active user
2. Click deactivate button (👤✗)
3. Confirm action
4. Verify "Inactive" chip appears
5. Button should change to activate (👤✓)

## Error Messages

### Common Error Messages:
- "You cannot delete your own account"
- "You cannot change your own account status"
- "Failed to delete user: [error details]"
- "Failed to update user status: [error details]"

### If Buttons Don't Respond:
1. Check if you're in bulk mode (exit bulk mode)
2. Verify you're logged in as admin
3. Check console for JavaScript errors
4. Try refreshing the page/app
5. Verify sample users exist to test with
