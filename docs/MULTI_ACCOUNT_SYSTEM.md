# Multi-Account System - Complete Implementation

## Overview
Implemented a comprehensive multi-account system where users can have multiple user accounts with the same email address, each potentially belonging to different companies or having different roles.

## Problem Solved
**Original Request**: "If one email id is registered with multiple accounts then where is the button for switch between these multiple accounts?"

**Solution**: Created a complete multi-account system that allows:
- **Multiple user accounts** with the same email address
- **Account selection** during login when multiple accounts exist
- **Account switching** from the profile screen
- **Different roles/companies** per account

## Key Differences: Multi-Company vs Multi-Account

### **Multi-Company System (Previous)**
- **One user account** belongs to **multiple companies**
- **Same user identity** across all companies
- **Switch between companies** from profile
- **Different roles per company**

### **Multi-Account System (New)**
- **Multiple user accounts** with **same email**
- **Different user identities** for each account
- **Switch between accounts** from profile
- **Each account can have different companies**

## Database Schema Changes

### Modified Users Table
```sql
-- Removed UNIQUE constraint on email
CREATE TABLE IF NOT EXISTS users (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  email TEXT NOT NULL,  -- No longer UNIQUE
  password TEXT NOT NULL,
  name TEXT NOT NULL,
  is_admin INTEGER DEFAULT 0,
  company_id INTEGER,
  permissions TEXT DEFAULT '[]',
  is_active INTEGER DEFAULT 1,
  last_login DATETIME,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  account_name TEXT,  -- Optional account identifier
  FOREIGN KEY (company_id) REFERENCES companies (id)
);
```

### New Database Methods
```javascript
// Get all accounts for an email
async getUserAccountsByEmail(email) {
  const users = await this.db.getAllAsync(
    'SELECT * FROM users WHERE email = ? ORDER BY last_login DESC',
    [email]
  );
  return users.map(user => ({
    ...user,
    permissions: JSON.parse(user.permissions || '[]'),
    is_admin: Boolean(user.is_admin),
    is_active: Boolean(user.is_active)
  }));
}
```

## User Interface Components

### 1. **Account Selection Screen**
**Location**: `src/screens/AccountSelectionScreen.js`

**Features**:
- **Professional account cards** with user details
- **Role indicators** (Admin/User badges)
- **Last login information**
- **Active/Inactive status**
- **One-click account selection**

### 2. **Account Switcher in Profile**
**Location**: `src/screens/ProfileScreen.js`

**Features**:
- **"Switch Account" button** in Account Information section
- **Modal dialog** with available accounts
- **Account details** and role information
- **Logout and re-login** flow for account switching

### 3. **Enhanced Navigation Flow**
**Location**: `src/navigation/AppNavigator.js`

**Features**:
- **Account selection** before company selection
- **Proper flow hierarchy**: Login → Account Selection → Company Selection → Main App

## Authentication Flow

### Login Process with Multiple Accounts
```javascript
const login = async (email, password) => {
  // 1. Get all accounts for email
  const userAccounts = await DatabaseService.getUserAccountsByEmail(email);
  
  // 2. Find accounts with matching password
  const validAccounts = userAccounts.filter(account => 
    account.password === password && account.is_active
  );
  
  // 3. Handle account selection
  if (validAccounts.length === 0) {
    throw new Error('Invalid email or password');
  } else if (validAccounts.length === 1) {
    // Single account - proceed with company selection
    proceedWithSingleAccount(validAccounts[0]);
  } else {
    // Multiple accounts - show account selection
    showAccountSelection(validAccounts);
  }
};
```

### Account Selection Logic
```javascript
// AuthContext state management
const initialState = {
  user: null,
  company: null,
  userCompanies: [],
  userAccounts: [],        // New: Available accounts
  needsAccountSelection: false,  // New: Account selection flag
  needsCompanySelection: false,
  // ... other states
};
```

## User Experience Flow

### Flow 1: Single Account User
```
Login → Single account found → Company selection (if multiple) → Main app
```

### Flow 2: Multi-Account User
```
Login → Multiple accounts found → Account selection → Company selection → Main app
```

### Flow 3: Account Switching
```
Profile → "Switch Account" → Account dialog → Select account → Logout → Login flow
```

## Registration Changes

### Enhanced Registration Validation
```javascript
// Allow multiple accounts per email, but prevent duplicate company registrations
const existingAccounts = await DatabaseService.getUserAccountsByEmail(email);

if (companyData && companyData.registration) {
  for (const account of existingAccounts) {
    const accountCompany = await DatabaseService.getCompanyById(account.company_id);
    if (accountCompany && accountCompany.registration === companyData.registration) {
      throw new Error('You already have an account for a company with this registration number.');
    }
  }
}
```

### Registration Scenarios
- **New email**: Create first account
- **Existing email + new company**: Create additional account
- **Existing email + existing company**: Prevent duplicate, suggest invitation system

## User Interface Examples

### Account Selection Screen
```javascript
// Professional account cards
{userAccounts.map((account) => (
  <Card key={account.id} style={styles.accountCard}>
    <Card.Content>
      <Text variant="titleMedium">{account.name}</Text>
      <Text variant="bodySmall">{account.email}</Text>
      <Text variant="bodySmall">
        {account.is_admin ? 'Administrator' : 'User'} • 
        Last login: {new Date(account.last_login).toLocaleDateString()}
      </Text>
      
      <View style={styles.accountBadges}>
        {account.is_admin && (
          <Chip icon="shield-account">Admin</Chip>
        )}
        <Chip icon={account.is_active ? "check-circle" : "pause-circle"}>
          {account.is_active ? "Active" : "Inactive"}
        </Chip>
      </View>
      
      <Button onPress={() => handleAccountSelect(account)}>
        Select Account
      </Button>
    </Card.Content>
  </Card>
))}
```

### Account Switcher in Profile
```javascript
// Account Information section with switch button
<View style={styles.sectionHeader}>
  <Text variant="titleLarge">Account Information</Text>
  <Button
    mode="text"
    compact
    onPress={handleSwitchAccount}
    icon="account-switch"
  >
    Switch Account
  </Button>
</View>

// Account switch dialog
<Dialog visible={accountSwitchDialogVisible}>
  <Dialog.Title>Switch Account</Dialog.Title>
  <Dialog.Content>
    {userAccounts.map((account) => (
      <Card key={account.id} onPress={() => handleAccountSelect(account)}>
        <Text>{account.name}</Text>
        <Text>{account.is_admin ? 'Admin' : 'User'}</Text>
      </Card>
    ))}
  </Dialog.Content>
</Dialog>
```

## Navigation Flow

### App Navigator Logic
```javascript
const AppNavigator = () => {
  const { isLoading, isAuthenticated, needsAccountSelection, needsCompanySelection } = useAuth();

  return (
    <NavigationContainer>
      {!isAuthenticated ? (
        <AuthStack />
      ) : needsAccountSelection ? (
        <AccountSelectionScreen />
      ) : needsCompanySelection ? (
        <CompanySelectionScreen />
      ) : (
        <MainTabs />
      )}
    </NavigationContainer>
  );
};
```

### Flow Hierarchy
1. **Authentication** (Login/Register)
2. **Account Selection** (if multiple accounts)
3. **Company Selection** (if multiple companies)
4. **Main Application**

## Account Management

### Account Switching Process
```javascript
const handleSwitchAccount = async () => {
  // 1. Load other accounts for same email
  const accounts = await DatabaseService.getUserAccountsByEmail(user.email);
  const otherAccounts = accounts.filter(acc => acc.is_active && acc.id !== user.id);
  
  // 2. Show account selection dialog
  if (otherAccounts.length > 0) {
    setUserAccounts(otherAccounts);
    setAccountSwitchDialogVisible(true);
  } else {
    Alert.alert('No Other Accounts', 'You don\'t have any other active accounts.');
  }
};

const handleAccountSelect = async (selectedAccount) => {
  // 3. Logout and trigger re-login flow
  await logout();
  // User will need to login again, which will show account selection
};
```

## Security Considerations

### 1. **Password Validation**
- **Same password** required for all accounts with same email
- **Account-specific validation** during login
- **Secure account switching** through logout/login flow

### 2. **Access Control**
- **Account isolation** - each account has separate permissions
- **Company-specific access** per account
- **Role-based permissions** per account

### 3. **Data Integrity**
- **Prevent duplicate company registrations** per email
- **Maintain account relationships** properly
- **Clean account switching** without data leakage

## Benefits

### 1. **Flexible User Management**
- **Multiple professional identities** per email
- **Different roles** in different organizations
- **Separate account contexts**

### 2. **Enterprise Support**
- **Consultants/contractors** can have accounts with multiple clients
- **Multi-organization users** properly supported
- **Professional account separation**

### 3. **User Experience**
- **Clear account selection** when multiple exist
- **Easy account switching** from profile
- **Professional interface** for account management

### 4. **Backward Compatibility**
- **Single account users** experience no change
- **Existing functionality** preserved
- **Gradual adoption** of multi-account features

## Testing Scenarios

### Test Case 1: Single Account Login
1. **User has one account** with email
2. **Login proceeds** directly to company selection
3. **No account selection** screen shown

### Test Case 2: Multi-Account Login
1. **User has multiple accounts** with same email/password
2. **Account selection screen** appears after login
3. **User selects account** and proceeds to company selection

### Test Case 3: Account Switching
1. **User in Profile screen** clicks "Switch Account"
2. **Account dialog** shows other available accounts
3. **User selects different account** and is logged out
4. **Re-login** shows account selection if multiple accounts

### Test Case 4: Registration with Existing Email
1. **User registers** with existing email but new company
2. **System allows** account creation
3. **Prevents duplicate** company registrations
4. **Login shows** account selection for multiple accounts

The multi-account system provides comprehensive support for users who need multiple professional identities while maintaining security, data integrity, and a professional user experience.
