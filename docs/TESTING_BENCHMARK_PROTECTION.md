# Testing Benchmark Update Protection

## Test Scenario
This document outlines how to test that benchmark updates don't affect historical data.

## Test Steps

### 1. Initial Setup
1. Navigate to a benchmark detail screen
2. Note the current benchmark amount (e.g., $25.50/day)

### 2. Record Historical Data
1. Add an actual spend entry:
   - Amount: $30.00
   - Date: Today
   - Expected variance: +$4.50 (over benchmark)
2. Verify the entry appears in the historical data table
3. Note the benchmark amount shown: $25.50
4. Note the variance: +$4.50

### 3. Update Benchmark Amount
1. Click the edit button (three dots menu)
2. Change the benchmark amount to $28.00
3. Save the changes
4. Verify the benchmark header now shows $28.00/day

### 4. Verify Historical Data Protection
1. Check the historical data table
2. **Expected Results**:
   - Historical entry should still show benchmark: $25.50 (NOT $28.00)
   - Actual amount: $30.00 (unchanged)
   - Variance: +$4.50 (unchanged and still correct)

### 5. Test New Entries
1. Add a new actual spend entry:
   - Amount: $32.00
   - Date: Tomorrow
   - Expected variance: +$4.00 (against new $28.00 benchmark)
2. Verify new entry uses updated benchmark amount

## Expected Behavior

### ✅ Correct Behavior
- **Historical entries**: Show original benchmark amount used for calculation
- **Historical variance**: Remains accurate and unchanged
- **New entries**: Use current benchmark amount
- **Data integrity**: No historical data is modified

### ❌ Incorrect Behavior (Fixed)
- Historical entries showing current benchmark amount
- Variance appearing incorrect due to mismatched amounts
- Historical data being recalculated

## Technical Verification

### Database Check (Mobile)
```sql
SELECT 
  amount as actual_amount,
  benchmark_amount_at_time,
  variance,
  period_start
FROM actual_spends 
WHERE benchmark_id = [benchmark_id]
ORDER BY period_start DESC;
```

### Mock Data Check (Web)
Check browser console for:
```javascript
// Should show benchmark_amount_at_time field
console.log(mockData.actualSpends);
```

## Success Criteria
✅ Historical variance calculations remain accurate after benchmark updates
✅ Users can confidently update benchmarks without losing data integrity
✅ Audit trail preserved showing what benchmark was used for each entry
✅ New entries use current benchmark amounts correctly
