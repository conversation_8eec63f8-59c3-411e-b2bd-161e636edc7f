# Registration Number Validation Testing Guide

## How to Test Registration Number Uniqueness

### Test Scenario 1: Company Creation with Duplicate Registration

#### Setup
1. **Current demo company** has registration: "REG123456"
2. **Try to create new company** with same registration

#### Steps to Test
1. **Go to RegisterScreen** (if you can access it)
2. **Fill in user details** (name, email, password)
3. **Fill in company details**:
   - Company Name: "Test Care Home"
   - **Registration: "REG123456"** (same as demo company)
   - Capacity: 25
   - Capacity Type: Beds
4. **Click "Create Account"**

#### Expected Result
- **Error message**: "A company with this registration number already exists"
- **Account creation fails**
- **User stays on registration screen**

### Test Scenario 2: Company Update with Duplicate Registration

#### Setup
1. **Login as admin user**
2. **Go to Profile screen**
3. **Current company** has registration: "REG123456"

#### Steps to Test
1. **Click "Edit" button** next to Company Information
2. **Change registration number** to a duplicate (if you know another exists)
3. **Or manually test** by temporarily changing current registration, then trying to change it back to a duplicate
4. **Click "Save"**

#### Expected Result
- **Error message**: "A company with this registration number already exists"
- **Update fails**
- **Original registration preserved**

### Test Scenario 3: Case Sensitivity Test

#### Steps to Test
1. **Try to create/update** with registration: "reg123456" (lowercase)
2. **When demo company** has: "REG123456" (uppercase)

#### Expected Result
- **Error message**: "A company with this registration number already exists"
- **Case-insensitive validation** working correctly

### Test Scenario 4: Self-Update Test (Should Work)

#### Steps to Test
1. **Login as admin**
2. **Go to Profile → Edit Company**
3. **Keep same registration number**: "REG123456"
4. **Change other details** (name, capacity, etc.)
5. **Click "Save"**

#### Expected Result
- **Update succeeds**
- **No error message**
- **Company keeps its own registration number**

### Test Scenario 5: Empty Registration Test (Should Work)

#### Steps to Test
1. **Create/update company** with **empty registration field**
2. **Leave registration blank**
3. **Fill other required fields**
4. **Save/Create**

#### Expected Result
- **Operation succeeds**
- **Empty registrations allowed**
- **No validation error**

## Console Debugging

### Check Console Logs
Open browser console (F12) and look for:

```javascript
// Successful validation
"Registration number validation passed"

// Duplicate found
"Registration number already exists: REG123456"

// Database check
"Checking registration number: REG123456"
```

### Database State Verification

#### Web Platform (MockData)
```javascript
// In browser console
console.log(DatabaseService.mockData.companies);
// Should show all companies with their registration numbers
```

#### Check Current Demo Company
```javascript
// Current demo company registration
console.log("Demo company registration:", company?.registration);
```

## Manual Testing Steps

### Quick Test Method
1. **Login as admin**
2. **Go to Profile screen**
3. **Click "Edit" next to Company Information**
4. **Try changing registration to "REG123456"** (if it's not already that)
5. **If it is already "REG123456", try "TEST123"**
6. **Save and see if it works**
7. **Then try changing to "REG123456" again**
8. **Should get duplicate error**

### Verification Points
- ✅ **Error message appears** for duplicates
- ✅ **Case-insensitive validation** works
- ✅ **Self-updates allowed** (same registration)
- ✅ **Empty registrations allowed**
- ✅ **Clear user feedback** provided

## Expected Error Messages

### Registration Creation Error
```
Alert Title: "Registration Failed"
Alert Message: "A company with this registration number already exists"
```

### Company Update Error
```
Alert Title: "Error"
Alert Message: "A company with this registration number already exists"
```

## Troubleshooting

### If Validation Doesn't Work
1. **Check console** for JavaScript errors
2. **Verify database connection**
3. **Check if demo data loaded** properly
4. **Ensure registration field** has value

### If No Error Appears
1. **Registration might be empty** (allowed)
2. **Registration might be unique** (working correctly)
3. **Check exact spelling** and case
4. **Verify demo company** has registration set

## Success Indicators

### Validation Working Correctly
- ✅ **Duplicate registrations rejected**
- ✅ **Clear error messages shown**
- ✅ **Case-insensitive comparison**
- ✅ **Self-updates allowed**
- ✅ **Empty registrations allowed**

### System Behavior
- ✅ **User stays on form** after error
- ✅ **Form data preserved** after error
- ✅ **No partial saves** occur
- ✅ **Database integrity maintained**

The validation system ensures that no two companies can have the same registration number while providing clear feedback to users and maintaining system integrity.
