# Clerk Integration Troubleshooting Guide

## Current Issue: "Sign up failed reason unknown"

### Checklist for Clerk Configuration

#### 1. ✅ Clerk Dashboard Configuration
- [ ] **Enable Native API**: Go to Clerk Dashboard → Native Applications → Enable Native API
- [ ] **Verify API Keys**: Check that publishable key is correct in API Keys section
- [ ] **Check Application Settings**: Ensure email/password authentication is enabled
- [ ] **Verify Domain Settings**: Make sure localhost and your domains are configured

#### 2. ✅ Environment Configuration
- [x] **Environment Variable**: `EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY` is set in `.env`
- [x] **Package Installation**: `@clerk/clerk-expo` is installed
- [x] **Token Cache**: `expo-secure-store` is installed and configured
- [x] **ClerkProvider**: Properly configured with publishable key and token cache

#### 3. ✅ Code Configuration
- [x] **ClerkProvider Wrapper**: App is wrapped with Clerk<PERSON>rovider
- [x] **Hooks Usage**: Using correct hooks (`useSignUp`, `useAuth`, etc.)
- [x] **Error Handling**: Proper error logging and handling

### Debugging Steps

#### Step 1: Test Clerk Connection
1. Navigate to Landing Page
2. Click "Test Clerk" button
3. Click "Test Clerk Connection"
4. Check console logs and alert message

#### Step 2: Check Environment Variables
```javascript
console.log('Clerk Key:', process.env.EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY);
```

#### Step 3: Test Basic Sign Up
1. Use the test screen with simple credentials
2. Check console logs for detailed error messages
3. Verify network requests in developer tools

### Common Issues and Solutions

#### Issue 1: "Clerk is not loaded"
**Cause**: ClerkProvider not properly configured or environment variable missing
**Solution**: 
- Verify `.env` file is in root directory
- Restart development server after adding environment variables
- Check ClerkProvider is at the root of your app

#### Issue 2: "Invalid publishable key"
**Cause**: Wrong API key or key not properly loaded
**Solution**:
- Copy key again from Clerk Dashboard → API Keys
- Ensure key starts with `pk_test_` or `pk_live_`
- Check for extra spaces or characters

#### Issue 3: "Native API not enabled"
**Cause**: Native applications not enabled in Clerk Dashboard
**Solution**:
- Go to Clerk Dashboard
- Navigate to "Native Applications" 
- Enable the Native API

#### Issue 4: "Network request failed"
**Cause**: Network connectivity or CORS issues
**Solution**:
- Check internet connection
- Verify Clerk service status
- Check if using correct Clerk instance

### Required Clerk Dashboard Settings

1. **Authentication Methods**:
   - Email/Password: ✅ Enabled
   - Email verification: ✅ Enabled

2. **Native Applications**:
   - Native API: ✅ Must be enabled
   - Allowed origins: Add your development URLs

3. **Session Settings**:
   - Session lifetime: Default (7 days)
   - Multi-session: Enabled if needed

### Testing Commands

```bash
# Restart development server
npm start

# Clear cache and restart
npx expo start --clear

# Check environment variables
npx expo config
```

### Debug Information to Collect

When reporting issues, include:

1. **Console Logs**: Full error messages from console
2. **Clerk Debug Info**: Output from ClerkDebug component
3. **Environment**: 
   - Expo SDK version
   - @clerk/clerk-expo version
   - Platform (iOS/Android/Web)
4. **Network**: Check network tab for failed requests
5. **Clerk Dashboard**: Screenshot of Native Applications settings

### Next Steps

1. **Test the basic connection** using the ClerkTest screen
2. **Check Clerk Dashboard** settings (especially Native API)
3. **Verify environment variables** are loaded correctly
4. **Check console logs** for detailed error messages
5. **Try with different email** (avoid common test emails)

### Contact Information

If issues persist:
- Check Clerk documentation: https://clerk.com/docs/quickstarts/expo
- Clerk Discord: https://clerk.com/discord
- GitHub Issues: https://github.com/clerk/javascript

### Files Modified for Debugging

Debug components have been removed from production code. For debugging, you can:
- Check console logs for detailed error messages
- Use browser developer tools for network inspection
- Add temporary console.log statements as needed
