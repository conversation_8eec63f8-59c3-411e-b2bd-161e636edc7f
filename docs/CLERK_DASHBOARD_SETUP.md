# Clerk Dashboard Configuration Guide

## 🚨 **CRITICAL: Required Dashboard Settings**

Based on the official Clerk Expo documentation, these settings are **REQUIRED** for the integration to work:

### **1. Enable Native API (CRITICAL)**

This is the most common cause of "sign up failed" errors in Expo apps.

**Steps:**
1. Go to [Clerk Dashboard](https://dashboard.clerk.com)
2. Select your application
3. Navigate to **"Configure" → "Native Applications"**
4. **Enable "Native API"** toggle
5. Click **"Save"**

**❌ Without this setting, ALL sign-up attempts will fail!**

### **2. Authentication Methods**

**Steps:**
1. Go to **"User & Authentication" → "Email, Phone, Username"**
2. Ensure **"Email address"** is enabled
3. Go to **"User & Authentication" → "Authentication Strategies"**
4. Ensure **"Password"** is enabled
5. Click **"Save"**

### **3. Email Verification Settings**

**Steps:**
1. Go to **"User & Authentication" → "Email, Phone, Username"**
2. Under **"Email address"** section:
   - ✅ Enable "Email address"
   - ✅ Enable "Require verification"
   - ✅ Set verification strategy to "Email code"
3. Click **"Save"**

### **4. API Keys**

**Steps:**
1. Go to **"Configure" → "API Keys"**
2. Copy the **"Publishable key"** (starts with `pk_test_` or `pk_live_`)
3. Paste it in your `.env` file as `EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY`

**Current Key Check:**
```
EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_cHJvbXB0LW9zcHJleS0zNi5jbGVyay5hY2NvdW50cy5kZXYk
```

### **5. Session Settings**

**Steps:**
1. Go to **"Configure" → "Sessions"**
2. Recommended settings:
   - **Session lifetime**: 7 days (default)
   - **Inactivity timeout**: 30 minutes
   - **Multi-session handling**: Single session per user
3. Click **"Save"**

### **6. Development Settings**

**Steps:**
1. Go to **"Configure" → "Domains"**
2. Add development domains:
   - `localhost:8081` (Expo default)
   - `localhost:19006` (Expo web)
   - Your computer's IP address (for mobile testing)
3. Click **"Save"**

## 🔧 **Configuration Verification Checklist**

### **Dashboard Settings:**
- [ ] ✅ Native API is **ENABLED** (most critical)
- [ ] ✅ Email address authentication is enabled
- [ ] ✅ Password authentication is enabled
- [ ] ✅ Email verification is enabled with "Email code" strategy
- [ ] ✅ Publishable key is copied correctly
- [ ] ✅ Development domains are added

### **Code Configuration:**
- [ ] ✅ `@clerk/clerk-expo` package installed
- [ ] ✅ `expo-secure-store` package installed
- [ ] ✅ Environment variable `EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY` set
- [ ] ✅ ClerkProvider configured with publishable key and token cache
- [ ] ✅ Development server restarted after adding environment variables

## 🧪 **Testing Steps**

### **Step 1: Basic Connection Test**
1. Open your app
2. Go to Landing page
3. Click **"Official Test"** (green button)
4. Check if "Clerk Loaded: Yes" appears
5. If "No", check environment variables and restart server

### **Step 2: Sign-Up Test**
1. In the Official Test screen
2. Enter a **real email address** (not <EMAIL>)
3. Enter a password (minimum 8 characters)
4. Click **"Continue"**
5. Check console logs for detailed error messages

### **Step 3: Email Verification Test**
1. If sign-up succeeds, check your email
2. Enter the 6-digit verification code
3. Click **"Verify"**
4. Should show success message

## 🚨 **Common Error Messages & Solutions**

### **"Sign up failed reason unknown"**
**Cause**: Native API not enabled in dashboard
**Solution**: Enable Native API in Clerk Dashboard → Native Applications

### **"Invalid publishable key"**
**Cause**: Wrong API key or environment variable not loaded
**Solution**: 
- Copy key again from dashboard
- Restart development server
- Check `.env` file is in root directory

### **"Network request failed"**
**Cause**: Connectivity or CORS issues
**Solution**:
- Check internet connection
- Verify Clerk service status
- Add development domains to dashboard

### **"Email address is not valid"**
**Cause**: Email validation failed
**Solution**: Use a real email address format

### **"Password is too weak"**
**Cause**: Password doesn't meet requirements
**Solution**: Use minimum 8 characters with mixed case/numbers

## 📞 **Support Resources**

- **Clerk Documentation**: https://clerk.com/docs/quickstarts/expo
- **Clerk Discord**: https://clerk.com/discord
- **GitHub Issues**: https://github.com/clerk/javascript
- **Status Page**: https://status.clerk.com

## 🔄 **After Configuration Changes**

Always restart your development server after:
- Changing environment variables
- Modifying Clerk dashboard settings
- Installing new packages

```bash
# Stop current server (Ctrl+C)
# Then restart:
npm start
# or
npx expo start --clear
```

## 📝 **Next Steps**

1. **Complete all dashboard settings** above
2. **Test with Official Test screen** (green button)
3. **Check console logs** for detailed error messages
4. **Use real email address** for testing
5. **Report specific error messages** if issues persist

The most common issue is forgetting to enable the Native API in the Clerk Dashboard!
