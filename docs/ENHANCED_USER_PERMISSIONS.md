# Enhanced User Creation with Spending Permissions

## Overview
The user creation system has been enhanced to include detailed spending data permissions, allowing administrators to control what users can do with spending data and which benchmarks/budgets they can access.

## New Permission System

### 1. Data Access Levels

#### **View Only**
- **Description**: User can see spending data but cannot submit actual spending figures
- **Use Case**: Read-only access for managers, auditors, or stakeholders who need visibility but not input rights
- **Permission Structure**:
```javascript
{
  type: 'spending',
  action: 'view',
  scope: 'all'
}
```

#### **Can Submit Spending**
- **Description**: User can view and submit actual spending figures
- **Use Case**: Staff members responsible for entering actual spending data
- **Permission Structure**: Depends on scope selection

### 2. Spending Submission Scope

#### **All Benchmarks & Budgets**
- **Description**: User can submit spending for any benchmark or budget in the company
- **Use Case**: Finance managers, senior staff with broad responsibilities
- **Permission Structure**:
```javascript
{
  type: 'spending',
  action: 'submit',
  scope: 'all'
}
```

#### **Specific Benchmarks & Budgets**
- **Description**: User can only submit spending for selected benchmarks/budgets
- **Use Case**: Department-specific staff, specialized roles
- **Permission Structure**:
```javascript
{
  type: 'spending',
  action: 'submit',
  scope: 'specific',
  benchmarkIds: [1, 3, 5] // Array of allowed benchmark IDs
}
```

## User Creation Form Enhancements

### New Form Sections

#### **1. Basic Information** (Existing)
- Full Name
- Email Address
- Password
- Admin Status

#### **2. Spending Data Permissions** (New)
- **Data Access Level**
  - Radio button: "View Only"
  - Radio button: "Can Submit Spending"

#### **3. Spending Submission Scope** (New - shown only if "Can Submit" is selected)
- **Scope Selection**
  - Radio button: "All Benchmarks & Budgets"
  - Radio button: "Specific Benchmarks & Budgets"

#### **4. Benchmark Selection** (New - shown only if "Specific" scope is selected)
- Checkbox list of available benchmarks and budgets
- Shows benchmark name, type (benchmark/budget), and period
- Requires at least one selection

### Form Validation

#### **Enhanced Validation Rules**
1. **Existing validations** (name, email, password)
2. **Specific scope validation**: If "Specific" scope is selected, at least one benchmark must be chosen
3. **Email uniqueness**: Prevents duplicate email addresses

#### **Error Messages**
- "Please select at least one benchmark or budget for specific scope permissions"
- Clear, actionable feedback for permission-related errors

## Technical Implementation

### State Management
```javascript
// New state variables added
const [canSubmitSpending, setCanSubmitSpending] = useState(false);
const [spendingScope, setSpendingScope] = useState('all');
const [selectedBenchmarks, setSelectedBenchmarks] = useState([]);
const [availableBenchmarks, setAvailableBenchmarks] = useState([]);
```

### Data Loading
- **Benchmarks loaded on component mount**: `loadBenchmarks()` function
- **Real-time benchmark list**: Shows current company benchmarks and budgets
- **Dynamic UI updates**: Form sections appear/hide based on selections

### Permission Building Logic
```javascript
// Build permissions array based on user selections
const permissions = [];

if (canSubmitSpending) {
  if (spendingScope === 'all') {
    permissions.push({
      type: 'spending',
      action: 'submit',
      scope: 'all'
    });
  } else if (spendingScope === 'specific' && selectedBenchmarks.length > 0) {
    permissions.push({
      type: 'spending',
      action: 'submit',
      scope: 'specific',
      benchmarkIds: selectedBenchmarks
    });
  }
} else {
  permissions.push({
    type: 'spending',
    action: 'view',
    scope: 'all'
  });
}
```

## User Interface Design

### Professional Layout
- **Clear section headers**: "Spending Data Permissions"
- **Logical flow**: Basic info → Admin status → Spending permissions
- **Visual hierarchy**: Radio buttons, checkboxes, and helper text
- **Responsive design**: Works on all screen sizes

### User Experience Features
- **Progressive disclosure**: Advanced options appear only when needed
- **Clear labeling**: Descriptive text for each permission level
- **Visual feedback**: Selected options clearly highlighted
- **Form persistence**: Selections maintained during form interaction

## Permission Usage Examples

### Example 1: Finance Manager
```javascript
// Full access to all spending data
{
  type: 'spending',
  action: 'submit',
  scope: 'all'
}
```

### Example 2: Department Head
```javascript
// Can submit spending for specific department budgets
{
  type: 'spending',
  action: 'submit',
  scope: 'specific',
  benchmarkIds: [2, 4, 7] // Department-specific benchmarks
}
```

### Example 3: Auditor
```javascript
// Read-only access to all spending data
{
  type: 'spending',
  action: 'view',
  scope: 'all'
}
```

## Benefits

### 1. Granular Control
- **Precise permissions**: Control exactly what each user can access
- **Security**: Prevent unauthorized spending submissions
- **Compliance**: Meet audit requirements for data access control

### 2. Operational Efficiency
- **Role-based access**: Users see only what they need
- **Reduced errors**: Limited scope reduces chance of mistakes
- **Clear responsibilities**: Defined roles for spending data

### 3. Scalability
- **Flexible system**: Easily add new permission types
- **Department structure**: Support for departmental boundaries
- **Growth ready**: Handles increasing numbers of users and benchmarks

## Future Enhancements

### Potential Additions
1. **Time-based permissions**: Temporary access to specific benchmarks
2. **Approval workflows**: Multi-step approval for spending submissions
3. **Permission templates**: Pre-defined permission sets for common roles
4. **Audit logging**: Track permission changes and usage
5. **Bulk permission updates**: Change permissions for multiple users

### Integration Opportunities
1. **Department management**: Link permissions to organizational structure
2. **Project-based access**: Permissions tied to specific projects
3. **External integrations**: Sync with HR systems for role-based permissions

## Testing Scenarios

### Test Cases
1. **View-only user**: Cannot access spending submission forms
2. **All-access user**: Can submit spending for any benchmark
3. **Specific-access user**: Can only submit for assigned benchmarks
4. **Permission validation**: Form prevents invalid permission combinations
5. **Benchmark selection**: UI updates correctly based on scope selection

The enhanced user creation system provides comprehensive control over spending data access while maintaining a user-friendly interface for administrators.
