# User Invitation System - Multi-Company Solution

## Problem Solved
**Original Issue**: Users couldn't register with the same email for different companies, causing registration failures with the error "User with this email already exists."

**Solution**: Implemented a comprehensive invitation system that allows existing users to be invited to join additional companies while maintaining proper security and data isolation.

## How It Works

### 1. **Registration Flow (Unchanged)**
- **New users** can still register normally with unique email addresses
- **Duplicate email registration** is prevented with helpful error message:
  ```
  "An account with this email already exists. If you need access to a different company, please contact the administrator of that company to add you to their account."
  ```

### 2. **Invitation Flow (New)**
- **Admins can invite existing users** to join their company
- **Users receive invitations** and can accept them
- **Multi-company access** is granted through user-company relationships

## Database Schema

### Invitations Table
```sql
CREATE TABLE IF NOT EXISTS invitations (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  email TEXT NOT NULL,
  company_id INTEGER NOT NULL,
  invited_by INTEGER NOT NULL,
  permissions TEXT DEFAULT '[]',
  token TEXT UNIQUE NOT NULL,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'expired')),
  expires_at DATETIME NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (company_id) REFERENCES companies (id),
  FOREIGN KEY (invited_by) REFERENCES users (id)
);
```

### User-Company Relationships (Enhanced)
```sql
CREATE TABLE IF NOT EXISTS user_companies (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id INTEGER NOT NULL,
  company_id INTEGER NOT NULL,
  is_admin INTEGER DEFAULT 0,
  permissions TEXT DEFAULT '[]',
  is_active INTEGER DEFAULT 1,
  joined_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
  FOREIGN KEY (company_id) REFERENCES companies (id) ON DELETE CASCADE,
  UNIQUE(user_id, company_id)
);
```

## User Interface Components

### 1. **Invitation Management Screen**
**Location**: `src/screens/InvitationManagementScreen.js`

**Features**:
- **Send invitations** to existing users
- **View invitation history** with status tracking
- **Invitation validation** (user exists, not already in company)
- **Status indicators** (pending, accepted, expired)

**Access**: Admin Panel → User Management → "Invite Users" button

### 2. **Invitation Accept Screen**
**Location**: `src/screens/InvitationAcceptScreen.js`

**Features**:
- **Company details display** for invitation
- **Accept/decline options**
- **Login prompt** if user not authenticated
- **Error handling** for invalid/expired invitations

**Access**: Via invitation link with token parameter

### 3. **Enhanced User Management**
**Updated**: `src/screens/UserManagementScreen.js`

**New Features**:
- **"Invite Users" button** in controls section
- **Navigation to invitation management**
- **Integrated with existing user management**

## Invitation Process Flow

### Step 1: Admin Sends Invitation
```javascript
// Admin clicks "Invite Users" in User Management
// Enters email of existing user
// System validates:
// 1. User exists with that email
// 2. User is not already in the company
// 3. Creates invitation with 7-day expiry
```

### Step 2: Invitation Created
```javascript
const invitation = {
  email: "<EMAIL>",
  company_id: 123,
  invited_by: adminUserId,
  token: "unique-random-token",
  status: "pending",
  expires_at: "2024-01-15T00:00:00Z"
};
```

### Step 3: User Accepts Invitation
```javascript
// User visits invitation link
// System validates invitation (not expired, still pending)
// User must be logged in with matching email
// Creates user-company relationship
// User gains access to new company
```

## API Methods

### Database Service Methods
```javascript
// Create invitation
await DatabaseService.createInvitation({
  email: "<EMAIL>",
  companyId: 123,
  invitedBy: adminUserId,
  permissions: []
});

// Get invitation by token
const invitation = await DatabaseService.getInvitationByToken(token);

// Accept invitation
const result = await DatabaseService.acceptInvitation(token);

// Get company invitations
const invitations = await DatabaseService.getCompanyInvitations(companyId);
```

### User-Company Relationship Methods
```javascript
// Add user to company
await DatabaseService.addUserToCompany(userId, companyId, {
  isAdmin: false,
  permissions: [],
  isActive: true
});

// Get user's companies
const userCompanies = await DatabaseService.getUserCompanies(userId);

// Update user role in company
await DatabaseService.updateUserCompanyRole(userId, companyId, {
  isAdmin: true,
  permissions: ['admin_access']
});
```

## User Experience Scenarios

### Scenario 1: Successful Invitation
```
1. Admin (Company A) wants to add existing user to their company
2. Admin goes to User Management → "Invite Users"
3. Admin enters user's email address
4. System validates user exists and creates invitation
5. User receives invitation (via app notification/email in future)
6. User accepts invitation and gains access to Company A
7. User can now switch between their companies
```

### Scenario 2: User Tries to Register (Existing Email)
```
1. User tries to register with existing email for new company
2. System shows error: "An account with this email already exists..."
3. User contacts admin of the company they want to join
4. Admin sends invitation through the system
5. User accepts invitation and gains access
```

### Scenario 3: Invalid Invitation
```
1. User clicks on expired/invalid invitation link
2. System shows appropriate error message
3. User is directed to contact administrator
4. Admin can send new invitation if needed
```

## Security Features

### 1. **Invitation Validation**
- **Token-based security** with unique random tokens
- **Expiration handling** (7-day default expiry)
- **Status tracking** (pending, accepted, expired)
- **Email verification** (must match logged-in user)

### 2. **Permission Control**
- **Admin-only invitation creation**
- **Company isolation** (admins can only invite to their company)
- **Role-based access** (invited users get regular permissions by default)

### 3. **Data Integrity**
- **Duplicate prevention** (can't invite user already in company)
- **Atomic operations** (invitation acceptance is all-or-nothing)
- **Proper cleanup** (expired invitations marked as such)

## Error Handling

### Registration Errors
```javascript
// Enhanced error message for duplicate email
"An account with this email already exists. If you need access to a different company, please contact the administrator of that company to add you to their account."
```

### Invitation Errors
```javascript
// User not found
"No account found with this email address. The user must create an account first before they can be invited to join your company."

// User already in company
"This user is already a member of your company"

// Invalid invitation
"Invitation not found or invalid"

// Expired invitation
"This invitation has expired"
```

## Benefits

### 1. **Solves Original Problem**
- **No more registration failures** for existing users
- **Clear guidance** on how to join additional companies
- **Proper multi-company support**

### 2. **Enhanced Security**
- **Controlled access** through admin invitations
- **No unauthorized company joining**
- **Proper audit trail** of who invited whom

### 3. **Better User Experience**
- **Clear error messages** with actionable guidance
- **Professional invitation process**
- **Seamless multi-company switching**

### 4. **Administrative Control**
- **Admins control** who can join their company
- **Invitation tracking** and management
- **Flexible permission system**

## Future Enhancements

### 1. **Email Notifications**
- Send actual email invitations
- Email templates for invitations
- Reminder emails for pending invitations

### 2. **Bulk Invitations**
- Invite multiple users at once
- CSV import for bulk invitations
- Batch invitation management

### 3. **Advanced Permissions**
- Custom permission sets per invitation
- Role templates for common invitation types
- Permission inheritance from company defaults

### 4. **Invitation Analytics**
- Track invitation acceptance rates
- Monitor invitation usage patterns
- Generate invitation reports

## Testing Scenarios

### Test Case 1: Successful Invitation Flow
1. **Admin creates invitation** for existing user
2. **User accepts invitation** 
3. **User gains access** to new company
4. **User can switch** between companies

### Test Case 2: Registration with Existing Email
1. **User tries to register** with existing email
2. **System shows helpful error** message
3. **User contacts admin** for invitation
4. **Admin sends invitation** through system

### Test Case 3: Invalid Invitation Handling
1. **User clicks expired invitation** link
2. **System shows appropriate error**
3. **User is guided** to contact admin
4. **Admin can send new invitation**

The invitation system provides a comprehensive solution for multi-company user access while maintaining security, data integrity, and a professional user experience.
