# Benchmark vs Actual Comparison Chart

## Overview
Added a visual comparison chart showing benchmark vs actual spending trends over the last 12 weeks (3 months) with weekly granularity in the BenchmarkDetailScreen.

## Features Implemented

### 1. Interactive Line Chart
- **Library**: `react-native-chart-kit` with `react-native-svg`
- **Chart Type**: Line chart with bezier curves for smooth visualization
- **Data Period**: Last 12 weeks of actual spend data
- **X-Axis**: Weekly periods (e.g., "Jan W1", "Jan W2", "Feb W1")
- **Comparison**: Benchmark amounts vs actual spending amounts

### 2. Data Processing
- **Weekly Aggregation**: Groups actual spend entries by week
- **Week Calculation**: Uses ISO week numbering system
- **Average Calculation**: Calculates average actual spending per week
- **Historical Benchmark**: Uses `benchmark_amount_at_time` for accurate historical comparison
- **Missing Data Handling**: Shows weeks with no data as zero values

### 3. Visual Design
- **Dual Lines**: Blue line for benchmark, green line for actual spending
- **Legend**: Clear color-coded legend below the chart
- **Responsive**: Chart width adapts to screen size
- **Professional Styling**: Clean, modern appearance with proper spacing

### 4. Smart Display Logic
- **Loading State**: Shows spinner while preparing chart data
- **Empty State**: Displays helpful message when insufficient data
- **Data Validation**: Only shows chart when meaningful data exists

## Implementation Details

### Chart Configuration
```javascript
chartConfig={{
  backgroundColor: '#ffffff',
  backgroundGradientFrom: '#ffffff',
  backgroundGradientTo: '#ffffff',
  decimalPlaces: 0,
  color: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
  labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
  propsForDots: {
    r: '6',
    strokeWidth: '2',
    stroke: '#ffa726'
  }
}}
```

### Data Structure
```javascript
chartData = {
  labels: ['Nov W1', 'Nov W2', 'Nov W3', 'Nov W4', 'Dec W1', 'Dec W2', 'Dec W3', 'Dec W4', 'Jan W1', 'Jan W2', 'Jan W3', 'Jan W4'], // Last 12 weeks
  datasets: [
    {
      data: [25.00, 25.00, 25.00, 25.00, 28.00, 28.00, 28.00, 28.00, 30.00, 30.00, 30.00, 30.00], // Benchmark amounts
      color: (opacity = 1) => `rgba(33, 150, 243, ${opacity})`, // Blue
      strokeWidth: 3,
    },
    {
      data: [30.00, 22.00, 0, 32.00, 28.00, 0, 35.00, 25.00, 0, 33.00, 27.00, 0], // Actual spending
      color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`, // Green
      strokeWidth: 3,
    }
  ],
  legend: ['Benchmark', 'Actual']
}
```

### Weekly Data Processing
1. **Filter Recent Data**: Only includes entries from last 12 weeks
2. **Week Calculation**: Uses ISO week numbering system for accurate grouping
3. **Group by Week**: Aggregates entries by year-week key (YYYY-WW format)
4. **Calculate Averages**: Computes average actual spending per week
5. **Fill Missing Weeks**: Ensures all 12 weeks are represented
6. **Week Labels**: Generates user-friendly labels (e.g., "Jan W1", "Feb W3")
7. **Round Values**: Rounds to 2 decimal places for clean display

## User Experience

### Chart Placement
- Located between Performance Summary and Recent Activity table
- Provides visual context before detailed tabular data
- Easily scrollable within the main content area

### Visual Hierarchy
- **Title**: "12-Week Trend: Benchmark vs Actual"
- **Chart**: Prominent line chart with weekly data points
- **X-Axis**: Weekly labels for granular time analysis
- **Legend**: Color-coded legend for easy interpretation
- **Responsive**: Adapts to different screen sizes

### States Handled
1. **Loading**: Shows spinner with "Loading chart data..." message
2. **Data Available**: Displays interactive line chart with legend
3. **Insufficient Data**: Shows empty state with helpful guidance

## Benefits

### 📊 **Visual Insights**
- Quick identification of spending trends over time
- Easy comparison between planned vs actual spending
- Visual detection of patterns and anomalies
- Weekly granularity for detailed analysis

### 📈 **Performance Tracking**
- Week-over-week variance analysis
- Historical benchmark accuracy assessment
- Trend identification for budget planning
- Short-term pattern recognition

### 🎯 **User Engagement**
- Interactive visual element enhances user experience
- Complements tabular data with visual representation
- Encourages regular monitoring of spending patterns

## Technical Specifications

### Dependencies Added
```json
{
  "react-native-chart-kit": "^6.12.0",
  "react-native-svg": "^13.4.0"
}
```

### Performance Considerations
- Chart data is prepared asynchronously to avoid UI blocking
- Data is cached and only recalculated when benchmark or actual spends change
- Responsive chart sizing based on screen dimensions

### Cross-Platform Compatibility
- Works on both web and mobile platforms
- SVG-based rendering ensures crisp graphics on all screen densities
- Responsive design adapts to different screen sizes

## Usage Example

### With Data
```
12-Week Trend: Benchmark vs Actual

    35 ┤       ●     ●
       │      ╱ ╲   ╱ ╲
    30 ┤●    ╱   ╲ ╱   ╲  ●
       │ ╲  ╱     ╲╱     ╲╱
    25 ┤  ╲╱               ●
       │
    20 ┤
       └─────────────────────────────────────
       Nov W1  Nov W3  Dec W1  Dec W3  Jan W1  Jan W3

● Benchmark  ● Actual
```

### Without Sufficient Data
```
[Chart Icon]
Not enough data for weekly chart
Add more actual spend entries to see the 12-week trend
```

The chart provides valuable visual insights into spending patterns and helps users quickly understand their performance against benchmarks over time.
