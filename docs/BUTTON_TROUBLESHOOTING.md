# Button Troubleshooting Guide

## Issue: Delete and Status Toggle Buttons Not Working

### Debugging Steps Added

I've added comprehensive debugging to help identify the issue:

#### 1. Enhanced Debug Info Card
Now shows:
- Current user details
- Platform (iOS/Android/web)
- Database mode (Web/Mobile)
- User counts

#### 2. Console Logging
Added logs for:
- Button clicks: "Delete button clicked!" / "Status toggle button clicked!"
- Function entry: "Delete button clicked for user: [name]"
- Database operations: "Deleting user with ID: [id]"
- Status changes: "Toggling user status for ID: [id] to: [status]"

#### 3. Test Database Functions Button
New button to directly test database operations without UI

### Step-by-Step Troubleshooting

#### Step 1: Check Basic Setup
1. **Open User Management screen**
2. **Look at Debug Info card** (orange background)
3. **Verify**:
   - Is Admin: Yes
   - Users Found: > 1 (need test users)
   - Platform: web/ios/android
   - Database Mode: Web/Mobile

#### Step 2: Create Test Users
1. **Click "Create Sample Users"** button
2. **Wait for success message**
3. **Verify users appear** in the list below
4. **Check Debug Info** shows Users Found: 4 (you + 3 test users)

#### Step 3: Test Button Clicks
1. **Open browser console** (F12 → Console tab)
2. **Click delete button** on a test user
3. **Look for console message**: "Delete button clicked!"
4. **If no message**: Button click not registering
5. **If message appears**: Button click working, check next step

#### Step 4: Test Database Functions
1. **Click "Test DB Functions"** button
2. **Check console** for detailed test results
3. **Should see**:
   - "=== Testing Database Functions ==="
   - "All users: [array]"
   - "Test user found: [user object]"
   - "Testing toggleUserStatus..."
   - "Status toggled successfully"

#### Step 5: Check for Errors
**Common error patterns**:
- "Cannot read property 'id' of undefined" → User object issue
- "Database connection failed" → Database issue
- "Permission denied" → Admin rights issue
- No console messages → Button event not firing

### Possible Causes and Solutions

#### Cause 1: No Test Users
**Symptoms**: Only see your own user, no buttons to test
**Solution**: Click "Create Sample Users" first

#### Cause 2: Button Events Not Firing
**Symptoms**: No "Button clicked!" messages in console
**Solutions**:
- Check if in bulk mode (exit bulk mode)
- Refresh the page/app
- Check for JavaScript errors in console

#### Cause 3: Database Connection Issues
**Symptoms**: Button clicks work but database operations fail
**Solutions**:
- Check "Test DB Functions" results
- Verify database initialization
- Check platform-specific database issues

#### Cause 4: Permission Issues
**Symptoms**: "You cannot delete your own account" for all users
**Solutions**:
- Verify you're testing on different users, not yourself
- Check user IDs in console logs
- Ensure test users were created properly

#### Cause 5: UI State Issues
**Symptoms**: Buttons appear but don't respond
**Solutions**:
- Exit bulk mode if active
- Check if loading state is stuck
- Refresh the component

### Console Commands for Manual Testing

If buttons still don't work, try these in browser console:

```javascript
// Test delete function directly
DatabaseService.deleteUser(2).then(() => console.log('Delete worked')).catch(e => console.error('Delete failed:', e));

// Test status toggle directly
DatabaseService.toggleUserStatus(2, false).then(() => console.log('Status toggle worked')).catch(e => console.error('Status toggle failed:', e));

// Check all users
DatabaseService.getUsersByCompany(1).then(users => console.log('All users:', users));
```

### Expected Console Output

When working correctly, you should see:

```
Delete button clicked!
Delete button clicked for user: John Doe
Deleting user with ID: 2
User logged out successfully
```

Or for status toggle:

```
Status toggle button clicked!
Toggle status button clicked for user: Jane Smith Current status: true
Toggling user status for ID: 3 to: false
```

### Platform-Specific Issues

#### Web Platform
- Check browser console for errors
- Verify localStorage/IndexedDB permissions
- Test in different browsers

#### Mobile Platform
- Check React Native debugger
- Verify SQLite database permissions
- Test on device vs simulator

### Final Verification

After troubleshooting:
1. **Create sample users**
2. **Test "Test DB Functions"** - should pass
3. **Click delete button** - should show confirmation dialog
4. **Confirm deletion** - user should disappear
5. **Click status toggle** - should show confirmation dialog
6. **Confirm toggle** - user status should change

If all steps work, the buttons are functioning correctly. If any step fails, the console logs will show exactly where the issue occurs.
