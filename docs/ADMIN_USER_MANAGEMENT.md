# Admin User Management Functions

This document outlines all the admin functions available for managing other users in the CostCompass application.

## Overview

The admin user management system provides comprehensive tools for administrators to manage users within their company. All functions are accessible through the User Management screen and are protected by admin-only permissions.

## Core Features

### 1. User Profile Management
- **Edit User Profiles**: <PERSON><PERSON> can modify user names and email addresses
- **View User Details**: Display comprehensive user information including last login
- **User Status Tracking**: Monitor active/inactive status and admin privileges

### 2. Password Management
- **Reset User Passwords**: <PERSON><PERSON> can reset passwords for any user
- **Password Validation**: Enforces minimum 6-character password requirement
- **Secure Password Handling**: Passwords are handled securely throughout the system

### 3. User Status Management
- **Activate/Deactivate Users**: Toggle user account status without deletion
- **Prevent Self-Modification**: <PERSON>mins cannot change their own status
- **Login Restrictions**: Inactive users cannot log in

### 4. Admin Rights Management
- **Grant/Revoke Admin Rights**: Promote or demote users to/from admin status
- **Admin Protection**: <PERSON>mins cannot modify their own admin status
- **Bulk Admin Operations**: Change admin status for multiple users at once

### 5. User Search and Filtering
- **Real-time Search**: Search users by name or email address
- **Instant Results**: Filter results update as you type
- **Case-insensitive**: Search works regardless of case

### 6. Bulk Operations
- **Bulk Selection**: Select multiple users using checkboxes
- **Bulk Delete**: Delete multiple users simultaneously
- **Bulk Admin Changes**: Grant or revoke admin rights for multiple users
- **Safety Checks**: Prevents bulk operations on current admin user

### 7. User Statistics Dashboard
- **Total Users**: Display total number of company users
- **Active/Inactive Count**: Show breakdown of user statuses
- **Admin Count**: Display number of administrators
- **Visual Statistics**: Color-coded statistics for quick overview

### 8. User Activity Tracking
- **Last Login Tracking**: Record and display when users last logged in
- **Activity History**: Monitor user engagement
- **Inactive User Identification**: Identify users who haven't logged in recently

## User Interface Components

### Main User Management Screen
- **User Statistics Card**: Overview of user metrics
- **Search and Controls**: Search bar and bulk mode toggle
- **User List**: Detailed list of all company users
- **Action Buttons**: Individual user management actions

### User Actions (Per User)
- **Edit Profile** (✏️): Modify user name and email
- **Reset Password** (🔑): Set new password for user
- **Toggle Admin** (👤+/👤-): Grant or revoke admin rights
- **Toggle Status** (👤✓/👤✗): Activate or deactivate account
- **Delete User** (🗑️): Permanently remove user

### Bulk Operations
- **Bulk Mode Toggle**: Enable/disable bulk selection
- **Selection Checkboxes**: Select multiple users
- **Bulk Actions Menu**: Dropdown with bulk operations
- **Selected User Counter**: Shows number of selected users

### Dialog Components
- **Edit User Dialog**: Form for editing user profiles
- **Reset Password Dialog**: Form for setting new passwords
- **Confirmation Dialogs**: Safety confirmations for destructive actions

## Database Schema Enhancements

### Users Table Updates
```sql
-- New fields added to users table
is_active INTEGER DEFAULT 1,        -- User account status
last_login DATETIME,                -- Last login timestamp
```

### New Database Methods
- `updateUserProfile(id, profileData)`
- `resetUserPassword(id, newPassword)`
- `toggleUserStatus(id, isActive)`
- `updateLastLogin(id)`
- `searchUsers(companyId, searchTerm)`
- `bulkDeleteUsers(userIds)`
- `bulkToggleAdmin(userIds, isAdmin)`
- `getUserStats(companyId)`

## Security Features

### Permission Checks
- All admin functions require admin privileges
- Users cannot modify their own admin status or account status
- Bulk operations include safety checks

### Input Validation
- Email format validation
- Password strength requirements
- Required field validation
- Duplicate email prevention

### Data Protection
- Passwords are handled securely
- User data is validated before database operations
- Proper error handling and user feedback

## Usage Examples

### Creating a New User
```javascript
const userData = {
  email: '<EMAIL>',
  password: 'securepassword',
  name: 'John Doe',
  isAdmin: false
};
await AdminService.createUser(userData, companyId);
```

### Bulk Admin Assignment
```javascript
const userIds = [1, 2, 3];
await AdminService.bulkToggleAdmin(userIds, true, currentUserId);
```

### Searching Users
```javascript
const results = await AdminService.searchUsers(companyId, 'john');
```

## Error Handling

All admin functions include comprehensive error handling:
- Input validation errors
- Permission denied errors
- Database operation errors
- User-friendly error messages

## Best Practices

1. **Always validate admin permissions** before allowing access to admin functions
2. **Use confirmation dialogs** for destructive operations
3. **Provide clear feedback** for all operations
4. **Implement proper error handling** with user-friendly messages
5. **Log admin actions** for audit purposes
6. **Regular backup** of user data before bulk operations

## Future Enhancements

Potential future improvements:
- User role-based permissions beyond admin/user
- User group management
- Advanced user analytics
- Email notifications for user actions
- User import/export functionality
- Audit log for admin actions
