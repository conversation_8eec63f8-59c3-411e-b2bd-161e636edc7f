# Logo Customization Guide

## Current Logo Implementation

The CostCompass app now has logos integrated in three key locations:

### 1. Landing Page (LandingScreen.js)
- **Location**: Hero section, above the main title
- **Size**: 80x80 pixels
- **Implementation**: 
```jsx
<Image
  source={require('../../assets/icon.png')}
  style={styles.logo}
  resizeMode="contain"
/>
```

### 2. Login Screen (LoginScreen.js)
- **Location**: Header section in circular container
- **Size**: 48x48 pixels in 64x64 container
- **Implementation**:
```jsx
<Image
  source={require('../../assets/icon.png')}
  style={styles.logo}
  resizeMode="contain"
/>
```

### 3. App Navigation Header (AppNavigator.js)
- **Location**: Main app header, next to "CostCompass" text
- **Size**: 28x28 pixels
- **Implementation**:
```jsx
<Image
  source={require('../../assets/icon.png')}
  style={styles.headerLogo}
  resizeMode="contain"
/>
```

## How to Add Your Custom Logo

### Option 1: Replace Existing Icon
1. **Prepare your logo**:
   - Format: PNG with transparent background
   - Size: 1024x1024 pixels (square)
   - Style: Should work well at small sizes

2. **Replace the file**:
   - Save your logo as `assets/icon.png`
   - The app will automatically use your new logo

### Option 2: Use Separate Logo File
1. **Add your logo file**:
   - Place your logo at `assets/logo.png`

2. **Update the imports** in these files:
   - `src/screens/LandingScreen.js` (line 99)
   - `src/screens/LoginScreen.js` (line 84)
   - `src/navigation/AppNavigator.js` (line 35)

   Change from:
   ```jsx
   source={require('../../assets/icon.png')}
   ```
   To:
   ```jsx
   source={require('../../assets/logo.png')}
   ```

### Option 3: Different Logos for Different Screens
You can use different logos for different contexts:

1. **Landing page**: `assets/logo-hero.png` (larger, more detailed)
2. **Login screen**: `assets/logo-auth.png` (medium size)
3. **App header**: `assets/logo-header.png` (smaller, simplified)

## Logo Design Recommendations

### Size Guidelines
- **Landing page**: 80x80 to 120x120 pixels display size
- **Login screen**: 48x48 to 64x64 pixels display size
- **App header**: 24x24 to 32x32 pixels display size

### Design Tips
1. **Scalability**: Logo should be readable at 24px size
2. **Contrast**: Works well on both light and dark backgrounds
3. **Format**: PNG with transparent background preferred
4. **Simplicity**: Avoid fine details that disappear at small sizes

### Brand Consistency
- Use consistent colors with your app theme
- Consider the blue (#1976D2) and orange (#E65100) color scheme
- Ensure logo complements the existing UI design

## Advanced Customization

### Adding Logo to Other Screens
To add logos to other screens, follow this pattern:

1. **Import Image component**:
```jsx
import { Image } from 'react-native';
```

2. **Add logo component**:
```jsx
<Image
  source={require('../../assets/logo.png')}
  style={styles.yourLogoStyle}
  resizeMode="contain"
/>
```

3. **Add styles**:
```jsx
yourLogoStyle: {
  width: 40,
  height: 40,
  marginRight: 8,
},
```

### Web Favicon
The app already uses `assets/favicon.png` for web favicon. Update this file to match your logo.

### App Icons
Update these files for app store/device icons:
- `assets/icon.png` - Main app icon
- `assets/adaptive-icon.png` - Android adaptive icon
- `assets/splash-icon.png` - Splash screen icon

## Testing Your Logo

1. **Test on different screen sizes**: Web, mobile, tablet
2. **Check readability**: Ensure logo is clear at all sizes
3. **Verify contrast**: Test on light and dark backgrounds
4. **Cross-platform**: Test on iOS, Android, and web

## File Structure
```
assets/
├── icon.png          # Main app icon (1024x1024)
├── logo.png          # Custom logo (optional)
├── favicon.png       # Web favicon
├── splash-icon.png   # Splash screen
└── adaptive-icon.png # Android adaptive icon
```

Your logo is now integrated throughout the app and provides consistent branding across all user touchpoints!
