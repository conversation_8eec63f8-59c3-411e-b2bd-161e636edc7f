import React, { createContext, useContext, useReducer, useEffect } from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
import DatabaseService from "../services/database";

const AuthContext = createContext();

const initialState = {
  user: null,
  company: null,
  userCompanies: [],
  userAccounts: [],
  isLoading: true,
  isAuthenticated: false,
  isFirstUser: false,
  hasLoggedOut: false,
  needsCompanySelection: false,
  needsAccountSelection: false,
};

const authReducer = (state, action) => {
  switch (action.type) {
    case "SET_LOADING":
      return { ...state, isLoading: action.payload };
    case "SET_USER":
      return {
        ...state,
        user: action.payload.user,
        company: action.payload.company,
        userCompanies: action.payload.userCompanies || [],
        userAccounts: action.payload.userAccounts || [],
        isAuthenticated:
          !!action.payload.user || action.payload.needsAccountSelection,
        isLoading: false,
        hasLoggedOut: false,
        needsCompanySelection: action.payload.needsCompanySelection || false,
        needsAccountSelection: action.payload.needsAccountSelection || false,
      };
    case "SET_COMPANY_SELECTION":
      return {
        ...state,
        needsCompanySelection: action.payload,
        isLoading: false,
      };
    case "SELECT_COMPANY":
      return {
        ...state,
        company: action.payload.company,
        user: {
          ...state.user,
          is_admin: action.payload.company.user_role.is_admin,
          permissions: action.payload.company.user_role.permissions,
        },
        needsCompanySelection: false,
      };
    case "SELECT_ACCOUNT":
      return {
        ...state,
        user: action.payload.user,
        userAccounts: [],
        needsAccountSelection: false,
        needsCompanySelection: action.payload.needsCompanySelection || false,
      };
    case "SET_FIRST_USER":
      return { ...state, isFirstUser: action.payload };
    case "LOGOUT":
      return {
        ...state,
        user: null,
        company: null,
        userCompanies: [],
        userAccounts: [],
        isAuthenticated: false,
        isLoading: false,
        hasLoggedOut: true,
        needsCompanySelection: false,
        needsAccountSelection: false,
      };
    default:
      return state;
  }
};

export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  useEffect(() => {
    // Add a timeout to prevent hanging
    const initTimeout = setTimeout(() => {
      console.log(
        "AuthContext: Initialization timeout (5s), setting loading to false",
      );
      dispatch({ type: "SET_LOADING", payload: false });
    }, 5000); // 5 second timeout

    initializeAuth()
      .then(() => {
        console.log("AuthContext: Initialization completed successfully");
      })
      .catch((error) => {
        console.error("AuthContext: Initialization failed:", error);
        dispatch({ type: "SET_LOADING", payload: false });
      })
      .finally(() => {
        clearTimeout(initTimeout);
      });

    return () => {
      clearTimeout(initTimeout);
    };
  }, []);

  const initializeAuth = async () => {
    try {
      // Initialize database
      await DatabaseService.init();

      // Check if this is the first user
      const hasUsers = await DatabaseService.hasUsers();
      dispatch({ type: "SET_FIRST_USER", payload: !hasUsers });

      // For demo purposes, clear session to show landing page
      // Comment out these lines if you want to maintain sessions
      // await AsyncStorage.removeItem("userId");
      // await AsyncStorage.removeItem("hasLoggedOut");

      // Check for stored user session
      let storedUserId = null;
      try {
        storedUserId = await AsyncStorage.getItem("userId");
      } catch (error) {
        console.log("AuthContext: Error getting stored user ID:", error);
      }

      if (storedUserId) {
        const user = await DatabaseService.getUserById(parseInt(storedUserId));

        if (user) {
          const company = user.company_id
            ? await DatabaseService.getCompanyById(user.company_id)
            : null;
          dispatch({ type: "SET_USER", payload: { user, company } });
          return;
        }
      }

      // Disable auto-login for demo purposes - users should see the landing page
      // Auto-login dummy user for development (if exists) - but not after explicit logout
      // const hasLoggedOut = await AsyncStorage.getItem('hasLoggedOut');
      // if (!hasLoggedOut) {
      //   const dummyUser = await DatabaseService.getUserByEmail('<EMAIL>');
      //   if (dummyUser) {
      //     console.log('Auto-logging in dummy user for development');
      //     const company = dummyUser.company_id ? await DatabaseService.getCompanyById(dummyUser.company_id) : null;
      //     await AsyncStorage.setItem('userId', dummyUser.id.toString());
      //     dispatch({ type: 'SET_USER', payload: { user: dummyUser, company } });
      //     return;
      //   }
      // }

      dispatch({ type: "SET_LOADING", payload: false });
    } catch (error) {
      console.error("Auth initialization error:", error);
      dispatch({ type: "SET_LOADING", payload: false });
    }
  };

  const login = async (email, password) => {
    try {
      console.log("Login attempt for:", email);

      // Get all accounts for this email
      const userAccounts = await DatabaseService.getUserAccountsByEmail(email);
      console.log("Found accounts for email:", userAccounts.length);

      if (userAccounts.length === 0) {
        console.log("No accounts found for:", email);
        throw new Error("Invalid email or password");
      }

      // Find account with matching password
      const validAccount = userAccounts.find(
        (account) => account.password === password,
      );

      if (!validAccount) {
        console.log("Invalid password for:", email);
        throw new Error("Invalid email or password");
      }

      if (!validAccount.is_active) {
        console.log("Account deactivated for:", email);
        throw new Error(
          "Account is deactivated. Please contact an administrator.",
        );
      }

      console.log("Valid account found:", {
        ...validAccount,
        password: "[HIDDEN]",
      });

      // Check if user has multiple accounts with same email
      const activeAccounts = userAccounts.filter(
        (acc) => acc.is_active && acc.password === password,
      );

      if (activeAccounts.length > 1) {
        // Multiple accounts with same credentials - show account selection
        console.log("Multiple accounts found, showing account selection");
        dispatch({
          type: "SET_USER",
          payload: {
            user: null,
            company: null,
            userAccounts: activeAccounts,
            needsAccountSelection: true,
            needsCompanySelection: false,
          },
        });
        return { success: true };
      }

      // Single account - proceed with company selection logic
      const user = validAccount;

      // Update last login
      await DatabaseService.updateLastLogin(user.id);

      // Get user's companies
      console.log("Getting companies for user ID:", user.id);
      const userCompanies = await DatabaseService.getUserCompanies(user.id);
      console.log("User companies found:", userCompanies.length, userCompanies);

      // Store user session and clear logout flag
      await AsyncStorage.setItem("userId", user.id.toString());
      await AsyncStorage.removeItem("hasLoggedOut");

      if (userCompanies.length === 0) {
        // User has no companies - this shouldn't happen but handle gracefully
        console.log("No companies found for user:", user.id);
        throw new Error(
          "No companies found for this user. Please contact an administrator.",
        );
      } else if (userCompanies.length === 1) {
        // User has only one company - auto-select it
        const company = userCompanies[0];
        const userWithRole = {
          ...user,
          is_admin: company.user_role.is_admin,
          permissions: company.user_role.permissions,
        };

        dispatch({
          type: "SET_USER",
          payload: {
            user: userWithRole,
            company,
            userCompanies,
            needsCompanySelection: false,
            needsAccountSelection: false,
          },
        });
      } else {
        // User has multiple companies - needs to select one
        dispatch({
          type: "SET_USER",
          payload: {
            user,
            company: null,
            userCompanies,
            needsCompanySelection: true,
            needsAccountSelection: false,
          },
        });
      }

      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const register = async (userData) => {
    try {
      console.log("AuthContext register called with:", {
        ...userData,
        password: "[HIDDEN]",
      });
      const { email, password, name, companyData, subscriptionData } = userData;

      // Check if user already exists (now allowing multiple accounts per email)
      console.log("Checking existing accounts for email:", email);
      const existingAccounts =
        await DatabaseService.getUserAccountsByEmail(email);
      console.log("Found existing accounts:", existingAccounts.length);

      // Allow multiple accounts per email, but check for duplicate company registrations
      if (
        companyData &&
        companyData.registration &&
        companyData.registration.trim()
      ) {
        for (const account of existingAccounts) {
          const accountCompany = await DatabaseService.getCompanyById(
            account.company_id,
          );
          if (
            accountCompany &&
            accountCompany.registration === companyData.registration.trim()
          ) {
            console.log("Account already exists for this company registration");
            throw new Error(
              "You already have an account for a company with this registration number. Please use the existing account or contact the administrator.",
            );
          }
        }
      }

      let companyId = null;
      let company = null;

      // Handle company creation/validation
      if (companyData) {
        console.log("Company data provided, validating registration...");

        // CRITICAL: Check company registration BEFORE creating user account
        if (companyData.registration && companyData.registration.trim()) {
          console.log(
            "Checking if registration number exists:",
            companyData.registration,
          );
          const registrationExists =
            await DatabaseService.checkRegistrationNumberExists(
              companyData.registration,
            );
          if (registrationExists) {
            console.log(
              "Company registration already exists:",
              companyData.registration,
            );
            throw new Error(
              "A company with this registration number already exists in the system. Please contact the administrator to add you to the existing company account.",
            );
          }
          console.log("Registration number is unique, proceeding...");
        }

        console.log("Creating new company...");
        const accountNumber = await DatabaseService.generateAccountNumber();
        console.log("Generated account number:", accountNumber);

        // Prepare company data with subscription information
        const companyCreateData = {
          accountNumber,
          ...companyData,
        };

        // Add subscription data if provided (required for new companies)
        if (subscriptionData) {
          console.log("Adding subscription data to company:", subscriptionData);
          const now = new Date();
          companyCreateData.subscription_plan = subscriptionData.plan;
          companyCreateData.subscription_status = 'active';
          companyCreateData.subscription_start_date = now.toISOString();
          companyCreateData.billing_cycle = subscriptionData.billing;

          // Set subscription end date based on billing cycle
          const endDate = new Date(now);
          if (subscriptionData.billing === 'annual') {
            endDate.setFullYear(endDate.getFullYear() + 1);
          } else {
            endDate.setMonth(endDate.getMonth() + 1);
          }
          companyCreateData.subscription_end_date = endDate.toISOString();

          // Clear trial dates since they have a paid subscription
          companyCreateData.trial_end_date = null;
        } else {
          // No subscription provided - this should not happen for new companies
          console.error("No subscription data provided for new company");
          throw new Error("Subscription is required to create a new company account");
        }

        companyId = await DatabaseService.createCompany(companyCreateData);
        console.log("Created company with ID:", companyId);

        company = await DatabaseService.getCompanyById(companyId);
        console.log("Retrieved company:", company);
      } else if (!state.isFirstUser) {
        // For subsequent users without company data, use existing company
        console.log("No company data provided, using existing company...");
        const companies = await DatabaseService.getAllCompanies();
        if (companies.length > 0) {
          companyId = companies[0].id; // Use first available company
          company = companies[0];
          console.log("Using existing company ID:", companyId);
        } else {
          console.log("No companies available");
          throw new Error(
            "No company available. Please contact administrator.",
          );
        }
      }

      // Create user
      console.log("Creating user...");
      const userId = await DatabaseService.createUser({
        email,
        password,
        name,
        isAdmin: state.isFirstUser || (companyData && !state.isFirstUser), // Admin if first user OR creating new company
        companyId,
        permissions: [],
      });
      console.log("Created user with ID:", userId);

      // Add user to company with appropriate role
      if (companyId) {
        await DatabaseService.addUserToCompany(userId, companyId, {
          isAdmin: state.isFirstUser || (companyData && !state.isFirstUser),
          permissions: [],
          isActive: true,
        });
        console.log("Added user to company with ID:", companyId);
      }

      const user = await DatabaseService.getUserById(userId);
      console.log("Retrieved user:", { ...user, password: "[HIDDEN]" });

      // Store user session and clear logout flag
      console.log("Storing user session...");
      await AsyncStorage.setItem("userId", user.id.toString());
      await AsyncStorage.removeItem("hasLoggedOut");

      console.log("Updating auth state...");
      dispatch({ type: "SET_USER", payload: { user, company } });
      dispatch({ type: "SET_FIRST_USER", payload: false });

      console.log("Registration completed successfully");
      return { success: true };
    } catch (error) {
      console.error("Registration error in AuthContext:", error);
      return { success: false, error: error.message };
    }
  };

  const logout = async () => {
    try {
      console.log("Logging out user...");
      await AsyncStorage.removeItem("userId");
      await AsyncStorage.setItem("hasLoggedOut", "true");
      dispatch({ type: "LOGOUT" });
      console.log("User logged out successfully");
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  const updateUser = async (userData) => {
    try {
      await DatabaseService.updateUser(state.user.id, userData);
      const updatedUser = await DatabaseService.getUserById(state.user.id);
      dispatch({
        type: "SET_USER",
        payload: { user: updatedUser, company: state.company },
      });
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const updateCompany = async (companyData) => {
    try {
      if (!state.company) {
        throw new Error("No company associated with user");
      }

      await DatabaseService.updateCompany(state.company.id, companyData);
      const updatedCompany = await DatabaseService.getCompanyById(
        state.company.id,
      );
      dispatch({
        type: "SET_USER",
        payload: { user: state.user, company: updatedCompany },
      });
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const selectCompany = async (company) => {
    try {
      // Store selected company
      await AsyncStorage.setItem("selectedCompanyId", company.id.toString());

      // Update user role based on company relationship
      const userWithRole = {
        ...state.user,
        is_admin: company.user_role.is_admin,
        permissions: company.user_role.permissions,
      };

      dispatch({ type: "SELECT_COMPANY", payload: { company } });
      dispatch({
        type: "SET_USER",
        payload: {
          user: userWithRole,
          company,
          userCompanies: state.userCompanies,
          needsCompanySelection: false,
        },
      });

      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const selectAccount = async (account) => {
    try {
      // Update last login for selected account
      await DatabaseService.updateLastLogin(account.id);

      // Get user's companies for this account
      const userCompanies = await DatabaseService.getUserCompanies(account.id);

      // Store user session
      await AsyncStorage.setItem("userId", account.id.toString());

      if (userCompanies.length === 0) {
        throw new Error(
          "No companies found for this account. Please contact an administrator.",
        );
      } else if (userCompanies.length === 1) {
        // Single company - auto-select it
        const company = userCompanies[0];
        const userWithRole = {
          ...account,
          is_admin: company.user_role.is_admin,
          permissions: company.user_role.permissions,
        };

        dispatch({
          type: "SET_USER",
          payload: {
            user: userWithRole,
            company,
            userCompanies,
            needsCompanySelection: false,
            needsAccountSelection: false,
          },
        });
      } else {
        // Multiple companies - show company selection
        dispatch({
          type: "SET_USER",
          payload: {
            user: account,
            company: null,
            userCompanies,
            needsCompanySelection: true,
            needsAccountSelection: false,
          },
        });
      }

      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const changePassword = async (currentPassword, newPassword) => {
    try {
      if (!state.user) {
        throw new Error("No user logged in");
      }

      await DatabaseService.changeUserPassword(
        state.user.id,
        currentPassword,
        newPassword,
      );

      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const refreshCompany = async () => {
    try {
      if (!state.company) {
        return;
      }

      console.log("Refreshing company data for company ID:", state.company.id);
      const updatedCompany = await DatabaseService.getCompanyById(
        state.company.id,
      );

      if (updatedCompany) {
        dispatch({ type: "SET_COMPANY", payload: updatedCompany });
        console.log("Company data refreshed:", updatedCompany);
      }
    } catch (error) {
      console.error("Error refreshing company data:", error);
    }
  };

  const value = {
    ...state,
    login,
    register,
    logout,
    updateUser,
    updateCompany,
    selectCompany,
    selectAccount,
    changePassword,
    refreshCompany,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
