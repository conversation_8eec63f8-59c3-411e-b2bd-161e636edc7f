import React, { createContext, useContext, useEffect, useState } from 'react';
import { useUser, useAuth } from '@clerk/clerk-expo';
import DatabaseService from '../services/database';

const ClerkAuthContext = createContext();

export const useClerkAuth = () => {
  const context = useContext(ClerkAuthContext);
  if (!context) {
    throw new Error('useClerkAuth must be used within a ClerkAuthProvider');
  }
  return context;
};

export const ClerkAuthProvider = ({ children }) => {
  const { user: clerkUser, isLoaded: userLoaded } = useUser();
  const { isSignedIn, signOut } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [dbUser, setDbUser] = useState(null);
  const [company, setCompany] = useState(null);
  const [userCompanies, setUserCompanies] = useState([]);
  const [needsCompanySelection, setNeedsCompanySelection] = useState(false);

  // Sync Clerk user with local database
  useEffect(() => {
    const syncUserWithDatabase = async () => {
      if (!userLoaded) return;
      
      setIsLoading(true);
      
      try {
        if (isSignedIn && clerkUser) {
          const email = clerkUser.emailAddresses[0]?.emailAddress;
          const name = clerkUser.fullName || clerkUser.firstName || 'User';
          
          console.log('Syncing Clerk user with database:', { email, name });
          
          // Check if user exists in our database
          // For Clerk users, we should get the first active user account
          const userAccounts = await DatabaseService.getUserAccountsByEmail(email);
          let existingUser = userAccounts.find(user => user.is_active) || null;
          
          if (!existingUser) {
            console.log('🔄 NEW CLERK USER DETECTED - Setting needsOnboarding = true');
            console.log('isSignedIn:', isSignedIn, 'dbUser will be null');
            // For new Clerk users, we'll need to handle company creation
            // This could redirect to a company setup flow
            setDbUser(null);
            setCompany(null);
            setUserCompanies([]);
            setNeedsCompanySelection(false);
          } else {
            console.log('Found existing user in database:', existingUser);
            
            // Get user's companies
            const companies = await DatabaseService.getUserCompanies(existingUser.id);
            console.log('User companies:', companies);
            
            setDbUser(existingUser);
            setUserCompanies(companies);
            
            if (companies.length === 0) {
              // User exists but has no companies - needs setup
              setCompany(null);
              setNeedsCompanySelection(false);
            } else if (companies.length === 1) {
              // Single company - auto-select
              const selectedCompany = companies[0];
              const userWithRole = {
                ...existingUser,
                is_admin: selectedCompany.user_role.is_admin,
                permissions: selectedCompany.user_role.permissions,
              };
              
              setDbUser(userWithRole);
              setCompany(selectedCompany);
              setNeedsCompanySelection(false);
            } else {
              // Multiple companies - show selection
              setCompany(null);
              setNeedsCompanySelection(true);
            }
          }
        } else {
          // User is signed out
          setDbUser(null);
          setCompany(null);
          setUserCompanies([]);
          setNeedsCompanySelection(false);
        }
      } catch (error) {
        console.error('Error syncing user with database:', error);
      } finally {
        setIsLoading(false);
      }
    };

    syncUserWithDatabase();
  }, [isSignedIn, clerkUser, userLoaded]);

  const selectCompany = async (selectedCompany) => {
    try {
      if (!dbUser) return;
      
      const userWithRole = {
        ...dbUser,
        is_admin: selectedCompany.user_role.is_admin,
        permissions: selectedCompany.user_role.permissions,
      };
      
      setDbUser(userWithRole);
      setCompany(selectedCompany);
      setNeedsCompanySelection(false);
      
      console.log('Company selected:', selectedCompany);
    } catch (error) {
      console.error('Error selecting company:', error);
    }
  };

  const logout = async () => {
    try {
      await signOut();
      setDbUser(null);
      setCompany(null);
      setUserCompanies([]);
      setNeedsCompanySelection(false);
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const refreshAuthState = async () => {
    console.log('🔄 Manually refreshing auth state...');
    console.log('🔄 Current state:', { isSignedIn, clerkUser: !!clerkUser, dbUser: !!dbUser });

    if (isSignedIn && clerkUser) {
      const email = clerkUser.emailAddresses[0]?.emailAddress;
      console.log('🔄 Looking for user with email:', email);

      const userAccounts = await DatabaseService.getUserAccountsByEmail(email);
      console.log('🔄 Found user accounts:', userAccounts.length);

      let existingUser = userAccounts.find(user => user.is_active) || null;

      if (existingUser) {
        console.log('✅ Found user after refresh:', existingUser.name, 'ID:', existingUser.id);
        const companies = await DatabaseService.getUserCompanies(existingUser.id);
        console.log('✅ Found companies:', companies.length);

        setDbUser(existingUser);
        setUserCompanies(companies);

        if (companies.length === 1) {
          const selectedCompany = companies[0];
          const userWithRole = {
            ...existingUser,
            is_admin: selectedCompany.user_role.is_admin,
            permissions: selectedCompany.user_role.permissions,
          };

          setDbUser(userWithRole);
          setCompany(selectedCompany);
          setNeedsCompanySelection(false);
          console.log('🎯 User authenticated with company:', selectedCompany.name);
        } else if (companies.length > 1) {
          setNeedsCompanySelection(true);
          console.log('🎯 Multiple companies found, needs selection');
        }

        // Force loading state to false to trigger re-render
        setIsLoading(false);
        console.log('🎯 Auth state refresh completed successfully');
      } else {
        console.log('❌ No user found after refresh - this should not happen after onboarding');
        console.log('❌ Available user accounts:', userAccounts);
        setIsLoading(false);
      }
    } else {
      console.log('❌ Cannot refresh auth state - missing requirements:', { isSignedIn, clerkUser: !!clerkUser });
    }
  };

  const value = {
    // Clerk user data
    clerkUser,
    isSignedIn,
    
    // Database user data (your existing business logic)
    user: dbUser,
    company,
    userCompanies,
    
    // State flags
    isLoading: isLoading || !userLoaded,
    isAuthenticated: isSignedIn && !!dbUser,
    needsCompanySelection,
    needsOnboarding: isSignedIn && !dbUser, // New Clerk user needs onboarding
    
    // Actions
    selectCompany,
    logout,
    refreshAuthState,
  };

  return (
    <ClerkAuthContext.Provider value={value}>
      {children}
    </ClerkAuthContext.Provider>
  );
};
