import React, { createContext, useContext, useState, useEffect } from "react";
import DatabaseService from "../services/database";
import { useAuth } from "./AuthContext";

const SubscriptionContext = createContext();

export const useSubscription = () => {
  const context = useContext(SubscriptionContext);
  if (!context) {
    throw new Error("useSubscription must be used within a SubscriptionProvider");
  }
  return context;
};

const initialState = {
  subscriptionStatus: null,
  isLoading: true,
  hasActiveSubscription: false,
  isTrialActive: false,
  daysLeftInTrial: 0,
  needsSubscription: false,
};

export const SubscriptionProvider = ({ children }) => {
  const { company, isAuthenticated } = useAuth();
  const [state, setState] = useState(initialState);

  const checkSubscriptionStatus = async () => {
    if (!company || !isAuthenticated) {
      setState(prev => ({ ...prev, isLoading: false }));
      return;
    }

    try {
      setState(prev => ({ ...prev, isLoading: true }));
      
      const subscriptionStatus = await DatabaseService.getSubscriptionStatus(company.id);
      const isActive = await DatabaseService.isSubscriptionActive(company.id);
      
      let daysLeftInTrial = 0;
      let isTrialActive = false;
      
      if (subscriptionStatus?.status === 'trial' && subscriptionStatus.trialEndDate) {
        const trialEnd = new Date(subscriptionStatus.trialEndDate);
        const now = new Date();
        const timeDiff = trialEnd.getTime() - now.getTime();
        daysLeftInTrial = Math.ceil(timeDiff / (1000 * 3600 * 24));
        isTrialActive = daysLeftInTrial > 0;
      }

      const needsSubscription = !isActive && !isTrialActive;

      setState({
        subscriptionStatus,
        isLoading: false,
        hasActiveSubscription: isActive && subscriptionStatus?.status === 'active',
        isTrialActive,
        daysLeftInTrial: Math.max(0, daysLeftInTrial),
        needsSubscription,
      });

    } catch (error) {
      console.error("Error checking subscription status:", error);
      setState(prev => ({ 
        ...prev, 
        isLoading: false,
        needsSubscription: true 
      }));
    }
  };

  const purchaseSubscription = async (plan, billingCycle = 'monthly') => {
    if (!company) {
      throw new Error("No company selected");
    }

    try {
      // Calculate subscription dates
      const startDate = new Date();
      const endDate = new Date();
      
      if (billingCycle === 'annual') {
        endDate.setFullYear(endDate.getFullYear() + 1);
      } else {
        endDate.setMonth(endDate.getMonth() + 1);
      }

      // Update subscription in database
      await DatabaseService.updateCompanySubscription(company.id, {
        plan,
        status: 'active',
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        billingCycle
      });

      // Refresh subscription status
      await checkSubscriptionStatus();

      return { success: true };
    } catch (error) {
      console.error("Error purchasing subscription:", error);
      return { success: false, error: error.message };
    }
  };

  const cancelSubscription = async () => {
    if (!company) {
      throw new Error("No company selected");
    }

    try {
      await DatabaseService.updateCompanySubscription(company.id, {
        plan: state.subscriptionStatus?.plan || 'trial',
        status: 'cancelled',
        startDate: state.subscriptionStatus?.subscriptionStartDate,
        endDate: state.subscriptionStatus?.subscriptionEndDate,
        billingCycle: state.subscriptionStatus?.billingCycle || 'monthly'
      });

      await checkSubscriptionStatus();
      return { success: true };
    } catch (error) {
      console.error("Error cancelling subscription:", error);
      return { success: false, error: error.message };
    }
  };

  const extendTrial = async (days = 7) => {
    if (!company) {
      throw new Error("No company selected");
    }

    try {
      const newTrialEndDate = new Date();
      newTrialEndDate.setDate(newTrialEndDate.getDate() + days);

      await DatabaseService.updateCompanySubscription(company.id, {
        plan: 'trial',
        status: 'trial',
        startDate: null,
        endDate: null,
        billingCycle: 'monthly'
      });

      // Update trial end date in company record
      const companyIndex = DatabaseService.mockData.companies.findIndex(c => c.id === company.id);
      if (companyIndex !== -1) {
        DatabaseService.mockData.companies[companyIndex].trial_end_date = newTrialEndDate.toISOString();
      }

      await checkSubscriptionStatus();
      return { success: true };
    } catch (error) {
      console.error("Error extending trial:", error);
      return { success: false, error: error.message };
    }
  };

  // Check subscription status when company changes
  useEffect(() => {
    checkSubscriptionStatus();
  }, [company?.id, isAuthenticated]);

  const value = {
    ...state,
    checkSubscriptionStatus,
    purchaseSubscription,
    cancelSubscription,
    extendTrial,
  };

  return (
    <SubscriptionContext.Provider value={value}>
      {children}
    </SubscriptionContext.Provider>
  );
};
