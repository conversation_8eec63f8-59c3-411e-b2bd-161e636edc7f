import React, { useState } from "react";
import { View, StyleSheet, ScrollView, Alert, Dimensions } from "react-native";
import {
  Text,
  Button,
  Card,
  ActivityIndicator,
  Modal,
  Portal,
  TextInput,
  Divider,
  Dialog,
} from "react-native-paper";
import { Ionicons } from "@expo/vector-icons";
// import { useSubscription } from "../contexts/SubscriptionContext";
import { colors, spacing, borderRadius, commonStyles } from "../styles/theme";

const { width } = Dimensions.get("window");

const SubscriptionModal = ({ visible, onDismiss, onSubscriptionComplete, companyName, initialSelectedPlan }) => {
  // const { purchaseSubscription, isLoading } = useSubscription();
  const [selectedPlan, setSelectedPlan] = useState(initialSelectedPlan || "professional");
  // Removed annual billing - only monthly subscriptions
  const [purchasing, setPurchasing] = useState(false);
  const [showPlanSelection, setShowPlanSelection] = useState(false);

  // Card details state
  const [cardNumber, setCardNumber] = useState("");
  const [expiryDate, setExpiryDate] = useState("");
  const [cvv, setCvv] = useState("");
  const [cardholderName, setCardholderName] = useState("");
  const [billingAddress, setBillingAddress] = useState("");
  const [postalCode, setPostalCode] = useState("");

  // Dialog state
  const [errorDialogVisible, setErrorDialogVisible] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [successDialogVisible, setSuccessDialogVisible] = useState(false);
  const [confirmDialogVisible, setConfirmDialogVisible] = useState(false);

  const pricingPlans = [
    {
      id: "starter",
      name: "Starter",
      description: "Perfect for small businesses getting started",
      monthlyPrice: 20,
      features: [
        "Up to 5 users",
        "Up to 5 benchmark/budget",
        "Benchmark/budget reports",
        "Email notifications",
        "Basic benchmarking"
      ],
      popular: false,
    },
    {
      id: "professional",
      name: "Professional",
      description: "Ideal for growing businesses",
      monthlyPrice: 30,
      features: [
        "Up to 10 users",
        "Up to 10 benchmark/budget",
        "Benchmark/budget reports",
        "Email notifications",
        "Basic benchmarking",
        "Upload historical data",
        "Data export"
      ],
      popular: true,
    },
    {
      id: "enterprise",
      name: "Enterprise",
      description: "For large organizations",
      monthlyPrice: 40,
      features: [
        "Unlimited users",
        "Up to 10 benchmark/budget",
        "Benchmark/budget reports",
        "Email notifications",
        "Basic benchmarking",
        "Upload historical data",
        "Data export"
      ],
      popular: false,
    },
  ];

  const showError = (message) => {
    setErrorMessage(message);
    setErrorDialogVisible(true);
  };

  const showSuccess = () => {
    setSuccessDialogVisible(true);
  };

  const showConfirm = () => {
    setConfirmDialogVisible(true);
  };

  const validateCardDetails = () => {
    console.log("=== VALIDATING CARD DETAILS ===");

    const cleanCardNumber = cardNumber.replace(/\s/g, '');
    console.log("Card number length (clean):", cleanCardNumber.length);
    if (!cardNumber.trim() || cleanCardNumber.length < 16) {
      console.log("VALIDATION FAILED: Card number invalid");
      showError("Please enter a valid 16-digit card number");
      return false;
    }

    console.log("Expiry date:", expiryDate);
    if (!expiryDate.trim() || !expiryDate.match(/^\d{2}\/\d{2}$/)) {
      console.log("VALIDATION FAILED: Expiry date invalid");
      showError("Please enter expiry date in MM/YY format");
      return false;
    }

    console.log("CVV length:", cvv.length);
    if (!cvv.trim() || cvv.length < 3) {
      console.log("VALIDATION FAILED: CVV invalid");
      showError("Please enter a valid CVV");
      return false;
    }

    console.log("Cardholder name:", cardholderName ? "provided" : "empty");
    if (!cardholderName.trim()) {
      console.log("VALIDATION FAILED: Cardholder name missing");
      showError("Please enter cardholder name");
      return false;
    }

    console.log("Billing address:", billingAddress ? "provided" : "empty");
    if (!billingAddress.trim()) {
      console.log("VALIDATION FAILED: Billing address missing");
      showError("Please enter billing address");
      return false;
    }

    console.log("Postal code:", postalCode);
    if (!postalCode.trim()) {
      console.log("VALIDATION FAILED: Postal code missing");
      showError("Please enter postal code");
      return false;
    }

    console.log("=== ALL VALIDATIONS PASSED ===");
    return true;
  };

  const handlePurchase = async () => {
    console.log("=== SUBSCRIPTION MODAL: handlePurchase called ===");
    console.log("Selected plan:", selectedPlan);
    console.log("Card details:", {
      cardNumber: cardNumber ? `${cardNumber.substring(0, 4)}****` : "empty",
      expiryDate,
      cvv: cvv ? "***" : "empty",
      cardholderName,
      billingAddress: billingAddress ? "provided" : "empty",
      postalCode
    });

    if (!selectedPlan) {
      console.log("ERROR: No plan selected");
      showError("Please select a plan");
      return;
    }

    console.log("About to validate card details...");
    if (!validateCardDetails()) {
      console.log("ERROR: Card validation failed");
      return;
    }
    console.log("Card validation passed!");

    console.log("Setting purchasing to true...");
    setPurchasing(true);

    try {
      // Simulate payment processing
      console.log("=== STARTING PAYMENT PROCESSING ===");
      console.log("Processing payment for plan:", selectedPlan);
      console.log("Billing cycle: monthly");

      console.log("Simulating payment delay...");
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Simulate successful payment processing
      console.log("=== PAYMENT PROCESSED SUCCESSFULLY ===");

      console.log("Showing success dialog...");
      showSuccess();
    } catch (error) {
      console.error("=== PAYMENT PROCESSING ERROR ===", error);
      showError(error.message || "Unable to process subscription");
    } finally {
      console.log("Setting purchasing to false...");
      setPurchasing(false);
    }
  };

  const formatCardNumber = (text) => {
    const cleaned = text.replace(/\s/g, '');
    const match = cleaned.match(/\d{1,4}/g);
    return match ? match.join(' ').substr(0, 19) : '';
  };

  const formatExpiryDate = (text) => {
    const cleaned = text.replace(/\D/g, '');
    if (cleaned.length >= 2) {
      return cleaned.substring(0, 2) + '/' + cleaned.substring(2, 4);
    }
    return cleaned;
  };

  const fillTestData = () => {
    console.log("Filling test data...");
    setCardNumber("4111 1111 1111 1111");
    setExpiryDate("12/25");
    setCvv("123");
    setCardholderName("John Doe");
    setBillingAddress("123 Test Street, Test City");
    setPostalCode("12345");
  };

  const handleDirectSubscription = () => {
    console.log("=== DIRECT SUBSCRIPTION BYPASS ===");
    showConfirm();
  };

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onDismiss}
        contentContainerStyle={styles.modalContainer}
      >
        <ScrollView showsVerticalScrollIndicator={false}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>Complete Your Subscription</Text>
            <Text style={styles.subtitle}>
              Subscribe to create your company account: {companyName}
            </Text>
          </View>



          {/* Plan Selection or Selected Plan Display */}
          {showPlanSelection ? (
            <View style={styles.planSelectionContainer}>
              <Text style={styles.sectionTitle}>Subscription Required</Text>
              <Text style={styles.sectionSubtitle}>
                A subscription is required to create your company account. Choose your plan and enter payment details to continue.
              </Text>
              {pricingPlans.map((plan) => (
                <Card
                  key={plan.id}
                  style={[
                    styles.planCard,
                    selectedPlan === plan.id && styles.selectedPlanCard,
                    plan.popular && styles.popularPlanCard
                  ]}
                  onPress={() => {
                    setSelectedPlan(plan.id);
                    setShowPlanSelection(false);
                  }}
                >
                  <Card.Content>
                    {plan.popular && (
                      <Text style={styles.popularBadge}>⭐ Most Popular</Text>
                    )}
                    <Text style={styles.planName}>{plan.name}</Text>
                    <Text style={styles.planPrice}>
                      £{plan.monthlyPrice}/month
                    </Text>
                    <Text style={styles.planDescription}>{plan.description}</Text>
                    <View style={styles.planFeatures}>
                      {plan.features.slice(0, 3).map((feature, index) => (
                        <Text key={index} style={styles.planFeature}>• {feature}</Text>
                      ))}
                    </View>
                  </Card.Content>
                </Card>
              ))}
              <Button
                mode="outlined"
                onPress={() => setShowPlanSelection(false)}
                style={styles.backButton}
              >
                Back to Payment
              </Button>
            </View>
          ) : (
            <Card style={styles.selectedPlanCard}>
              <Card.Content>
                <Text style={styles.selectedPlanTitle}>Selected Plan</Text>
                <Text style={styles.selectedPlanName}>
                  {pricingPlans.find(p => p.id === selectedPlan)?.name}
                </Text>
                <Text style={styles.selectedPlanPrice}>
                  £{pricingPlans.find(p => p.id === selectedPlan)?.monthlyPrice}/month
                </Text>
                <Button
                  mode="text"
                  onPress={() => setShowPlanSelection(true)}
                  style={styles.changePlanButton}
                >
                  Change Plan
                </Button>
              </Card.Content>
            </Card>
          )}

          {/* Payment Details - Only show when not selecting plans */}
          {!showPlanSelection && (
            <Card style={styles.paymentCard}>
              <Card.Content>
                <Text style={styles.sectionTitle}>Payment Details</Text>

              {/* Test Buttons - Remove in production */}
              <View style={{ flexDirection: 'row', gap: spacing.sm, marginBottom: spacing.md }}>
                <Button
                  mode="outlined"
                  onPress={fillTestData}
                  style={{ flex: 1 }}
                  compact
                >
                  Fill Test Data
                </Button>
                <Button
                  mode="contained"
                  onPress={handleDirectSubscription}
                  style={{ flex: 1 }}
                  compact
                  buttonColor={colors.secondary}
                >
                  Direct Subscribe
                </Button>
              </View>
              
              <TextInput
                label="Card Number *"
                value={cardNumber}
                onChangeText={(text) => setCardNumber(formatCardNumber(text))}
                mode="outlined"
                style={styles.input}
                keyboardType="numeric"
                maxLength={19}
                disabled={purchasing}
              />

              <View style={styles.cardRow}>
                <TextInput
                  label="MM/YY *"
                  value={expiryDate}
                  onChangeText={(text) => setExpiryDate(formatExpiryDate(text))}
                  mode="outlined"
                  style={[styles.input, styles.halfInput]}
                  keyboardType="numeric"
                  maxLength={5}
                  disabled={purchasing}
                />
                <TextInput
                  label="CVV *"
                  value={cvv}
                  onChangeText={setCvv}
                  mode="outlined"
                  style={[styles.input, styles.halfInput]}
                  keyboardType="numeric"
                  maxLength={4}
                  secureTextEntry
                  disabled={purchasing}
                />
              </View>

              <TextInput
                label="Cardholder Name *"
                value={cardholderName}
                onChangeText={setCardholderName}
                mode="outlined"
                style={styles.input}
                disabled={purchasing}
              />

              <TextInput
                label="Billing Address *"
                value={billingAddress}
                onChangeText={setBillingAddress}
                mode="outlined"
                style={styles.input}
                multiline
                disabled={purchasing}
              />

              <TextInput
                label="Postal Code *"
                value={postalCode}
                onChangeText={setPostalCode}
                mode="outlined"
                style={styles.input}
                disabled={purchasing}
              />
              </Card.Content>
            </Card>
          )}

          {/* Action Buttons - Only show when not selecting plans */}
          {!showPlanSelection && (
            <View style={styles.actionButtons}>
            <Button
              mode="outlined"
              onPress={onDismiss}
              style={styles.cancelButton}
              disabled={purchasing}
            >
              Cancel
            </Button>
            
            <Button
              mode="contained"
              onPress={() => {
                console.log("=== SUBSCRIBE BUTTON CLICKED ===");
                handlePurchase();
              }}
              style={styles.subscribeButton}
              disabled={purchasing}
              buttonColor={colors.primary}
            >
              {purchasing ? (
                <ActivityIndicator color="white" size="small" />
              ) : (
                `Subscribe & Create Account`
              )}
            </Button>
            </View>
          )}

          {!showPlanSelection && (
            <Text style={styles.securityNote}>
              🔒 Your payment information is secure and encrypted
            </Text>
          )}
        </ScrollView>
      </Modal>

      {/* Compact Dialogs */}
      <Portal>
        <Dialog
          visible={errorDialogVisible}
          onDismiss={() => setErrorDialogVisible(false)}
          style={commonStyles.compactDialog}
        >
          <Dialog.Title style={commonStyles.compactDialogTitle}>Error</Dialog.Title>
          <Dialog.Content style={commonStyles.compactDialogContent}>
            <Text variant="bodyMedium" style={commonStyles.compactDialogText}>{errorMessage}</Text>
          </Dialog.Content>
          <Dialog.Actions style={commonStyles.compactDialogActions}>
            <Button
              onPress={() => setErrorDialogVisible(false)}
              mode="contained"
              style={commonStyles.compactDialogButton}
              compact
            >
              OK
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      <Portal>
        <Dialog
          visible={successDialogVisible}
          onDismiss={() => setSuccessDialogVisible(false)}
          style={commonStyles.compactDialog}
        >
          <Dialog.Title style={commonStyles.compactDialogTitle}>Subscription Activated!</Dialog.Title>
          <Dialog.Content style={commonStyles.compactDialogContent}>
            <Text variant="bodyMedium" style={commonStyles.compactDialogText}>
              Your {selectedPlan} plan has been activated successfully. Your account will now be created.
            </Text>
          </Dialog.Content>
          <Dialog.Actions style={commonStyles.compactDialogActions}>
            <Button
              onPress={() => {
                setSuccessDialogVisible(false);
                console.log("=== USER CLICKED CONTINUE ===");
                console.log("About to call onSubscriptionComplete...");
                const subscriptionData = {
                  plan: selectedPlan,
                  billing: 'monthly',
                  cardDetails: {
                    last4: cardNumber.replace(/\s/g, '').slice(-4),
                    cardholderName,
                  }
                };
                console.log("Subscription data:", subscriptionData);
                onSubscriptionComplete(subscriptionData);
              }}
              mode="contained"
              style={commonStyles.compactDialogButton}
              compact
            >
              Continue
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      <Portal>
        <Dialog
          visible={confirmDialogVisible}
          onDismiss={() => setConfirmDialogVisible(false)}
          style={commonStyles.compactDialog}
        >
          <Dialog.Title style={commonStyles.compactDialogTitle}>Test Subscription</Dialog.Title>
          <Dialog.Content style={commonStyles.compactDialogContent}>
            <Text variant="bodyMedium" style={commonStyles.compactDialogText}>
              This will directly create the account with subscription. Continue?
            </Text>
          </Dialog.Content>
          <Dialog.Actions style={commonStyles.compactDialogActions}>
            <Button
              onPress={() => setConfirmDialogVisible(false)}
              mode="outlined"
              style={commonStyles.compactDialogButton}
              compact
            >
              Cancel
            </Button>
            <Button
              onPress={() => {
                setConfirmDialogVisible(false);
                console.log("=== CALLING onSubscriptionComplete DIRECTLY ===");
                const subscriptionData = {
                  plan: selectedPlan,
                  billing: 'monthly',
                  cardDetails: {
                    last4: "1111",
                    cardholderName: "Test User",
                  }
                };
                console.log("Direct subscription data:", subscriptionData);
                onSubscriptionComplete(subscriptionData);
              }}
              mode="contained"
              style={commonStyles.compactDialogButton}
              compact
            >
              Continue
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </Portal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    backgroundColor: colors.surface,
    margin: spacing.lg,
    borderRadius: borderRadius.xl,
    maxHeight: "90%",
    padding: spacing.lg,
  },
  header: {
    alignItems: "center",
    marginBottom: spacing.lg,
  },
  title: {
    fontSize: 24,
    fontWeight: "700",
    color: colors.text,
    textAlign: "center",
    marginBottom: spacing.sm,
  },
  subtitle: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: "center",
  },
  billingToggle: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: spacing.lg,
    gap: spacing.md,
  },
  toggleText: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  activeToggleText: {
    color: colors.primary,
    fontWeight: "600",
  },
  toggleButton: {
    borderRadius: borderRadius.md,
  },
  planSelectionContainer: {
    marginBottom: spacing.lg,
  },
  planCard: {
    marginBottom: spacing.md,
    borderWidth: 1,
    borderColor: colors.outline,
  },
  selectedPlanCard: {
    marginBottom: spacing.lg,
    borderWidth: 2,
    borderColor: colors.primary,
  },
  popularPlanCard: {
    borderColor: colors.secondary,
    borderWidth: 2,
  },
  popularBadge: {
    fontSize: 12,
    fontWeight: "600",
    color: colors.secondary,
    marginBottom: spacing.xs,
  },
  planName: {
    fontSize: 18,
    fontWeight: "700",
    color: colors.text,
    marginBottom: spacing.xs,
  },
  planPrice: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.primary,
    marginBottom: spacing.sm,
  },
  planDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: spacing.sm,
  },
  planFeatures: {
    marginTop: spacing.xs,
  },
  planFeature: {
    fontSize: 12,
    color: colors.textSecondary,
    marginBottom: 2,
  },
  backButton: {
    marginTop: spacing.md,
    alignSelf: "center",
  },
  selectedPlanTitle: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  selectedPlanName: {
    fontSize: 18,
    fontWeight: "700",
    color: colors.text,
    marginBottom: spacing.xs,
  },
  selectedPlanPrice: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.primary,
    marginBottom: spacing.sm,
  },
  changePlanButton: {
    alignSelf: "flex-start",
  },
  paymentCard: {
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
    marginBottom: spacing.sm,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: "center",
    marginBottom: spacing.md,
    lineHeight: 20,
  },
  input: {
    marginBottom: spacing.md,
    backgroundColor: colors.surface,
  },
  cardRow: {
    flexDirection: "row",
    gap: spacing.md,
  },
  halfInput: {
    flex: 1,
  },
  actionButtons: {
    flexDirection: "row",
    gap: spacing.md,
    marginBottom: spacing.md,
  },
  cancelButton: {
    flex: 1,
  },
  subscribeButton: {
    flex: 2,
  },
  securityNote: {
    fontSize: 12,
    color: colors.textSecondary,
    textAlign: "center",
    fontStyle: "italic",
  },
});

export default SubscriptionModal;
