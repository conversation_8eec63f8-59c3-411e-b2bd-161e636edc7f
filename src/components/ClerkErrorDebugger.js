import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';

const ClerkErrorDebugger = ({ error, context = 'Unknown' }) => {
  if (!error) return null;

  const parseClerkError = (error) => {
    const errorInfo = {
      context,
      timestamp: new Date().toISOString(),
      message: 'Unknown error',
      code: null,
      details: [],
      suggestions: []
    };

    // Parse error message
    if (error.message) {
      errorInfo.message = error.message;
    }

    // Parse Clerk-specific errors
    if (error.errors && Array.isArray(error.errors)) {
      errorInfo.details = error.errors.map(err => ({
        code: err.code,
        message: err.message,
        field: err.meta?.paramName || 'unknown'
      }));

      // Add specific suggestions based on error codes
      error.errors.forEach(err => {
        switch (err.code) {
          case 'form_identifier_not_found':
            errorInfo.suggestions.push('Email address not found. Check if email is correct.');
            break;
          case 'form_password_incorrect':
            errorInfo.suggestions.push('Password is incorrect. Try resetting password.');
            break;
          case 'form_identifier_exists':
            errorInfo.suggestions.push('<PERSON>ail already exists. Try signing in instead.');
            break;
          case 'form_param_unknown':
            if (err.meta?.paramName === 'first_name' || err.meta?.paramName === 'last_name') {
              errorInfo.suggestions.push('First/Last name not enabled in Clerk Dashboard → User & Authentication → Email, Phone, Username → Enable "First and last name"');
            } else {
              errorInfo.suggestions.push(`Parameter "${err.meta?.paramName}" not supported. Check Clerk Dashboard settings.`);
            }
            break;
          case 'session_token_and_uat_header_missing':
            errorInfo.suggestions.push('Authentication headers missing. Check Clerk configuration.');
            break;
          case 'clerk_js_not_loaded':
            errorInfo.suggestions.push('Clerk SDK not loaded properly. Check publishable key.');
            break;
          case 'verification_already_verified':
            errorInfo.suggestions.push('Email already verified! Proceeding to next step automatically.');
            errorInfo.suggestions.push('If stuck, try refreshing the page or starting over.');
            break;
          default:
            if (err.message.includes('Native API')) {
              errorInfo.suggestions.push('Enable Native API in Clerk Dashboard → Native Applications');
            } else if (err.message.includes('publishable key')) {
              errorInfo.suggestions.push('Check EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY in .env file');
            } else if (err.message.includes('network')) {
              errorInfo.suggestions.push('Check internet connection and Clerk service status');
            }
        }
      });
    }

    // Generic error handling
    if (errorInfo.suggestions.length === 0) {
      if (errorInfo.message.toLowerCase().includes('unknown')) {
        errorInfo.suggestions.push('Most likely: Native API not enabled in Clerk Dashboard');
        errorInfo.suggestions.push('Check: Environment variables loaded correctly');
        errorInfo.suggestions.push('Verify: Internet connection and Clerk service status');
      }
    }

    return errorInfo;
  };

  const errorInfo = parseClerkError(error);

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>🚨 Clerk Error Debug</Text>
        <Text style={styles.context}>Context: {errorInfo.context}</Text>
        <Text style={styles.timestamp}>{errorInfo.timestamp}</Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Error Message:</Text>
        <Text style={styles.errorMessage}>{errorInfo.message}</Text>
      </View>

      {errorInfo.details.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Error Details:</Text>
          {errorInfo.details.map((detail, index) => (
            <View key={index} style={styles.errorDetail}>
              <Text style={styles.errorCode}>Code: {detail.code}</Text>
              <Text style={styles.errorField}>Field: {detail.field}</Text>
              <Text style={styles.errorDetailMessage}>{detail.message}</Text>
            </View>
          ))}
        </View>
      )}

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Suggested Solutions:</Text>
        {errorInfo.suggestions.map((suggestion, index) => (
          <Text key={index} style={styles.suggestion}>
            • {suggestion}
          </Text>
        ))}
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Environment Check:</Text>
        <Text style={styles.envCheck}>
          Publishable Key: {process.env.EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY ? '✅ Set' : '❌ Missing'}
        </Text>
        <Text style={styles.envCheck}>
          Key Format: {process.env.EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY?.startsWith('pk_') ? '✅ Valid' : '❌ Invalid'}
        </Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Raw Error (for support):</Text>
        <Text style={styles.rawError}>
          {JSON.stringify(error, null, 2)}
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff3cd',
    borderColor: '#ffeaa7',
    borderWidth: 1,
    borderRadius: 8,
    margin: 16,
    maxHeight: 400,
  },
  header: {
    backgroundColor: '#fd79a8',
    padding: 12,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'white',
  },
  context: {
    fontSize: 14,
    color: 'white',
    marginTop: 4,
  },
  timestamp: {
    fontSize: 12,
    color: 'rgba(255,255,255,0.8)',
    marginTop: 2,
  },
  section: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#ffeaa7',
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#d63031',
    marginBottom: 8,
  },
  errorMessage: {
    fontSize: 14,
    color: '#2d3436',
    backgroundColor: '#fff',
    padding: 8,
    borderRadius: 4,
    fontFamily: 'monospace',
  },
  errorDetail: {
    backgroundColor: '#fff',
    padding: 8,
    borderRadius: 4,
    marginBottom: 8,
  },
  errorCode: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#e17055',
  },
  errorField: {
    fontSize: 12,
    color: '#636e72',
  },
  errorDetailMessage: {
    fontSize: 13,
    color: '#2d3436',
    marginTop: 4,
  },
  suggestion: {
    fontSize: 13,
    color: '#00b894',
    marginBottom: 4,
    lineHeight: 18,
  },
  envCheck: {
    fontSize: 13,
    color: '#2d3436',
    marginBottom: 4,
    fontFamily: 'monospace',
  },
  rawError: {
    fontSize: 10,
    color: '#636e72',
    backgroundColor: '#f8f9fa',
    padding: 8,
    borderRadius: 4,
    fontFamily: 'monospace',
  },
});

export default ClerkErrorDebugger;
