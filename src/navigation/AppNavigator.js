import React, { useState, useEffect } from "react";
import { Text, View, Image, StyleSheet } from "react-native";
import { NavigationContainer } from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { Ionicons } from "@expo/vector-icons";

import { useAuth } from "../contexts/AuthContext";
import { useSubscription } from "../contexts/SubscriptionContext";
import { ClerkAuthProvider, useClerkAuth } from "../contexts/ClerkAuthContext";
import DatabaseService from "../services/database";
import useInvitations from "../hooks/useInvitations";
import LoadingScreen from "../screens/LoadingScreen";
import LandingScreen from "../screens/LandingScreen";
import LoginScreen from "../screens/LoginScreen";

import ClerkSignInScreen from "../screens/ClerkSignInScreen";
import ClerkSignUpScreen from "../screens/ClerkSignUpScreen";
import ClerkOnboardingScreen from "../screens/ClerkOnboardingScreen";
import HomeScreen from "../screens/HomeScreen";
import BenchmarkDetailScreen from "../screens/BenchmarkDetailScreen";

import UserManagementScreen from "../screens/UserManagementScreen";
import BenchmarkSetupScreen from "../screens/BenchmarkSetupScreen";
import HistoricalDataScreen from "../screens/HistoricalDataScreen";
import ProfileScreen from "../screens/ProfileScreen";
import CompanySelectionScreen from "../screens/CompanySelectionScreen";
import InvitationManagementScreen from "../screens/InvitationManagementScreen";
import InvitationAcceptScreen from "../screens/InvitationAcceptScreen";
import UserInvitationsScreen from "../screens/UserInvitationsScreen";
import AccountSelectionScreen from "../screens/AccountSelectionScreen";
import NotificationScreen from "../screens/NotificationScreen";
import OccupancyDetailScreen from "../screens/OccupancyDetailScreen";
import SubscriptionScreen from "../screens/SubscriptionScreen";

const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

// Custom header component with logo
const LogoTitle = () => (
  <View style={styles.logoTitleContainer}>
    <Image
      source={require("../../assets/logo.png")}
      style={styles.headerLogo}
      resizeMode="contain"
    />
    <Text style={styles.headerTitle}>CostCompass</Text>
  </View>
);

const AuthStack = () => (
  <Stack.Navigator screenOptions={{ headerShown: false }}>
    <Stack.Screen name="Landing" component={LandingScreen} />
    <Stack.Screen name="Login" component={LoginScreen} />
    <Stack.Screen name="ClerkSignIn" component={ClerkSignInScreen} />
    <Stack.Screen name="ClerkSignUp" component={ClerkSignUpScreen} />
    <Stack.Screen name="ClerkOnboarding" component={ClerkOnboardingScreen} />
    <Stack.Screen
      name="Subscription"
      component={SubscriptionScreen}
      options={{ headerShown: true, title: "Choose Your Plan" }}
    />
    <Stack.Screen
      name="InvitationAccept"
      component={InvitationAcceptScreen}
      options={{ headerShown: true, title: "Company Invitation" }}
    />
  </Stack.Navigator>
);

const HomeStack = () => (
  <Stack.Navigator>
    <Stack.Screen
      name="HomeMain"
      component={HomeScreen}
      options={{
        headerShown: false, // Hide header completely for HomeScreen
      }}
    />
    <Stack.Screen
      name="BenchmarkDetail"
      component={BenchmarkDetailScreen}
      options={{ title: "Benchmark Details" }}
    />
    <Stack.Screen
      name="OccupancyDetail"
      component={OccupancyDetailScreen}
      options={{ title: "Occupancy Management" }}
    />
    <Stack.Screen
      name="BenchmarkSetup"
      component={BenchmarkSetupScreen}
      options={{ title: "Setup Benchmark" }}
    />
    <Stack.Screen
      name="HistoricalData"
      component={HistoricalDataScreen}
      options={{ title: "Historical Data" }}
    />
  </Stack.Navigator>
);

const ManagementStack = () => (
  <Stack.Navigator initialRouteName="UserManagement">
    <Stack.Screen
      name="UserManagement"
      component={UserManagementScreen}
      options={{
        title: "User Management",
        headerRight: () => (
          <Text style={{ marginRight: 15, fontSize: 12, color: "#666" }}>
            Admin Panel
          </Text>
        ),
      }}
    />
    <Stack.Screen
      name="InvitationManagement"
      component={InvitationManagementScreen}
      options={{ title: "User Invitations" }}
    />
  </Stack.Navigator>
);

const MainTabs = () => {
  const { user, company } = useAuth();
  const { pendingCount } = useInvitations();
  const [notificationCount, setNotificationCount] = useState(0);

  // Load notification count
  useEffect(() => {
    const loadNotificationCount = async () => {
      if (company) {
        try {
          const notifications = await DatabaseService.getNotificationsByCompany(company.id, true);
          setNotificationCount(notifications.length);
        } catch (error) {
          console.error("Error loading notification count:", error);
        }
      }
    };

    loadNotificationCount();
  }, [company]);

  // Debug logging
  console.log("MainTabs - Current user:", user);
  console.log("MainTabs - Is admin:", user?.is_admin);
  console.log("MainTabs - Pending invitations:", pendingCount);
  console.log("MainTabs - Unread notifications:", notificationCount);

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          if (route.name === "Home") {
            iconName = focused ? "home" : "home-outline";
          } else if (route.name === "Invitations") {
            iconName = focused ? "mail" : "mail-outline";
          } else if (route.name === "Management") {
            iconName = focused ? "settings" : "settings-outline";
          } else if (route.name === "Profile") {
            iconName = focused ? "person" : "person-outline";
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: "#2196F3",
        tabBarInactiveTintColor: "gray",
        headerShown: false,
      })}
    >
      <Tab.Screen name="Home" component={HomeStack} />
      <Tab.Screen
        name="Invitations"
        component={UserInvitationsScreen}
        options={{
          tabBarBadge: pendingCount > 0 ? pendingCount : undefined,
        }}
      />
      <Tab.Screen
        name="Notifications"
        component={NotificationScreen}
        options={{
          tabBarBadge: notificationCount > 0 ? notificationCount : undefined,
        }}
      />
      {user?.is_admin && (
        <Tab.Screen name="Management" component={ManagementStack} />
      )}
      <Tab.Screen name="Profile" component={ProfileScreen} />
    </Tab.Navigator>
  );
};

// Clerk-based App Navigator
const ClerkAppNavigator = () => {
  const {
    isLoading,
    isAuthenticated,
    needsCompanySelection,
    needsOnboarding,
    selectCompany,
    refreshAuthState,
    dbUser,
    company,
    clerkUser,
    isSignedIn,
  } = useClerkAuth();

  const {
    isLoading: subscriptionLoading,
    needsSubscription,
  } = useSubscription();

  if (isLoading || subscriptionLoading) {
    return <LoadingScreen />;
  }

  console.log('🚀 ClerkAppNavigator state:', {
    isLoading,
    isAuthenticated,
    needsCompanySelection,
    needsOnboarding,
    dbUser: !!dbUser,
    company: !!company,
    clerkUser: !!clerkUser,
    isSignedIn,
  });

  console.log('🎯 Navigation decision:');
  if (needsOnboarding) {
    console.log('→ Showing ONBOARDING screen');
  } else if (!isAuthenticated) {
    console.log('→ Showing AUTH stack');
  } else if (needsCompanySelection) {
    console.log('→ Showing COMPANY SELECTION');
  } else {
    console.log('→ Showing MAIN APP');
  }

  return (
    <NavigationContainer>
      {needsOnboarding ? (
        <Stack.Navigator screenOptions={{ headerShown: false }}>
          <Stack.Screen name="ClerkOnboarding">
            {() => <ClerkOnboardingScreen onComplete={async () => {
              // Refresh the auth context after onboarding
              console.log('🎯 ONBOARDING CALLBACK TRIGGERED - Refreshing auth state');

              try {
                await refreshAuthState();
                console.log('🎯 Auth state refreshed successfully');

                // Force a small delay to ensure state propagation
                setTimeout(() => {
                  console.log('🎯 Navigation should now update to main app');
                }, 500);

              } catch (error) {
                console.error('❌ Error refreshing auth state:', error);
              }
            }} />}
          </Stack.Screen>
        </Stack.Navigator>
      ) : !isAuthenticated ? (
        <AuthStack />
      ) : needsCompanySelection ? (
        <Stack.Navigator screenOptions={{ headerShown: false }}>
          <Stack.Screen name="CompanySelection">
            {() => <CompanySelectionScreen onSelectCompany={selectCompany} />}
          </Stack.Screen>
        </Stack.Navigator>
      ) : needsSubscription ? (
        <Stack.Navigator screenOptions={{ headerShown: false }}>
          <Stack.Screen
            name="Subscription"
            component={SubscriptionScreen}
          />
        </Stack.Navigator>
      ) : (
        <MainTabs />
      )}
    </NavigationContainer>
  );
};

// Original App Navigator (keeping for backward compatibility)
const OriginalAppNavigator = () => {
  const {
    isLoading,
    isAuthenticated,
    needsCompanySelection,
    needsAccountSelection,
  } = useAuth();

  const {
    isLoading: subscriptionLoading,
    needsSubscription,
  } = useSubscription();

  if (isLoading || subscriptionLoading) {
    return <LoadingScreen />;
  }

  return (
    <NavigationContainer>
      {!isAuthenticated ? (
        <AuthStack />
      ) : needsAccountSelection ? (
        <Stack.Navigator screenOptions={{ headerShown: false }}>
          <Stack.Screen
            name="AccountSelection"
            component={AccountSelectionScreen}
          />
        </Stack.Navigator>
      ) : needsCompanySelection ? (
        <Stack.Navigator screenOptions={{ headerShown: false }}>
          <Stack.Screen
            name="CompanySelection"
            component={CompanySelectionScreen}
          />
        </Stack.Navigator>
      ) : needsSubscription ? (
        <Stack.Navigator screenOptions={{ headerShown: false }}>
          <Stack.Screen
            name="Subscription"
            component={SubscriptionScreen}
          />
        </Stack.Navigator>
      ) : (
        <MainTabs />
      )}
    </NavigationContainer>
  );
};

// Main App Navigator with Clerk integration
const AppNavigator = () => {
  return (
    <ClerkAuthProvider>
      <ClerkAppNavigator />
    </ClerkAuthProvider>
  );
};

const styles = StyleSheet.create({
  logoTitleContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "rgba(255, 255, 255, 0.1)", // Neutral background to showcase logo
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: "rgba(25, 118, 210, 0.2)",
  },
  headerLogo: {
    width: 32,
    height: 32,
    marginRight: 8,
    // Removed tintColor to show actual logo colors
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#1976D2",
  },
});

export default AppNavigator;
