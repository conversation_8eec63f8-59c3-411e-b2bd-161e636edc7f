import React, { useState } from "react";
import { View, StyleSheet, ScrollView, Alert, Platform, Dimensions } from "react-native";
import { Text, Button, Card, Surface, ActivityIndicator } from "react-native-paper";
import { Ionicons } from "@expo/vector-icons";
import { useSubscription } from "../contexts/SubscriptionContext";
import { useClerkAuth } from "../contexts/ClerkAuthContext";
import { colors, spacing, borderRadius, commonStyles } from "../styles/theme";

const { width } = Dimensions.get("window");

const SubscriptionScreen = ({ navigation, route }) => {
  const { purchaseSubscription, subscriptionStatus, isLoading } = useSubscription();
  const { company } = useClerkAuth();
  const selectedPlanFromRoute = route?.params?.selectedPlan;
  const [selectedPlan, setSelectedPlan] = useState(selectedPlanFromRoute || "professional");
  // Removed annual billing - only monthly subscriptions
  const [purchasing, setPurchasing] = useState(false);

  const pricingPlans = [
    {
      id: "starter",
      name: "Starter",
      description: "Perfect for small businesses getting started",
      monthlyPrice: 20,
      features: [
        "Up to 5 users",
        "Up to 5 benchmark/budget",
        "Benchmark/budget reports",
        "Email notifications",
        "Basic benchmarking"
      ],
      popular: false,
    },
    {
      id: "professional",
      name: "Professional",
      description: "Ideal for growing businesses",
      monthlyPrice: 30,
      features: [
        "Up to 10 users",
        "Up to 10 benchmark/budget",
        "Benchmark/budget reports",
        "Email notifications",
        "Basic benchmarking",
        "Upload historical data",
        "Data export"
      ],
      popular: true,
    },
    {
      id: "enterprise",
      name: "Enterprise",
      description: "For large organizations",
      monthlyPrice: 40,
      features: [
        "Unlimited users",
        "Up to 10 benchmark/budget",
        "Benchmark/budget reports",
        "Email notifications",
        "Basic benchmarking",
        "Upload historical data",
        "Data export"
      ],
      popular: false,
    },
  ];

  const handlePurchase = async () => {
    if (!selectedPlan) {
      Alert.alert("Error", "Please select a plan");
      return;
    }

    setPurchasing(true);
    
    try {
      const result = await purchaseSubscription(selectedPlan, 'monthly');
      
      if (result.success) {
        Alert.alert(
          "Subscription Activated!",
          `Your ${selectedPlan} plan has been activated successfully. Welcome to CostCompass!`,
          [
            {
              text: "Continue",
              onPress: () => navigation.navigate("Dashboard")
            }
          ]
        );
      } else {
        Alert.alert("Purchase Failed", result.error || "Unable to process subscription");
      }
    } catch (error) {
      Alert.alert("Purchase Failed", error.message);
    } finally {
      setPurchasing(false);
    }
  };

  if (isLoading) {
    return (
      <View style={commonStyles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={{ marginTop: spacing.md }}>Loading subscription details...</Text>
      </View>
    );
  }

  return (
    <View style={commonStyles.container}>
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Subscription Required</Text>
          <Text style={styles.subtitle}>
            A paid subscription is required to continue using CostCompass. Select your plan and enter payment details to activate your account.
          </Text>
          {company && (
            <Text style={styles.companyName}>
              For: {company.name}
            </Text>
          )}
        </View>



        {/* Pricing Cards */}
        <View style={styles.pricingGrid}>
          {pricingPlans.map((plan) => (
            <Card
              key={plan.id}
              style={[
                styles.pricingCard,
                selectedPlan === plan.id && styles.selectedCard,
                plan.popular && styles.popularCard
              ]}
              onPress={() => setSelectedPlan(plan.id)}
            >
              {plan.popular && (
                <View style={styles.popularBadge}>
                  <Text style={styles.popularBadgeText}>Most Popular</Text>
                </View>
              )}
              
              <Card.Content style={styles.cardContent}>
                <Text style={styles.planName}>{plan.name}</Text>
                <Text style={styles.planDescription}>{plan.description}</Text>
                
                <View style={styles.priceContainer}>
                  <Text style={styles.price}>
                    £{plan.monthlyPrice}
                  </Text>
                  <Text style={styles.priceUnit}>
                    /month
                  </Text>
                </View>

                <View style={styles.featuresList}>
                  {plan.features.map((feature, index) => (
                    <View key={index} style={styles.featureItem}>
                      <Ionicons name="checkmark-circle" size={16} color={colors.success} />
                      <Text style={styles.featureText}>{feature}</Text>
                    </View>
                  ))}
                </View>
              </Card.Content>
            </Card>
          ))}
        </View>

        {/* Purchase Button */}
        <View style={styles.purchaseSection}>
          <Button
            mode="contained"
            onPress={handlePurchase}
            style={styles.purchaseButton}
            disabled={purchasing || !selectedPlan}
            buttonColor={colors.primary}
          >
            {purchasing ? (
              <ActivityIndicator color="white" size="small" />
            ) : (
              `Subscribe to ${selectedPlan ? pricingPlans.find(p => p.id === selectedPlan)?.name : 'Selected Plan'}`
            )}
          </Button>
          
          <Text style={styles.purchaseNote}>
            • 30-day money-back guarantee{'\n'}
            • Cancel anytime{'\n'}
            • Secure payment processing
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  scrollContainer: {
    padding: spacing.lg,
    paddingBottom: spacing.xxxl,
  },
  header: {
    alignItems: "center",
    marginBottom: spacing.xl,
  },
  title: {
    fontSize: 28,
    fontWeight: "700",
    color: colors.text,
    textAlign: "center",
    marginBottom: spacing.sm,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: "center",
    marginBottom: spacing.md,
  },
  companyName: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: "600",
  },
  billingToggle: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: spacing.xl,
    gap: spacing.md,
  },
  toggleText: {
    fontSize: 16,
    color: colors.textSecondary,
  },
  activeToggleText: {
    color: colors.primary,
    fontWeight: "600",
  },
  toggleButton: {
    borderRadius: borderRadius.md,
  },
  pricingGrid: {
    gap: spacing.lg,
    marginBottom: spacing.xl,
  },
  pricingCard: {
    borderRadius: borderRadius.lg,
    borderWidth: 2,
    borderColor: colors.border,
    position: "relative",
  },
  selectedCard: {
    borderColor: colors.primary,
    backgroundColor: colors.primaryLight,
  },
  popularCard: {
    borderColor: colors.success,
  },
  popularBadge: {
    position: "absolute",
    top: -12,
    left: "50%",
    transform: [{ translateX: -50 }],
    backgroundColor: colors.success,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.full,
    zIndex: 1,
  },
  popularBadgeText: {
    color: "white",
    fontSize: 12,
    fontWeight: "600",
  },
  cardContent: {
    padding: spacing.lg,
  },
  planName: {
    fontSize: 20,
    fontWeight: "700",
    color: colors.text,
    textAlign: "center",
    marginBottom: spacing.xs,
  },
  planDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: "center",
    marginBottom: spacing.lg,
  },
  priceContainer: {
    alignItems: "center",
    marginBottom: spacing.lg,
  },
  price: {
    fontSize: 36,
    fontWeight: "700",
    color: colors.primary,
  },
  priceUnit: {
    fontSize: 14,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  featuresList: {
    gap: spacing.sm,
  },
  featureItem: {
    flexDirection: "row",
    alignItems: "center",
    gap: spacing.sm,
  },
  featureText: {
    fontSize: 14,
    color: colors.text,
    flex: 1,
  },
  purchaseSection: {
    alignItems: "center",
  },
  purchaseButton: {
    width: "100%",
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.md,
    marginBottom: spacing.md,
  },
  purchaseNote: {
    fontSize: 12,
    color: colors.textSecondary,
    textAlign: "center",
    lineHeight: 18,
  },
});

export default SubscriptionScreen;
