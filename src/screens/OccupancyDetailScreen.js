import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Alert, Platform, Dimensions, Image } from 'react-native';
import {
  Text,
  Card,
  TextInput,
  Button,
  ActivityIndicator,
  Divider,
  Chip,
  DataTable,
  IconButton,
  Surface
} from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { colors, spacing, borderRadius, commonStyles, typography, elevation } from '../styles/theme';
import DatabaseService from '../services/database';
import { useClerkAuth } from '../contexts/ClerkAuthContext';

const { width } = Dimensions.get("window");

const OccupancyDetailScreen = ({ navigation }) => {
  const { user, company } = useClerkAuth();

  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [occupancyHistory, setOccupancyHistory] = useState([]);

  // Form state for updating occupancy
  const [newOccupancy, setNewOccupancy] = useState('');
  const [changeReason, setChangeReason] = useState('');

  useEffect(() => {
    loadOccupancyData();
  }, [company]);

  const loadOccupancyData = async () => {
    try {
      if (company) {
        console.log('Loading occupancy data for company:', company.id);
        
        // Set current occupancy in form
        setNewOccupancy(company.current_occupancy.toString());
        
        // Load occupancy history
        const history = await DatabaseService.getOccupancyHistory(company.id, 20);
        setOccupancyHistory(history || []);
        
        console.log('Loaded occupancy history:', history);
      }
    } catch (error) {
      console.error('Error loading occupancy data:', error);
      Alert.alert('Error', 'Failed to load occupancy data');
    } finally {
      setLoading(false);
    }
  };

  const validateForm = () => {
    if (!newOccupancy.trim()) {
      Alert.alert('Error', 'Please enter the new occupancy number');
      return false;
    }

    const numOccupancy = parseInt(newOccupancy);
    if (isNaN(numOccupancy) || numOccupancy < 0) {
      Alert.alert('Error', 'Please enter a valid occupancy number (0 or greater)');
      return false;
    }

    if (numOccupancy > company.capacity) {
      Alert.alert('Error', `Occupancy cannot exceed capacity (${company.capacity})`);
      return false;
    }

    return true;
  };

  const handleUpdateOccupancy = async () => {
    if (!validateForm()) return;

    const numOccupancy = parseInt(newOccupancy);
    
    // Check if occupancy actually changed
    if (numOccupancy === company.current_occupancy) {
      Alert.alert('Info', 'Occupancy number is the same as current occupancy');
      return;
    }

    setSubmitting(true);

    try {
      console.log('Updating occupancy:', {
        companyId: company.id,
        newOccupancy: numOccupancy,
        changeReason: changeReason.trim() || null,
        userId: user.id
      });

      await DatabaseService.updateCompanyOccupancy(
        company.id,
        numOccupancy,
        changeReason.trim() || null,
        user.id
      );

      // Clear form
      setChangeReason('');

      // Refresh company data in AuthContext
      await refreshCompany();

      // Reload occupancy history
      await loadOccupancyData();

      Alert.alert('Success', 'Occupancy updated successfully!');
    } catch (error) {
      console.error('Error updating occupancy:', error);
      Alert.alert('Error', 'Failed to update occupancy. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const calculateOccupancyPercentage = () => {
    if (!company || company.capacity === 0) return 0;
    return Math.round((company.current_occupancy / company.capacity) * 100);
  };

  const getOccupancyColor = (percentage) => {
    if (percentage >= 90) return colors.success;
    if (percentage >= 70) return colors.warning;
    return colors.error;
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatDateTime = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  const getChangeIcon = (oldOccupancy, newOccupancy) => {
    if (newOccupancy > oldOccupancy) return 'trending-up';
    if (newOccupancy < oldOccupancy) return 'trending-down';
    return 'trending-flat';
  };

  const getChangeColor = (oldOccupancy, newOccupancy) => {
    if (newOccupancy > oldOccupancy) return colors.success; // Green for increase
    if (newOccupancy < oldOccupancy) return colors.error; // Red for decrease
    return colors.textSecondary; // Gray for no change
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
        <Text variant="bodyMedium" style={styles.loadingText}>
          Loading occupancy data...
        </Text>
      </View>
    );
  }

  if (!company) {
    return (
      <View style={styles.container}>
        <Card style={styles.card}>
          <Card.Content>
            <Text variant="headlineSmall">No Company Data</Text>
            <Text variant="bodyMedium">
              No company information available.
            </Text>
          </Card.Content>
        </Card>
      </View>
    );
  }

  const occupancyPercentage = calculateOccupancyPercentage();

  return (
    <View style={commonStyles.container}>
      {/* Split Layout - Blue section on left, content on right */}
      <View style={styles.mainContent}>
        {/* Left Side - Blue Header Section (Desktop only) */}
        {Platform.OS === 'web' && width > 768 && (
          <View style={styles.leftSide}>
            <LinearGradient
              colors={colors.gradientPrimary}
              style={styles.leftSideGradient}
            >
              <View style={styles.leftSideContent}>
                {/* App Logo and Name */}
                <View style={styles.logoSection}>
                  <View style={styles.appLogoContainer}>
                    <Image
                      source={require("../../assets/logo.png")}
                      style={styles.appLogo}
                      resizeMode="contain"
                    />
                  </View>
                  <Text style={styles.appNameBig}>CostCompass</Text>
                </View>

                {/* Page Header */}
                <View style={styles.pageHeader}>
                  <View style={styles.headerIconContainer}>
                    <Ionicons
                      name="people"
                      size={32}
                      color={colors.textOnPrimary}
                    />
                  </View>
                  <Text style={styles.leftSideTitle}>
                    Occupancy Management
                  </Text>
                  <Text style={styles.leftSideSubtitle}>
                    Track and manage facility occupancy levels
                  </Text>
                </View>

                {/* Occupancy Stats */}
                <View style={styles.leftSideInfo}>
                  <View style={styles.leftSideInfoItem}>
                    <Ionicons name="people" size={20} color={colors.textOnPrimary} />
                    <Text style={styles.leftSideInfoText}>
                      {company.current_occupancy} / {company.capacity} {company.capacity_type}
                    </Text>
                  </View>
                  <View style={styles.leftSideInfoItem}>
                    <Ionicons name="analytics" size={20} color={colors.textOnPrimary} />
                    <Text style={styles.leftSideInfoText}>
                      {occupancyPercentage}% occupancy rate
                    </Text>
                  </View>
                  <View style={styles.leftSideInfoItem}>
                    <Ionicons name="time" size={20} color={colors.textOnPrimary} />
                    <Text style={styles.leftSideInfoText}>
                      {occupancyHistory.length} history records
                    </Text>
                  </View>
                </View>
              </View>
            </LinearGradient>
          </View>
        )}

        {/* Right Side - Main Content */}
        <View style={styles.rightSide}>
          {/* Mobile Header (only shown on mobile) */}
          {(Platform.OS !== 'web' || width <= 768) && (
            <LinearGradient
              colors={colors.gradientPrimary}
              style={styles.mobileHeader}
            >
              <View style={styles.mobileHeaderContent}>
                <View style={styles.headerIconContainer}>
                  <Ionicons
                    name="people"
                    size={32}
                    color={colors.textOnPrimary}
                  />
                </View>
                <Text style={styles.mobileHeaderTitle}>
                  Occupancy Management
                </Text>
                <Text style={styles.mobileHeaderSubtitle}>
                  Track and manage facility occupancy levels
                </Text>
              </View>
            </LinearGradient>
          )}

          <ScrollView
            contentContainerStyle={styles.rightSideScrollContainer}
            style={styles.rightSideScrollView}
            showsVerticalScrollIndicator={false}
          >
      {/* Current Occupancy and Update Occupancy Side by Side */}
      <View style={styles.cardsRow}>
        {/* Current Occupancy Overview */}
        <Card style={[commonStyles.cardLarge, styles.cardHalf]}>
          <Card.Content>
            <View style={commonStyles.sectionHeader}>
              <Ionicons name="analytics" size={24} color={colors.primary} />
              <Text variant="titleLarge" style={commonStyles.sectionTitle}>
                Current Occupancy
              </Text>
              <Chip
                icon="trending-up"
                style={[styles.occupancyChip, { backgroundColor: getOccupancyColor(occupancyPercentage) + '20' }]}
                textStyle={{ color: getOccupancyColor(occupancyPercentage) }}
              >
                {occupancyPercentage}%
              </Chip>
            </View>

            <View style={styles.occupancyOverview}>
              <View style={styles.occupancyStats}>
                <Text variant="headlineLarge" style={[styles.occupancyNumber, { color: getOccupancyColor(occupancyPercentage) }]}>
                  {company.current_occupancy}
                </Text>
                <Text variant="bodyLarge" style={styles.occupancyText}>
                  of {company.capacity} {company.capacity_type}
                </Text>
              </View>

              <View style={styles.occupancyDetails}>
                <Text variant="bodyMedium" style={styles.detailText}>
                  Available: {company.capacity - company.current_occupancy} {company.capacity_type}
                </Text>
                <Text variant="bodyMedium" style={styles.detailText}>
                  Occupancy Rate: {occupancyPercentage}%
                </Text>
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* Update Occupancy Form */}
        <Card style={[commonStyles.cardLarge, styles.cardHalf]}>
          <Card.Content>
            <View style={commonStyles.sectionHeader}>
              <Ionicons name="create" size={24} color={colors.primary} />
              <Text variant="titleLarge" style={commonStyles.sectionTitle}>
                Update Occupancy
              </Text>
            </View>
            <Divider style={styles.divider} />

            <TextInput
              label={`New Occupancy (0 - ${company.capacity})`}
              value={newOccupancy}
              onChangeText={setNewOccupancy}
              mode="outlined"
              keyboardType="numeric"
              style={styles.input}
              disabled={submitting}
              placeholder="Enter new occupancy number"
              left={<TextInput.Icon icon="people" />}
            />

            <TextInput
              label="Reason for Change (Optional)"
              value={changeReason}
              onChangeText={setChangeReason}
              mode="outlined"
              multiline
              numberOfLines={2}
              style={styles.input}
              disabled={submitting}
              placeholder="e.g., New admission, Discharge, Transfer..."
              left={<TextInput.Icon icon="note-text" />}
            />

            <Button
              mode="contained"
              onPress={handleUpdateOccupancy}
              loading={submitting}
              disabled={submitting}
              style={styles.submitButton}
              icon="check"
            >
              {submitting ? 'Updating...' : 'Update Occupancy'}
            </Button>
          </Card.Content>
        </Card>
      </View>

      {/* Occupancy History */}
      <Card style={commonStyles.cardLarge}>
        <Card.Content>
          <View style={commonStyles.sectionHeader}>
            <Ionicons name="time" size={24} color={colors.primary} />
            <Text variant="titleLarge" style={commonStyles.sectionTitle}>
              Occupancy History
            </Text>
          </View>
          <Divider style={styles.divider} />

          {occupancyHistory.length === 0 ? (
            <View style={commonStyles.emptyState}>
              <View style={commonStyles.emptyStateIcon}>
                <Ionicons name="analytics-outline" size={48} color={colors.textLight} />
              </View>
              <Text style={commonStyles.emptyStateTitle}>
                No occupancy changes recorded yet
              </Text>
              <Text style={commonStyles.emptyStateText}>
                Use the form above to record your first occupancy update
              </Text>
            </View>
          ) : (
            <DataTable style={styles.dataTable}>
              <DataTable.Header style={styles.tableHeader}>
                <DataTable.Title style={styles.dateColumn}>
                  <View style={styles.headerTitle}>
                    <Text style={styles.headerText}>Date</Text>
                  </View>
                </DataTable.Title>
                <DataTable.Title style={styles.changeColumn} numeric>
                  <View style={styles.headerTitle}>
                    <Text style={styles.headerText}>Change</Text>
                  </View>
                </DataTable.Title>
                <DataTable.Title style={styles.reasonColumn}>
                  <View style={styles.headerTitle}>
                    <Text style={styles.headerText}>Reason</Text>
                  </View>
                </DataTable.Title>
                <DataTable.Title style={styles.userColumn}>
                  <View style={styles.headerTitle}>
                    <Text style={styles.headerText}>Updated By</Text>
                  </View>
                </DataTable.Title>
              </DataTable.Header>

              {occupancyHistory.map((record) => (
                <DataTable.Row key={record.id} style={styles.tableRow}>
                  <DataTable.Cell style={[styles.dateColumn, styles.dataCell]}>
                    <Text style={styles.dateText}>
                      {formatDateTime(record.created_at)}
                    </Text>
                  </DataTable.Cell>
                  <DataTable.Cell style={[styles.changeColumn, styles.dataCell]} numeric>
                    <View style={styles.changeCell}>
                      <Ionicons
                        name={getChangeIcon(record.old_occupancy, record.new_occupancy)}
                        size={14}
                        color={getChangeColor(record.old_occupancy, record.new_occupancy)}
                      />
                      <Text style={[styles.changeCellText, { color: getChangeColor(record.old_occupancy, record.new_occupancy) }]}>
                        {record.old_occupancy} → {record.new_occupancy}
                      </Text>
                    </View>
                  </DataTable.Cell>
                  <DataTable.Cell style={[styles.reasonColumn, styles.dataCell]}>
                    <Text style={styles.reasonText}>
                      {record.change_reason || 'No reason provided'}
                    </Text>
                  </DataTable.Cell>
                  <DataTable.Cell style={[styles.userColumn, styles.dataCell]}>
                    <Text style={styles.userText}>
                      {record.created_by_name || 'Unknown'}
                    </Text>
                  </DataTable.Cell>
                </DataTable.Row>
              ))}
            </DataTable>
          )}
        </Card.Content>
      </Card>

      {/* Back Button */}
      <Button
        mode="outlined"
        onPress={() => navigation.goBack()}
        style={styles.backButton}
        icon="arrow-left"
      >
        Back to Dashboard
      </Button>
          </ScrollView>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  // Split Layout Styles
  mainContent: {
    flex: 1,
    flexDirection: Platform.OS === 'web' && width > 768 ? 'row' : 'column',
  },
  // Left Side - Blue Section (Desktop only)
  leftSide: {
    flex: 0.4,
    minWidth: 350,
    maxWidth: 450,
  },
  leftSideGradient: {
    flex: 1,
    padding: spacing.lg,
    paddingTop: spacing.md,
    justifyContent: 'flex-start',
  },
  leftSideContent: {
    alignItems: 'flex-start',
    maxWidth: 350,
    alignSelf: 'center',
    width: '100%',
  },
  // Logo Section
  logoSection: {
    alignItems: 'flex-start',
    alignSelf: 'flex-start',
    marginBottom: spacing.xl,
    paddingBottom: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.2)',
    width: '100%',
  },
  appLogoContainer: {
    width: '100%', // Full width of the blue sidebar content area
    height: 120, // Taller to accommodate larger logo
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.lg,
    marginTop: spacing.sm,
    // Remove artificial shadows and borders for more natural integration
  },
  appLogo: {
    width: 280, // Almost full width of the blue sidebar content
    height: 100, // Proportional height
    // Remove tintColor to show original logo colors
  },
  appNameBig: {
    color: colors.textOnPrimary,
    fontSize: 32,
    fontWeight: typography.fontWeights.bold,
    textAlign: 'left',
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
    letterSpacing: 0.5,
  },
  // Page Header Section
  pageHeader: {
    alignItems: 'flex-start',
    marginBottom: spacing.lg,
    width: '100%',
  },
  leftSideTitle: {
    color: colors.textOnPrimary,
    fontSize: 24,
    fontWeight: typography.fontWeights.bold,
    textAlign: 'left',
    marginBottom: spacing.sm,
    textShadowColor: 'rgba(0,0,0,0.1)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  leftSideSubtitle: {
    color: colors.textOnPrimary,
    fontSize: 16,
    textAlign: 'left',
    opacity: 0.95,
    marginBottom: spacing.lg,
    textShadowColor: 'rgba(0,0,0,0.1)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  leftSideInfo: {
    marginTop: spacing.lg,
    alignSelf: 'stretch',
  },
  leftSideInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    padding: spacing.md,
    borderRadius: borderRadius.lg,
  },
  leftSideInfoText: {
    color: colors.textOnPrimary,
    fontSize: 14,
    fontWeight: '500',
    marginLeft: spacing.sm,
    flex: 1,
  },
  // Right Side - Main Content
  rightSide: {
    flex: 1.6,
    backgroundColor: colors.background,
  },
  rightSideScrollContainer: {
    padding: spacing.lg,
    paddingBottom: spacing.xxxl,
  },
  rightSideScrollView: {
    flex: 1,
  },
  // Mobile Header
  mobileHeader: {
    paddingTop: spacing.xxxxl,
    paddingBottom: spacing.xl,
    paddingHorizontal: spacing.xl,
    borderBottomLeftRadius: borderRadius.xxl,
    borderBottomRightRadius: borderRadius.xxl,
  },
  mobileHeaderContent: {
    alignItems: 'center',
  },
  mobileHeaderTitle: {
    color: colors.textOnPrimary,
    fontSize: 24,
    fontWeight: typography.fontWeights.bold,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  mobileHeaderSubtitle: {
    color: colors.textOnPrimary,
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.95,
  },
  // Cards Layout
  cardsRow: {
    flexDirection: width > 768 ? 'row' : 'column',
    gap: spacing.lg,
    marginBottom: spacing.lg,
  },
  cardHalf: {
    flex: width > 768 ? 1 : undefined,
    minHeight: width > 768 ? 300 : undefined,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: spacing.md,
    color: colors.textSecondary,
  },
  headerIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
    elevation: elevation.sm,
    shadowColor: 'rgba(0, 0, 0, 0.3)',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  occupancyChip: {
    alignSelf: 'flex-start',
  },
  occupancyOverview: {
    flexDirection: width > 768 ? 'column' : 'row',
    justifyContent: width > 768 ? 'flex-start' : 'space-between',
    alignItems: width > 768 ? 'flex-start' : 'center',
    gap: width > 768 ? spacing.md : 0,
  },
  occupancyStats: {
    alignItems: 'flex-start',
  },
  occupancyNumber: {
    fontWeight: typography.fontWeights.bold,
    marginBottom: spacing.xs,
  },
  occupancyText: {
    color: colors.textSecondary,
  },
  occupancyDetails: {
    alignItems: width > 768 ? 'flex-start' : 'flex-end',
    marginTop: width > 768 ? spacing.sm : 0,
  },
  detailText: {
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  divider: {
    marginBottom: spacing.md,
  },
  input: {
    marginBottom: spacing.md,
  },
  submitButton: {
    marginTop: spacing.sm,
    borderRadius: borderRadius.sm,
  },
  backButton: {
    margin: spacing.md,
    marginTop: spacing.sm,
    borderRadius: borderRadius.sm,
  },
  dataTable: {
    backgroundColor: 'transparent',
  },
  tableHeader: {
    backgroundColor: colors.surfaceVariant,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  tableRow: {
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
    minHeight: 48,
  },
  headerTitle: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerText: {
    fontSize: typography.fontSizes.sm,
    fontWeight: typography.fontWeights.bold,
    color: colors.text,
    textDecorationLine: 'underline',
    textAlign: 'center',
  },
  dataCell: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  dateColumn: {
    flex: 1.5,
    paddingHorizontal: spacing.sm,
    justifyContent: 'center',
  },
  changeColumn: {
    flex: 1.2,
    paddingHorizontal: spacing.sm,
    justifyContent: 'center',
  },
  reasonColumn: {
    flex: 1.5,
    paddingHorizontal: spacing.sm,
    justifyContent: 'center',
  },
  userColumn: {
    flex: 1.2,
    paddingHorizontal: spacing.sm,
    justifyContent: 'center',
  },
  dateText: {
    fontSize: typography.fontSizes.xs,
    color: colors.text,
    fontWeight: typography.fontWeights.normal,
    textAlign: 'left',
  },
  changeCell: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    gap: spacing.xs,
  },
  changeCellText: {
    fontSize: typography.fontSizes.xs,
    fontWeight: typography.fontWeights.medium,
    textAlign: 'right',
  },
  reasonText: {
    fontSize: typography.fontSizes.xs,
    color: colors.textSecondary,
    fontWeight: typography.fontWeights.normal,
    textAlign: 'left',
  },
  userText: {
    fontSize: typography.fontSizes.xs,
    color: colors.textSecondary,
    fontWeight: typography.fontWeights.normal,
    textAlign: 'left',
  },
});

export default OccupancyDetailScreen;
