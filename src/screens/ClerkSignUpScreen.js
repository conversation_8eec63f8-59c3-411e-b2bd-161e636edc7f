import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert, Dimensions, Platform, TouchableOpacity } from 'react-native';
import {
  Text,
  TextInput,
  Button,
  Card,
  ActivityIndicator,
  SegmentedButtons,
  Switch,
  Dialog,
  Portal,
} from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useSignUp } from '@clerk/clerk-expo';
import ClerkErrorDebugger from '../components/ClerkErrorDebugger';
import DatabaseService from '../services/DatabaseService';

import {
  colors,
  spacing,
  borderRadius,
  commonStyles,
  typography,
} from '../styles/theme';

const { width } = Dimensions.get("window");
const isDesktop = Platform.OS === "web" && width > 768;

const ClerkSignUpScreen = ({ navigation, route }) => {
  const selectedPlanFromRoute = route?.params?.selectedPlan;
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [pendingVerification, setPendingVerification] = useState(false);
  const [verificationCode, setVerificationCode] = useState('');

  // Company data (always required for new Clerk users)
  const [companyName, setCompanyName] = useState('');
  const [companyRegistration, setCompanyRegistration] = useState('');
  const [capacity, setCapacity] = useState('');
  const [currentOccupancy, setCurrentOccupancy] = useState('');
  const [capacityType, setCapacityType] = useState('beds');

  // Dialog state
  const [errorDialogVisible, setErrorDialogVisible] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [errorTitle, setErrorTitle] = useState('Error');
  const [lastError, setLastError] = useState(null);
  const [step, setStep] = useState(1); // 1: User details, 2: Company details, 3: Verification

  const { signUp, setActive, isLoaded } = useSignUp();

  const showError = (title, message) => {
    setErrorTitle(title);
    setErrorMessage(message);
    setErrorDialogVisible(true);
  };

  const validateUserForm = () => {
    if (!email.trim() || !password.trim() || !firstName.trim()) {
      showError('Error', 'Please fill in all required fields');
      return false;
    }

    if (password !== confirmPassword) {
      showError('Error', 'Passwords do not match');
      return false;
    }

    if (password.length < 6) {
      showError('Error', 'Password must be at least 6 characters long');
      return false;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      showError('Error', 'Please enter a valid email address');
      return false;
    }

    return true;
  };

  const validateCompanyForm = () => {
    if (!companyName.trim() || !capacity.trim()) {
      showError('Error', 'Please fill in all company details');
      return false;
    }

    if (isNaN(capacity) || parseInt(capacity) <= 0) {
      showError('Error', 'Please enter a valid capacity number');
      return false;
    }

    if (currentOccupancy && (isNaN(currentOccupancy) || parseInt(currentOccupancy) < 0)) {
      showError('Error', 'Please enter a valid current occupancy number');
      return false;
    }

    if (currentOccupancy && parseInt(currentOccupancy) > parseInt(capacity)) {
      showError('Error', 'Current occupancy cannot exceed capacity');
      return false;
    }

    return true;
  };

  const handleNextStep = () => {
    if (step === 1) {
      if (validateUserForm()) {
        setStep(2);
      }
    } else if (step === 2) {
      if (validateCompanyForm()) {
        // First create Clerk account, then handle payment
        handleClerkSignUp();
      }
    }
  };

  // Subscription handling is now moved to onboarding screen
  // This screen only handles Clerk authentication

  const handleClerkSignUp = async () => {
    if (!isLoaded) return;

    setLoading(true);

    try {
      console.log('Creating Clerk account with:', {
        email: email.trim(),
        firstName: firstName.trim(),
        lastName: lastName.trim()
      });

      // Try without firstName/lastName first to test basic sign-up
      const result = await signUp.create({
        emailAddress: email.trim(),
        password,
      });

      console.log('Clerk sign up result:', result);

      // Send email verification
      await signUp.prepareEmailAddressVerification({ strategy: 'email_code' });
      setStep(3);
      setPendingVerification(true);

      Alert.alert(
        'Verification Required',
        'Please check your email for a verification code.'
      );
    } catch (error) {
      console.error('Sign up error:', error);
      console.error('Error details:', JSON.stringify(error, null, 2));

      // Capture error for debugging
      setLastError(error);

      let errorMessage = 'An error occurred during sign up';
      if (error.errors && error.errors.length > 0) {
        errorMessage = error.errors[0].message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      showError('Sign Up Failed', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleVerification = async () => {
    if (!isLoaded) return;

    if (!verificationCode.trim()) {
      showError('Error', 'Please enter the verification code');
      return;
    }

    setLoading(true);

    // Clear any previous errors before attempting verification
    setLastError(null);

    try {
      console.log('Attempting email verification with code:', verificationCode.trim());

      const result = await signUp.attemptEmailAddressVerification({
        code: verificationCode.trim(),
      });

      console.log('Verification result:', result);

      console.log('Verification status:', result.status);
      console.log('Created session ID:', result.createdSessionId);

      if (result.status === 'complete') {
        await setActive({ session: result.createdSessionId });
        console.log('Sign up and verification successful - status complete');

        // Clear verification code and error state
        setVerificationCode('');
        setLastError(null);

        // Store the company data for later use in onboarding
        // Don't create database records yet - let ClerkAuthContext handle onboarding
        console.log('Clerk authentication successful, will proceed to onboarding');

        // Navigate to main app - ClerkAuthContext will detect new user and show onboarding
        navigation.reset({
          index: 0,
          routes: [{ name: 'Landing' }],
        });
      } else if (result.status === 'missing_requirements') {
        // Email verified but missing other requirements - proceed to payment anyway
        console.log('Email verified but missing requirements - proceeding to payment');

        // Try to set session if available
        if (result.createdSessionId) {
          await setActive({ session: result.createdSessionId });
        }

        // Clear verification code and error state
        setVerificationCode('');
        setLastError(null);

        // Check if this is a new user by looking in the database
        console.log('Verification successful, checking if user exists in database...');

        try {
          const email = result.emailAddress;
          const userAccounts = await DatabaseService.getUserAccountsByEmail(email);
          const existingUser = userAccounts.find(user => user.is_active) || null;

          if (!existingUser) {
            console.log('New user detected, navigating to onboarding...');
            // Navigate directly to onboarding for new users
            navigation.reset({
              index: 0,
              routes: [{ name: 'ClerkOnboarding' }],
            });
          } else {
            console.log('Existing user found, navigating to main app...');
            // Navigate to main app for existing users
            navigation.reset({
              index: 0,
              routes: [{ name: 'Landing' }],
            });
          }
        } catch (error) {
          console.error('Error checking user existence:', error);
          // Fallback to landing page
          navigation.reset({
            index: 0,
            routes: [{ name: 'Landing' }],
          });
        }
      } else {
        console.log('Verification incomplete:', result);
        console.log('Full result object:', JSON.stringify(result, null, 2));

        // Check if email verification was successful even if overall status isn't complete
        const emailVerified = result.verifications?.emailAddress?.status === 'verified';

        if (emailVerified) {
          console.log('Email verification successful, proceeding to payment');

          // Try to set session if available
          if (result.createdSessionId) {
            await setActive({ session: result.createdSessionId });
          }

          // Clear verification code and error state
          setVerificationCode('');
          setLastError(null);

          // Navigate to main app - ClerkAuthContext will detect new user and show onboarding
          navigation.reset({
            index: 0,
            routes: [{ name: 'Landing' }],
          });
        } else {
          showError('Error', `Verification status: ${result.status}. Please try again or contact support.`);
        }
      }
    } catch (error) {
      console.error('Verification error:', error);
      console.error('Verification error details:', JSON.stringify(error, null, 2));

      // Check if already verified
      if (error.errors && error.errors.some(e => e.code === 'verification_already_verified')) {
        console.log('Email already verified, proceeding to subscription');

        // Email is already verified, proceed to subscription
        try {
          // Check if we have a session already
          if (signUp.createdSessionId) {
            await setActive({ session: signUp.createdSessionId });
          }

          // Clear verification code and error state
          setVerificationCode('');
          setLastError(null);

          // Navigate to main app - ClerkAuthContext will detect new user and show onboarding
          navigation.reset({
            index: 0,
            routes: [{ name: 'Landing' }],
          });
          return;
        } catch (sessionError) {
          console.error('Session activation error:', sessionError);
          // Even if session activation fails, proceed to main app
          setVerificationCode('');
          setLastError(null);
          navigation.reset({
            index: 0,
            routes: [{ name: 'Landing' }],
          });
          return;
        }
      }

      // Check if it's a simple invalid code error
      const isSimpleInvalidCode = error.errors && error.errors.some(e =>
        e.code === 'form_code_incorrect' ||
        e.code === 'verification_failed' ||
        e.message?.toLowerCase().includes('invalid') ||
        e.message?.toLowerCase().includes('incorrect')
      );

      if (isSimpleInvalidCode) {
        // For simple invalid codes, don't show debug info
        console.log('Simple invalid code error, not showing debug');
        setLastError(null);

        let errorMessage = 'Invalid verification code. Please check your email and try again.';
        if (error.errors && error.errors.length > 0) {
          errorMessage = error.errors[0].longMessage || error.errors[0].message || errorMessage;
        }

        showError('Invalid Code', errorMessage);
      } else {
        // For other errors, show debug info
        console.log('Complex error, showing debug info');
        setLastError(error);

        let errorMessage = 'Verification failed';
        if (error.errors && error.errors.length > 0) {
          errorMessage = error.errors[0].message;
        } else if (error.message) {
          errorMessage = error.message;
        }

        showError('Verification Failed', errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  // Company and user creation moved to ClerkOnboardingScreen

  const navigateToSignIn = () => {
    navigation.navigate('ClerkSignIn');
  };

  const navigateToLanding = () => {
    navigation.navigate('Landing');
  };

  const handleGoBack = () => {
    if (step === 3 || pendingVerification) {
      setStep(2);
      setPendingVerification(false);
    } else if (step === 2) {
      setStep(1);
    } else {
      navigation.goBack();
    }
  };

  const getStepTitle = () => {
    switch (step) {
      case 1: return 'Create Account';
      case 2: return 'Company Details';
      case 3: return 'Verify Email';
      default: return 'Create Account';
    }
  };

  const getStepSubtitle = () => {
    switch (step) {
      case 1: return 'Enter your personal information';
      case 2: return 'Tell us about your company';
      case 3: return `Enter the verification code sent to ${email}`;
      default: return 'Join CostCompass to manage your benchmarks';
    }
  };

  // Render step 3 (verification) as part of the main form
  const renderStepContent = () => {
    if (step === 1) {
      return (
        <>
          {/* Name Fields */}
          <View style={styles.nameRow}>
            <TextInput
              label="First Name *"
              value={firstName}
              onChangeText={setFirstName}
              mode="outlined"
              style={[styles.input, styles.nameInput]}
              autoComplete="given-name"
              disabled={loading}
            />
            <TextInput
              label="Last Name"
              value={lastName}
              onChangeText={setLastName}
              mode="outlined"
              style={[styles.input, styles.nameInput]}
              autoComplete="family-name"
              disabled={loading}
            />
          </View>

          <TextInput
            label="Email *"
            value={email}
            onChangeText={setEmail}
            mode="outlined"
            style={styles.input}
            keyboardType="email-address"
            autoCapitalize="none"
            autoComplete="email"
            disabled={loading}
          />

          <TextInput
            label="Password *"
            value={password}
            onChangeText={setPassword}
            mode="outlined"
            style={styles.input}
            secureTextEntry={!showPassword}
            autoComplete="new-password"
            disabled={loading}
            right={
              <TextInput.Icon
                icon={showPassword ? 'eye-off' : 'eye'}
                onPress={() => setShowPassword(!showPassword)}
              />
            }
          />

          <TextInput
            label="Confirm Password *"
            value={confirmPassword}
            onChangeText={setConfirmPassword}
            mode="outlined"
            style={styles.input}
            secureTextEntry={!showConfirmPassword}
            autoComplete="new-password"
            disabled={loading}
            right={
              <TextInput.Icon
                icon={showConfirmPassword ? 'eye-off' : 'eye'}
                onPress={() => setShowConfirmPassword(!showConfirmPassword)}
              />
            }
          />
        </>
      );
    } else if (step === 2) {
      return (
        <>
          <TextInput
            label="Company Name *"
            value={companyName}
            onChangeText={setCompanyName}
            mode="outlined"
            style={styles.input}
            disabled={loading}
            placeholder="Enter your company name"
          />

          <TextInput
            label="Registration Number (Optional)"
            value={companyRegistration}
            onChangeText={setCompanyRegistration}
            mode="outlined"
            style={styles.input}
            disabled={loading}
            placeholder="Company registration number"
          />

          <View style={styles.capacityRow}>
            <TextInput
              label={`Capacity (${capacityType}) *`}
              value={capacity}
              onChangeText={setCapacity}
              mode="outlined"
              keyboardType="numeric"
              style={[styles.input, styles.capacityInput]}
              disabled={loading}
            />

            <View style={styles.capacityTypeContainer}>
              <Text style={styles.capacityTypeLabel}>Type:</Text>
              <SegmentedButtons
                value={capacityType}
                onValueChange={setCapacityType}
                buttons={[
                  { value: 'beds', label: 'Beds' },
                  { value: 'rooms', label: 'Rooms' },
                ]}
                style={styles.segmentedButtons}
                disabled={loading}
              />
            </View>
          </View>

          <TextInput
            label={`Current Occupancy (${capacityType})`}
            value={currentOccupancy}
            onChangeText={setCurrentOccupancy}
            mode="outlined"
            keyboardType="numeric"
            style={styles.input}
            disabled={loading}
          />
        </>
      );
    } else if (step === 3) {
      return (
        <>
          <TextInput
            label="Verification Code"
            value={verificationCode}
            onChangeText={(text) => {
              setVerificationCode(text);
              // Clear error state when user starts typing
              if (lastError) {
                setLastError(null);
              }
            }}
            mode="outlined"
            style={styles.input}
            keyboardType="number-pad"
            autoComplete="one-time-code"
            disabled={loading}
            placeholder="Enter 6-digit code"
          />

          <Text style={styles.verificationNote}>
            We've sent a verification code to {email}. Please check your inbox and enter the code above.
          </Text>

          <Text style={styles.nextStepNote}>
            After verification, you'll complete your subscription and your account will be ready!
          </Text>

          {/* Show skip button if verification already done */}
          {lastError && lastError.errors && lastError.errors.some(e => e.code === 'verification_already_verified') && (
            <TouchableOpacity
              style={[styles.button, styles.skipButton]}
              onPress={async () => {
                try {
                  // Activate session if available
                  if (signUp.createdSessionId) {
                    await setActive({ session: signUp.createdSessionId });
                  }
                  setLastError(null);
                  navigation.reset({
                    index: 0,
                    routes: [{ name: 'Landing' }],
                  });
                } catch (error) {
                  console.error('Error activating session:', error);
                  navigation.reset({
                    index: 0,
                    routes: [{ name: 'Landing' }],
                  });
                }
              }}
            >
              <Text style={styles.skipButtonText}>
                Email Already Verified - Continue to Payment
              </Text>
            </TouchableOpacity>
          )}
        </>
      );
    }
  };

  const getButtonText = () => {
    if (step === 1) return 'Next: Company Details';
    if (step === 2) return 'Create Account & Verify Email';
    if (step === 3) return 'Verify Email';
    return 'Create Account';
  };

  const handleButtonPress = () => {
    if (step === 3) {
      handleVerification();
    } else {
      handleNextStep();
    }
  };

  return (
    <View style={commonStyles.container}>
      {/* Split Layout - Blue section on left, content on right */}
      <View style={styles.mainContent}>
        {/* Left Side - Blue Gradient Section */}
        <View style={styles.leftSide}>
          <LinearGradient
            colors={colors.gradientPrimary}
            style={styles.leftSideGradient}
          >
            <View style={styles.leftSideContent}>
              {/* Logo and App Name */}
              <View style={styles.leftSideHeader}>
                <Text style={styles.leftSideTitle}>CostCompass</Text>
                <Text style={styles.leftSideSubtitle}>Create Account</Text>
              </View>

              {/* Welcome Message */}
              <View style={styles.leftSideInfo}>
                <Text style={styles.leftSideWelcome}>Welcome!</Text>
                <Text style={styles.leftSideDescription}>
                  Join thousands of care homes managing their costs effectively with CostCompass.
                </Text>
                <Text style={styles.leftSideFeature}>
                  ✓ 30-day free trial{'\n'}
                  ✓ Real-time cost tracking{'\n'}
                  ✓ Advanced reporting{'\n'}
                  ✓ Benchmark comparisons
                </Text>
              </View>
            </View>
          </LinearGradient>
        </View>

        {/* Right Side - Registration Form */}
        <View style={styles.rightSide}>
          <ScrollView
            contentContainerStyle={styles.rightSideScrollContainer}
            showsVerticalScrollIndicator={true}
          >
            <Card style={styles.formCard}>
              <Card.Content style={styles.cardContent}>
                {/* Back Button */}
                <View style={styles.backButtonContainer}>
                  <TouchableOpacity
                    style={styles.backButton}
                    onPress={handleGoBack}
                    disabled={loading}
                  >
                    <Ionicons name="arrow-back" size={24} color="#666" />
                  </TouchableOpacity>
                </View>

                <Text style={styles.formTitle}>{getStepTitle()}</Text>
                <Text style={styles.formSubtitle}>
                  {getStepSubtitle()}
                </Text>

                {/* Show error debugger if there's an error */}
                {lastError && (
                  <ClerkErrorDebugger
                    error={lastError}
                    context={step === 2 ? 'Sign Up' : 'Email Verification'}
                  />
                )}

                {/* Step Progress Indicator */}
                <View style={styles.stepIndicator}>
                  <View style={[styles.stepDot, step >= 1 && styles.stepDotActive]}>
                    <Text style={[styles.stepDotText, step >= 1 && styles.stepDotTextActive]}>1</Text>
                  </View>
                  <View style={[styles.stepLine, step >= 2 && styles.stepLineActive]} />
                  <View style={[styles.stepDot, step >= 2 && styles.stepDotActive]}>
                    <Text style={[styles.stepDotText, step >= 2 && styles.stepDotTextActive]}>2</Text>
                  </View>
                  <View style={[styles.stepLine, step >= 3 && styles.stepLineActive]} />
                  <View style={[styles.stepDot, step >= 3 && styles.stepDotActive]}>
                    <Text style={[styles.stepDotText, step >= 3 && styles.stepDotTextActive]}>3</Text>
                  </View>
                </View>

                {/* Dynamic Step Content */}
                {renderStepContent()}

                <Button
                  mode="contained"
                  onPress={handleButtonPress}
                  style={styles.createAccountButton}
                  disabled={loading}
                >
                  {loading ? (
                    <ActivityIndicator color="white" size="small" />
                  ) : (
                    getButtonText()
                  )}
                </Button>

                <View style={styles.loginSection}>
                  <Text variant="bodyMedium" style={styles.loginText}>
                    Already have an account?
                  </Text>
                  <Button mode="text" onPress={navigateToSignIn} disabled={loading}>
                    Sign In
                  </Button>
                </View>
              </Card.Content>
            </Card>
          </ScrollView>
        </View>
      </View>

      {/* Error Dialog */}
      <Portal>
        <Dialog
          visible={errorDialogVisible}
          onDismiss={() => setErrorDialogVisible(false)}
          style={commonStyles.compactDialog}
        >
          <Dialog.Title style={commonStyles.compactDialogTitle}>{errorTitle}</Dialog.Title>
          <Dialog.Content style={commonStyles.compactDialogContent}>
            <Text variant="bodyMedium" style={commonStyles.compactDialogText}>{errorMessage}</Text>
          </Dialog.Content>
          <Dialog.Actions style={commonStyles.compactDialogActions}>
            <Button
              onPress={() => setErrorDialogVisible(false)}
              mode="contained"
              style={commonStyles.compactDialogButton}
              compact
            >
              OK
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      {/* Subscription and company setup moved to ClerkOnboardingScreen */}

      {/* Mobile Header - Only visible on mobile */}
      <View style={styles.mobileHeaderContainer}>
        <LinearGradient
          colors={colors.gradientPrimary}
          style={styles.mobileHeader}
        >
          {/* Mobile Back Button */}
          <TouchableOpacity
            style={styles.mobileBackButton}
            onPress={handleGoBack}
            disabled={loading}
          >
            <Ionicons
              name="arrow-back"
              size={24}
              color={colors.textOnPrimary}
            />
          </TouchableOpacity>

          <View style={styles.mobileHeaderContent}>
            <Text style={styles.mobileHeaderTitle}>CostCompass</Text>
            <Text style={styles.mobileHeaderSubtitle}>{getStepTitle()}</Text>
          </View>
        </LinearGradient>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  // Main Layout
  mainContent: {
    flex: 1,
    flexDirection: isDesktop ? "row" : "column",
  },

  // Left Side - Blue Section
  leftSide: {
    flex: isDesktop ? 0.4 : 0,
    minHeight: isDesktop ? "100vh" : 0,
    display: isDesktop ? "flex" : "none",
  },
  leftSideGradient: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.xxxxl,
  },
  leftSideContent: {
    maxWidth: 400,
    alignItems: "center",
  },
  leftSideHeader: {
    alignItems: "center",
    marginBottom: spacing.xxxxl,
  },
  leftSideTitle: {
    ...typography.h1,
    color: colors.textOnPrimary,
    fontWeight: "bold",
    marginBottom: spacing.sm,
    textAlign: "center",
  },
  leftSideSubtitle: {
    ...typography.h3,
    color: colors.textOnPrimary,
    opacity: 0.9,
    textAlign: "center",
  },
  leftSideInfo: {
    alignItems: "center",
  },
  leftSideWelcome: {
    ...typography.h2,
    color: colors.textOnPrimary,
    fontWeight: "bold",
    marginBottom: spacing.lg,
    textAlign: "center",
  },
  leftSideDescription: {
    ...typography.body1,
    color: colors.textOnPrimary,
    opacity: 0.9,
    textAlign: "center",
    marginBottom: spacing.xl,
    lineHeight: 24,
  },
  leftSideFeature: {
    ...typography.body1,
    color: colors.textOnPrimary,
    opacity: 0.9,
    textAlign: "left",
    lineHeight: 28,
  },

  // Right Side - Form
  rightSide: {
    flex: isDesktop ? 0.6 : 1,
    backgroundColor: colors.background,
    justifyContent: "center",
    alignItems: "center",
  },
  rightSideScrollContainer: {
    flexGrow: 1,
    justifyContent: "center",
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.xl,
    width: "100%",
    maxWidth: 500,
  },

  // Form Card
  formCard: {
    margin: 0,
    marginBottom: spacing.lg,
    backgroundColor: colors.surface,
    borderRadius: borderRadius.xl,
    elevation: 2,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    width: "100%",
  },
  cardContent: {
    padding: spacing.xl,
    alignItems: "stretch",
    width: "100%",
  },

  // Back Button
  backButtonContainer: {
    marginBottom: spacing.lg,
  },
  backButton: {
    alignSelf: "flex-start",
    padding: spacing.sm,
    borderRadius: borderRadius.md,
  },

  // Form Elements
  formTitle: {
    ...typography.h2,
    color: colors.textPrimary,
    fontWeight: "bold",
    marginBottom: spacing.sm,
    textAlign: "center",
  },
  formSubtitle: {
    ...typography.body1,
    color: colors.textSecondary,
    textAlign: "center",
    marginBottom: spacing.xl,
  },
  nameRow: {
    flexDirection: "row",
    gap: spacing.md,
    marginBottom: spacing.md,
  },
  nameInput: {
    flex: 1,
    backgroundColor: colors.surface,
  },
  input: {
    marginBottom: spacing.md,
    backgroundColor: colors.surface,
    width: "100%",
  },

  // Step Indicator
  stepIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.xl,
    paddingHorizontal: spacing.lg,
  },
  stepDot: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#e1e5e9',
    justifyContent: 'center',
    alignItems: 'center',
  },
  stepDotActive: {
    backgroundColor: colors.primary,
  },
  stepDotText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#666',
  },
  stepDotTextActive: {
    color: 'white',
  },
  stepLine: {
    flex: 1,
    height: 2,
    backgroundColor: '#e1e5e9',
    marginHorizontal: spacing.sm,
  },
  stepLineActive: {
    backgroundColor: colors.primary,
  },

  // Company form specific styles
  capacityRow: {
    marginBottom: spacing.md,
  },
  capacityInput: {
    marginBottom: spacing.sm,
  },
  capacityTypeContainer: {
    marginBottom: 0,
  },
  capacityTypeLabel: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  segmentedButtons: {
    marginBottom: 0,
  },

  // Verification specific styles
  verificationNote: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: spacing.sm,
    lineHeight: 20,
  },
  nextStepNote: {
    fontSize: 14,
    color: '#4CAF50',
    textAlign: 'center',
    marginTop: 12,
    lineHeight: 20,
    fontWeight: '500',
  },
  skipButton: {
    backgroundColor: '#4CAF50',
    marginTop: 16,
  },
  skipButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
  },

  createAccountButton: {
    marginTop: spacing.lg,
    backgroundColor: colors.primary,
    borderRadius: borderRadius.lg,
    paddingVertical: spacing.sm,
  },
  loginSection: {
    marginTop: spacing.xl,
    alignItems: "center",
  },
  loginText: {
    color: colors.textSecondary,
    textAlign: "center",
    marginBottom: spacing.xs,
  },

  // Mobile Header
  mobileHeaderContainer: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
    display: isDesktop ? "none" : "flex",
  },
  mobileHeader: {
    paddingTop: spacing.xxxxl,
    paddingBottom: spacing.lg,
    paddingHorizontal: spacing.lg,
    position: "relative",
  },
  mobileBackButton: {
    position: "absolute",
    top: spacing.xxxxl + spacing.sm,
    left: spacing.lg,
    zIndex: 2,
    padding: spacing.sm,
    borderRadius: borderRadius.md,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
  },
  mobileHeaderContent: {
    alignItems: "center",
    marginTop: spacing.xl,
  },
  mobileHeaderTitle: {
    ...typography.h2,
    color: colors.textOnPrimary,
    fontWeight: "bold",
    textAlign: "center",
  },
  mobileHeaderSubtitle: {
    ...typography.body1,
    color: colors.textOnPrimary,
    opacity: 0.9,
    textAlign: "center",
    marginTop: spacing.xs,
  },
});

export default ClerkSignUpScreen;
