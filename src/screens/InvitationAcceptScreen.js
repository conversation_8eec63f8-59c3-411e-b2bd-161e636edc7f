import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Platform, Dimensions, Image } from 'react-native';
import {
  Text,
  Card,
  Button,
  ActivityIndicator,
  Chip
} from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { colors, spacing, borderRadius, commonStyles, typography, elevation } from '../styles/theme';
import { useClerkAuth } from '../contexts/ClerkAuthContext';
import DatabaseService from '../services/database';

const { width } = Dimensions.get("window");

const InvitationAcceptScreen = ({ route, navigation }) => {
  const { token } = route.params;
  const { user, login } = useAuth();
  const [invitation, setInvitation] = useState(null);
  const [company, setCompany] = useState(null);
  const [loading, setLoading] = useState(true);
  const [accepting, setAccepting] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadInvitation();
  }, []);

  const loadInvitation = async () => {
    try {
      setLoading(true);
      const invitationData = await DatabaseService.getInvitationByToken(token);
      
      if (!invitationData) {
        setError('Invitation not found or invalid');
        return;
      }

      if (invitationData.status === 'expired') {
        setError('This invitation has expired');
        return;
      }

      if (invitationData.status === 'accepted') {
        setError('This invitation has already been accepted');
        return;
      }

      setInvitation(invitationData);

      // Load company details
      const companyData = await DatabaseService.getCompanyById(invitationData.company_id);
      setCompany(companyData);

    } catch (error) {
      console.error('Error loading invitation:', error);
      setError('Failed to load invitation details');
    } finally {
      setLoading(false);
    }
  };

  const handleAcceptInvitation = async () => {
    try {
      setAccepting(true);

      // Accept the invitation
      const result = await DatabaseService.acceptInvitation(token);
      
      if (result.success) {
        // Refresh user's companies by re-logging in
        if (user) {
          // User is already logged in, just refresh their data
          const userCompanies = await DatabaseService.getUserCompanies(user.id);
          // You might want to update the auth context here
          
          navigation.navigate('CompanySelection');
        } else {
          // User needs to log in first
          navigation.navigate('Login', { 
            message: 'Please log in to access your new company' 
          });
        }
      }
    } catch (error) {
      console.error('Error accepting invitation:', error);
      setError('Failed to accept invitation: ' + error.message);
    } finally {
      setAccepting(false);
    }
  };

  const handleDecline = () => {
    navigation.goBack();
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
        <Text variant="bodyLarge" style={styles.loadingText}>
          Loading invitation...
        </Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.container}>
        <Card style={styles.errorCard}>
          <Card.Content>
            <Text variant="headlineSmall" style={styles.errorTitle}>
              Invitation Error
            </Text>
            <Text variant="bodyLarge" style={styles.errorText}>
              {error}
            </Text>
            <Button
              mode="contained"
              onPress={() => navigation.navigate('Login')}
              style={styles.button}
            >
              Go to Login
            </Button>
          </Card.Content>
        </Card>
      </View>
    );
  }

  return (
    <View style={commonStyles.container}>
      {/* Split Layout - Blue section on left, content on right */}
      <View style={styles.mainContent}>
        {/* Left Side - Blue Header Section (Desktop only) */}
        {Platform.OS === 'web' && width > 600 && (
          <View style={styles.leftSide}>
            <LinearGradient
              colors={colors.gradientPrimary}
              style={styles.leftSideGradient}
            >
              <View style={styles.leftSideContent}>
                {/* App Logo and Name */}
                <View style={styles.logoSection}>
                  <View style={styles.appLogoContainer}>
                    <Image
                      source={require("../../assets/logo.png")}
                      style={styles.appLogo}
                      resizeMode="contain"
                    />
                  </View>
                  <Text style={styles.appNameBig}>CostCompass</Text>
                </View>

                {/* Page Header */}
                <View style={styles.pageHeader}>
                  <View style={styles.headerIconContainer}>
                    <Ionicons
                      name="mail-open"
                      size={32}
                      color={colors.textOnPrimary}
                    />
                  </View>
                  <Text variant="headlineMedium" style={styles.leftSideTitle}>
                    Company Invitation
                  </Text>
                  <Text variant="bodyMedium" style={styles.leftSideSubtitle}>
                    You've been invited to join a company
                  </Text>
                </View>

                {/* Invitation Info */}
                {invitation && company && (
                  <View style={styles.leftSideInfo}>
                    <View style={styles.leftSideInfoItem}>
                      <Ionicons name="business" size={20} color={colors.textOnPrimary} />
                      <Text style={styles.leftSideInfoText}>
                        {company.name}
                      </Text>
                    </View>
                    <View style={styles.leftSideInfoItem}>
                      <Ionicons name="mail" size={20} color={colors.textOnPrimary} />
                      <Text style={styles.leftSideInfoText}>
                        {invitation.email}
                      </Text>
                    </View>
                  </View>
                )}
              </View>
            </LinearGradient>
          </View>
        )}

        {/* Right Side - Main Content */}
        <View style={styles.rightSide}>
          {/* Mobile Header (only shown on mobile) */}
          {(Platform.OS !== 'web' || width <= 600) && (
            <LinearGradient
              colors={colors.gradientPrimary}
              style={styles.mobileHeader}
            >
              <View style={styles.mobileHeaderContent}>
                <View style={styles.headerIconContainer}>
                  <Ionicons
                    name="mail-open"
                    size={32}
                    color={colors.textOnPrimary}
                  />
                </View>
                <Text variant="headlineMedium" style={styles.mobileHeaderTitle}>
                  Company Invitation
                </Text>
                <Text variant="bodyMedium" style={styles.mobileHeaderSubtitle}>
                  You've been invited to join a company
                </Text>
              </View>
            </LinearGradient>
          )}

          <View style={styles.content}>
        <Card style={commonStyles.cardLarge}>
          <Card.Content>
          
          <Text variant="bodyLarge" style={styles.description}>
            You've been invited to join:
          </Text>

          <Card style={styles.companyCard}>
            <Card.Content>
              <Text variant="titleLarge" style={styles.companyName}>
                {company?.name}
              </Text>
              {company?.registration && (
                <Text variant="bodyMedium" style={styles.companyDetail}>
                  Registration: {company.registration}
                </Text>
              )}
              <Text variant="bodyMedium" style={styles.companyDetail}>
                Capacity: {company?.current_occupancy}/{company?.capacity} {company?.capacity_type}
              </Text>
              
              <View style={styles.invitationDetails}>
                <Text variant="bodySmall" style={styles.invitationLabel}>
                  Invitation Details:
                </Text>
                <Text variant="bodySmall" style={styles.invitationInfo}>
                  Email: {invitation?.email}
                </Text>
                <Text variant="bodySmall" style={styles.invitationInfo}>
                  Expires: {invitation?.expires_at ? new Date(invitation.expires_at).toLocaleDateString() : 'N/A'}
                </Text>
                <Chip icon="account" style={styles.roleChip}>
                  Role: User
                </Chip>
              </View>
            </Card.Content>
          </Card>

          <Text variant="bodyMedium" style={styles.acceptanceText}>
            By accepting this invitation, you'll be able to access this company's data and features based on your assigned permissions.
          </Text>

          <View style={styles.buttonContainer}>
            <Button
              mode="outlined"
              onPress={handleDecline}
              style={[styles.button, styles.declineButton]}
              disabled={accepting}
            >
              Decline
            </Button>
            <Button
              mode="contained"
              onPress={handleAcceptInvitation}
              style={[styles.button, styles.acceptButton]}
              loading={accepting}
              disabled={accepting}
            >
              Accept Invitation
            </Button>
          </View>

          {!user && (
            <Card style={styles.loginPrompt}>
              <Card.Content>
                <Text variant="bodyMedium" style={styles.loginPromptText}>
                  You need to be logged in to accept this invitation. Please log in with the email address: {invitation?.email}
                </Text>
                <Button
                  mode="text"
                  onPress={() => navigation.navigate('Login')}
                  style={styles.loginButton}
                >
                  Go to Login
                </Button>
              </Card.Content>
            </Card>
          )}
          </Card.Content>
        </Card>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  // Split Layout Styles
  mainContent: {
    flex: 1,
    flexDirection: Platform.OS === 'web' && width > 600 ? 'row' : 'column',
  },
  // Left Side - Blue Section (Desktop only)
  leftSide: {
    flex: 0.4,
    minWidth: 350,
    maxWidth: 450,
  },
  leftSideGradient: {
    flex: 1,
    padding: spacing.lg,
    paddingTop: spacing.md,
    justifyContent: 'flex-start',
  },
  leftSideContent: {
    alignItems: 'flex-start',
    maxWidth: 350,
    alignSelf: 'center',
    width: '100%',
  },
  // Logo Section
  logoSection: {
    alignItems: 'flex-start',
    alignSelf: 'flex-start',
    marginBottom: spacing.xl,
    paddingBottom: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.2)',
    width: '100%',
  },
  appLogoContainer: {
    width: '100%', // Full width of the blue sidebar content area
    height: 120, // Taller to accommodate larger logo
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.lg,
    marginTop: spacing.sm,
    // Remove artificial shadows and borders for more natural integration
  },
  appLogo: {
    width: 280, // Almost full width of the blue sidebar content
    height: 100, // Proportional height
    // Remove tintColor to show original logo colors
  },
  appNameBig: {
    color: colors.textOnPrimary,
    fontSize: 32,
    fontWeight: typography.fontWeights.bold,
    textAlign: 'left',
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
    letterSpacing: 0.5,
  },
  // Page Header Section
  pageHeader: {
    alignItems: 'flex-start',
    marginBottom: spacing.lg,
    width: '100%',
  },
  leftSideTitle: {
    color: colors.textOnPrimary,
    fontSize: 24,
    fontWeight: typography.fontWeights.bold,
    textAlign: 'left',
    marginBottom: spacing.sm,
    textShadowColor: 'rgba(0,0,0,0.1)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  leftSideSubtitle: {
    color: colors.textOnPrimary,
    fontSize: 16,
    textAlign: 'left',
    opacity: 0.95,
    marginBottom: spacing.lg,
    textShadowColor: 'rgba(0,0,0,0.1)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  leftSideInfo: {
    marginTop: spacing.lg,
    alignSelf: 'stretch',
  },
  leftSideInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    padding: spacing.md,
    borderRadius: borderRadius.lg,
  },
  leftSideInfoText: {
    color: colors.textOnPrimary,
    fontSize: 14,
    fontWeight: '500',
    marginLeft: spacing.sm,
    flex: 1,
  },
  // Right Side - Main Content
  rightSide: {
    flex: 1.6,
    backgroundColor: colors.background,
  },
  // Mobile Header
  mobileHeader: {
    paddingTop: spacing.xxxxl,
    paddingBottom: spacing.xl,
    paddingHorizontal: spacing.xl,
    borderBottomLeftRadius: borderRadius.xxl,
    borderBottomRightRadius: borderRadius.xxl,
  },
  mobileHeaderContent: {
    alignItems: 'center',
  },
  mobileHeaderTitle: {
    color: colors.textOnPrimary,
    fontSize: 24,
    fontWeight: typography.fontWeights.bold,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  mobileHeaderSubtitle: {
    color: colors.textOnPrimary,
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.95,
  },
  headerIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
    elevation: elevation.sm,
    shadowColor: 'rgba(0, 0, 0, 0.3)',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  content: {
    flex: 1,
    padding: spacing.lg,
    justifyContent: 'center',
    paddingBottom: spacing.xxxl,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: spacing.md,
    color: colors.textSecondary,
  },
  errorCard: {
    maxWidth: 400,
    alignSelf: 'center',
    width: '100%',
    borderRadius: borderRadius.lg,
    elevation: elevation.lg,
  },
  errorTitle: {
    textAlign: 'center',
    marginBottom: spacing.md,
    color: colors.error,
    fontWeight: typography.fontWeights.bold,
    fontSize: typography.fontSizes.xl,
  },
  description: {
    textAlign: 'center',
    marginBottom: spacing.md,
    color: colors.text,
    fontSize: typography.fontSizes.lg,
  },
  errorText: {
    textAlign: 'center',
    marginBottom: spacing.lg,
    color: colors.textSecondary,
  },
  companyCard: {
    marginBottom: spacing.md,
    backgroundColor: colors.primaryLight + '33',
    borderRadius: borderRadius.md,
    elevation: elevation.sm,
  },
  companyName: {
    fontWeight: typography.fontWeights.bold,
    color: colors.primary,
    marginBottom: spacing.sm,
    fontSize: typography.fontSizes.xl,
  },
  companyDetail: {
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  invitationDetails: {
    marginTop: spacing.md,
    paddingTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  invitationLabel: {
    fontWeight: typography.fontWeights.bold,
    color: colors.text,
    marginBottom: spacing.sm,
  },
  invitationInfo: {
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  roleChip: {
    alignSelf: 'flex-start',
    marginTop: spacing.sm,
    backgroundColor: colors.success + '33',
  },
  acceptanceText: {
    textAlign: 'center',
    color: colors.textSecondary,
    marginBottom: spacing.lg,
    lineHeight: 20,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: spacing.sm,
    marginBottom: spacing.md,
  },
  button: {
    flex: 1,
    borderRadius: borderRadius.sm,
  },
  declineButton: {
    borderColor: colors.error,
  },
  acceptButton: {
    backgroundColor: colors.success,
  },
  loginPrompt: {
    backgroundColor: colors.warning + '33',
    borderColor: colors.warning,
    borderWidth: 1,
    borderRadius: borderRadius.md,
    elevation: elevation.sm,
  },
  loginPromptText: {
    color: colors.accentDark,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  loginButton: {
    alignSelf: 'center',
  },
});

export default InvitationAcceptScreen;
