import React, { useState, useEffect } from "react";
import {
  View,
  StyleSheet,
  ScrollView,
  Platform,
  Alert,
  TouchableOpacity,
  Dimensions,
  Image,
} from "react-native";
import {
  Text,
  Card,
  DataTable,
  ActivityIndicator,
  Button,
  IconButton,
  Portal,
  Modal,
  TextInput,
  SegmentedButtons,
  Divider,
  Surface,
} from "react-native-paper";
import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from 'expo-linear-gradient';
import { colors, spacing, borderRadius, commonStyles, typography, elevation } from '../styles/theme';
import DatabaseService from "../services/database";
import { useAuth } from "../contexts/AuthContext";
import DateTimePicker from "@react-native-community/datetimepicker";
import * as FileSystem from "expo-file-system";
import * as Sharing from "expo-sharing";
import * as XLSX from "xlsx";
import * as DocumentPicker from "expo-document-picker";

const { width } = Dimensions.get("window");

// Custom DateInput component
const DateInput = ({
  label,
  value,
  onPress,
  onChangeText,
  disabled,
  placeholder,
}) => {
  if (Platform.OS === "web") {
    return (
      <View style={{ marginBottom: spacing.lg }}>
        <Text style={{
          marginBottom: spacing.sm,
          color: colors.primary,
          fontSize: typography.fontSizes.sm,
          fontWeight: typography.fontWeights.medium
        }}>
          {label}
        </Text>
        <View
          style={{
            borderWidth: 2,
            borderColor: colors.primary,
            borderRadius: borderRadius.md,
            backgroundColor: disabled ? colors.surfaceDisabled : colors.surface,
            flexDirection: "row",
            alignItems: "center",
            paddingLeft: spacing.md,
            minHeight: 56,
            elevation: elevation.xs,
            shadowColor: colors.primary,
            shadowOffset: { width: 0, height: 1 },
            shadowOpacity: 0.1,
            shadowRadius: 2,
          }}
        >
          <Ionicons
            name="calendar"
            size={20}
            color={colors.primary}
            style={{ marginRight: spacing.sm }}
          />
          <input
            type="date"
            value={value}
            onChange={(e) => onChangeText && onChangeText(e.target.value)}
            disabled={disabled}
            placeholder={placeholder}
            style={{
              border: "none",
              outline: "none",
              background: "transparent",
              fontSize: "16px",
              flex: 1,
              padding: "12px 8px",
              color: disabled ? colors.textDisabled : colors.text,
            }}
          />
        </View>
      </View>
    );
  }

  return (
    <TouchableOpacity onPress={disabled ? null : onPress} disabled={disabled}>
      <TextInput
        label={label}
        value={value}
        mode="outlined"
        style={{ marginBottom: spacing.lg }}
        disabled={true}
        placeholder={placeholder}
        outlineColor={colors.primary}
        activeOutlineColor={colors.primary}
        textColor={colors.text}
        left={
          <TextInput.Icon
            icon="calendar"
            iconColor={colors.primary}
            onPress={disabled ? null : onPress}
          />
        }
        pointerEvents="none"
      />
    </TouchableOpacity>
  );
};

const HistoricalDataScreen = ({ route, navigation }) => {
  const { benchmarkId } = route.params || {};
  const { user, company } = useAuth();

  const [loading, setLoading] = useState(true);
  const [benchmark, setBenchmark] = useState(null);
  const [actualSpends, setActualSpends] = useState([]);
  const [showDownloadModal, setShowDownloadModal] = useState(false);
  const [downloadStartDate, setDownloadStartDate] = useState("");
  const [downloadEndDate, setDownloadEndDate] = useState("");
  const [downloadFormat, setDownloadFormat] = useState("excel");
  const [isDownloading, setIsDownloading] = useState(false);
  const [showStartPicker, setShowStartPicker] = useState(false);
  const [showEndPicker, setShowEndPicker] = useState(false);

  // Plan limits state
  const [canExportData, setCanExportData] = useState(false);
  const [canUploadData, setCanUploadData] = useState(false);

  // Upload functionality state
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  // Delete functionality state
  const [deletingEntryId, setDeletingEntryId] = useState(null);

  useEffect(() => {
    loadData();
  }, [benchmarkId]);

  // Check plan limits for data export and upload
  useEffect(() => {
    const checkPermissions = async () => {
      if (company?.id) {
        try {
          const exportLimitCheck = await DatabaseService.checkPlanLimits(company.id, 'exportData');
          setCanExportData(exportLimitCheck.allowed);

          const uploadLimitCheck = await DatabaseService.checkPlanLimits(company.id, 'uploadHistoricalData');
          setCanUploadData(uploadLimitCheck.allowed);
        } catch (error) {
          console.error('Error checking permissions:', error);
          setCanExportData(false);
          setCanUploadData(false);
        }
      }
    };

    checkPermissions();
  }, [company?.id]);

  const loadData = async () => {
    try {
      setLoading(true);

      if (benchmarkId) {
        // Load specific benchmark data
        const benchmarkData =
          await DatabaseService.getBenchmarkById(benchmarkId);
        setBenchmark(benchmarkData);

        // Load all actual spends for this benchmark
        const spendsData =
          await DatabaseService.getActualSpendsByBenchmark(benchmarkId);
        setActualSpends(spendsData);
      } else {
        // Load all benchmarks for the company if no specific benchmark
        const benchmarks = await DatabaseService.getBenchmarksByCompany(
          company.id,
        );
        // For now, we'll focus on the specific benchmark case
        // Could be extended to show all company data
      }
    } catch (error) {
      console.error("Error loading historical data:", error);
      Alert.alert("Error", "Failed to load historical data");
    } finally {
      setLoading(false);
    }
  };

  // Utility functions
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatCurrency = (amount, currency = "USD") => {
    const currencySymbols = {
      USD: "$",
      EUR: "€",
      GBP: "£",
      CAD: "C$",
      AUD: "A$",
      JPY: "¥",
      CHF: "CHF",
      CNY: "¥",
      INR: "₹",
    };
    const symbol = currencySymbols[currency] || "$";
    return `${symbol}${amount.toFixed(2)}`;
  };

  const getVarianceColor = (variance) => {
    if (variance > 0) return "#F44336"; // Red for over budget (keep red for errors)
    if (variance < 0) return "#2196F3"; // Blue for under budget
    return "#666"; // Gray for on budget
  };

  const getVarianceIcon = (variance) => {
    if (variance > 0) return "trending-up";
    if (variance < 0) return "trending-down";
    return "remove";
  };

  // Download functionality
  const handleDownloadData = async () => {
    if (!downloadStartDate || !downloadEndDate) {
      Alert.alert("Error", "Please select both start and end dates");
      return;
    }

    if (new Date(downloadStartDate) > new Date(downloadEndDate)) {
      Alert.alert("Error", "Start date must be before end date");
      return;
    }

    setIsDownloading(true);
    try {
      // Filter data by date range
      const filteredData = actualSpends.filter((spend) => {
        const spendDate = new Date(spend.period_start);
        const startDate = new Date(downloadStartDate);
        const endDate = new Date(downloadEndDate);
        return spendDate >= startDate && spendDate <= endDate;
      });

      if (filteredData.length === 0) {
        Alert.alert("No Data", "No data found for the selected date range");
        setIsDownloading(false);
        return;
      }

      if (downloadFormat === "excel") {
        await generateExcelReport(filteredData);
      } else {
        await generatePDFReport(filteredData);
      }

      setShowDownloadModal(false);
    } catch (error) {
      console.error("Download error:", error);
      Alert.alert("Error", "Failed to generate report. Please try again.");
    } finally {
      setIsDownloading(false);
    }
  };

  const generateExcelReport = async (data) => {
    try {
      // Prepare data for Excel
      const excelData = data.map((spend) => ({
        Date: formatDate(spend.period_start),
        "Period End":
          spend.period_start !== spend.period_end
            ? formatDate(spend.period_end)
            : "",
        Occupancy: spend.occupancy_at_time || 1,
        "Benchmark Amount": spend.benchmark_amount_at_time || benchmark.amount,
        "Actual Amount": spend.amount,
        Variance: spend.variance,
        Currency: benchmark.currency,
        "Posted By": spend.created_by_name || "Unknown",
        "Posted Date": formatDate(spend.created_at),
        "Period Type": spend.period_type,
      }));

      // Create workbook and worksheet
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.json_to_sheet(excelData);

      // Add some styling (column widths)
      const colWidths = [
        { wch: 12 }, // Date
        { wch: 12 }, // Period End
        { wch: 10 }, // Occupancy
        { wch: 15 }, // Benchmark Amount
        { wch: 15 }, // Actual Amount
        { wch: 12 }, // Variance
        { wch: 10 }, // Currency
        { wch: 15 }, // Posted By
        { wch: 12 }, // Posted Date
        { wch: 12 }, // Period Type
      ];
      ws["!cols"] = colWidths;

      // Add worksheet to workbook
      XLSX.utils.book_append_sheet(wb, ws, "Historical Data");

      // Generate file
      const wbout = XLSX.write(wb, { type: "base64", bookType: "xlsx" });
      const filename = `${benchmark.name}_${downloadStartDate}_to_${downloadEndDate}.xlsx`;
      const fileUri = FileSystem.documentDirectory + filename;

      await FileSystem.writeAsStringAsync(fileUri, wbout, {
        encoding: FileSystem.EncodingType.Base64,
      });

      // Share the file
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(fileUri);
      } else {
        Alert.alert("Success", `Excel file saved to: ${fileUri}`);
      }
    } catch (error) {
      console.error("Excel generation error:", error);
      throw error;
    }
  };

  const generatePDFReport = async (data) => {
    try {
      // Create HTML content for PDF
      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>Historical Data Report</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .benchmark-info { background-color: #f5f5f5; padding: 15px; margin-bottom: 20px; border-radius: 5px; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; font-weight: bold; }
            .currency { text-align: right; }
            .variance-positive { color: #d32f2f; }
            .variance-negative { color: #388e3c; }
            .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>Historical Data Report</h1>
            <h2>${benchmark.name}</h2>
          </div>

          <div class="benchmark-info">
            <p><strong>Benchmark:</strong> ${formatCurrency(benchmark.amount, benchmark.currency)} per ${benchmark.period}</p>
            <p><strong>Type:</strong> ${benchmark.type}</p>
            <p><strong>Report Period:</strong> ${formatDate(downloadStartDate)} to ${formatDate(downloadEndDate)}</p>
            <p><strong>Generated:</strong> ${new Date().toLocaleDateString()}</p>
          </div>

          <table>
            <thead>
              <tr>
                <th>Date</th>
                <th>Occupancy</th>
                <th>Benchmark</th>
                <th>Actual</th>
                <th>Variance</th>
                <th>Posted By</th>
                <th>Posted Date</th>
              </tr>
            </thead>
            <tbody>
              ${data
                .map(
                  (spend) => `
                <tr>
                  <td>${formatDate(spend.period_start)}${spend.period_start !== spend.period_end ? ` - ${formatDate(spend.period_end)}` : ""}</td>
                  <td style="text-align: center;">${spend.occupancy_at_time || 1}</td>
                  <td class="currency">${formatCurrency(spend.benchmark_amount_at_time || benchmark.amount, benchmark.currency)}</td>
                  <td class="currency">${formatCurrency(spend.amount, benchmark.currency)}</td>
                  <td class="currency ${spend.variance >= 0 ? "variance-positive" : "variance-negative"}">
                    ${spend.variance >= 0 ? "+" : ""}${formatCurrency(spend.variance, benchmark.currency)}
                  </td>
                  <td>${spend.created_by_name || "Unknown"}</td>
                  <td>${formatDate(spend.created_at)}</td>
                </tr>
              `,
                )
                .join("")}
            </tbody>
          </table>

          <div class="footer">
            <p>Generated by CostCompass - Budget Management System</p>
          </div>
        </body>
        </html>
      `;

      // For web platform, create and download HTML file
      if (Platform.OS === "web") {
        const blob = new Blob([htmlContent], { type: "text/html" });
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `${benchmark.name}_${downloadStartDate}_to_${downloadEndDate}.html`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
        Alert.alert("Success", "Report downloaded successfully");
      } else {
        // For mobile, we'll save as HTML since PDF generation is complex
        const filename = `${benchmark.name}_${downloadStartDate}_to_${downloadEndDate}.html`;
        const fileUri = FileSystem.documentDirectory + filename;

        await FileSystem.writeAsStringAsync(fileUri, htmlContent);

        if (await Sharing.isAvailableAsync()) {
          await Sharing.shareAsync(fileUri);
        } else {
          Alert.alert("Success", `Report saved to: ${fileUri}`);
        }
      }
    } catch (error) {
      console.error("PDF generation error:", error);
      throw error;
    }
  };

  // Date picker handlers
  const handleStartDateChange = (event, selectedDate) => {
    setShowStartPicker(Platform.OS === "ios");
    if (selectedDate) {
      const dateString = selectedDate.toISOString().split("T")[0];
      setDownloadStartDate(dateString);
    }
  };

  const handleEndDateChange = (event, selectedDate) => {
    setShowEndPicker(Platform.OS === "ios");
    if (selectedDate) {
      const dateString = selectedDate.toISOString().split("T")[0];
      setDownloadEndDate(dateString);
    }
  };

  const showStartDatePickerModal = () => {
    setShowStartPicker(true);
  };

  const showEndDatePickerModal = () => {
    setShowEndPicker(true);
  };

  // Upload functionality
  const generateTemplate = async () => {
    try {
      // Create template data with sample row
      const templateData = [
        {
          "Date (YYYY-MM-DD)": "2024-01-15",
          "Period End (YYYY-MM-DD)": "2024-01-15",
          "Benchmark Amount": benchmark.amount.toString(),
          "Actual Amount": "25.50",
          "Period Type": "daily",
          "Occupancy": "42"
        },
        {
          "Date (YYYY-MM-DD)": "Example: 2024-01-16",
          "Period End (YYYY-MM-DD)": "Leave blank for daily periods",
          "Benchmark Amount": "Historical benchmark amount",
          "Actual Amount": "Example: 30.00",
          "Period Type": "daily, weekly, or monthly",
          "Occupancy": "Number of occupants"
        }
      ];

      // Create workbook and worksheet
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.json_to_sheet(templateData);

      // Set column widths
      const colWidths = [
        { wch: 18 }, // Date
        { wch: 20 }, // Period End
        { wch: 18 }, // Benchmark Amount
        { wch: 15 }, // Actual Amount
        { wch: 18 }, // Period Type
        { wch: 12 }  // Occupancy
      ];
      ws["!cols"] = colWidths;

      // Add worksheet to workbook
      XLSX.utils.book_append_sheet(wb, ws, "Historical Data Template");

      // Generate file
      const wbout = XLSX.write(wb, { type: "base64", bookType: "xlsx" });
      const filename = `${benchmark.name}_Upload_Template.xlsx`;

      // For web platform, create and download file
      if (Platform.OS === "web") {
        const blob = new Blob([XLSX.write(wb, { type: "array", bookType: "xlsx" })], {
          type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        });
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
        Alert.alert("Success", "Template downloaded successfully");
      } else {
        // For mobile platforms
        const fileUri = FileSystem.documentDirectory + filename;
        await FileSystem.writeAsStringAsync(fileUri, wbout, {
          encoding: FileSystem.EncodingType.Base64,
        });

        // Share the file
        if (await Sharing.isAvailableAsync()) {
          await Sharing.shareAsync(fileUri);
        } else {
          Alert.alert("Success", `Template saved to: ${fileUri}`);
        }
      }
    } catch (error) {
      console.error("Template generation error:", error);
      Alert.alert("Error", "Failed to generate template. Please try again.");
    }
  };

  const handleFileUpload = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const file = result.assets[0];
        await processUploadedFile(file);
      }
    } catch (error) {
      console.error("File upload error:", error);
      Alert.alert("Error", "Failed to upload file. Please try again.");
    }
  };

  const processUploadedFile = async (file) => {
    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Read the file
      const fileContent = await FileSystem.readAsStringAsync(file.uri, {
        encoding: FileSystem.EncodingType.Base64,
      });

      // Parse Excel file
      const workbook = XLSX.read(fileContent, { type: "base64" });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const data = XLSX.utils.sheet_to_json(worksheet);

      if (data.length === 0) {
        Alert.alert("Error", "The uploaded file appears to be empty.");
        return;
      }

      // Validate and process data
      const validEntries = [];
      const errors = [];

      for (let i = 0; i < data.length; i++) {
        const row = data[i];
        const rowNum = i + 2; // Excel row number (accounting for header)

        // Skip example rows
        if (row["Date (YYYY-MM-DD)"]?.toString().toLowerCase().includes("example")) {
          continue;
        }

        const validation = validateUploadRow(row, rowNum);
        if (validation.isValid) {
          validEntries.push(validation.data);
        } else {
          errors.push(`Row ${rowNum}: ${validation.error}`);
        }
      }

      if (errors.length > 0 && validEntries.length === 0) {
        Alert.alert("Upload Failed", `Please fix the following errors:\n\n${errors.slice(0, 5).join('\n')}${errors.length > 5 ? '\n...and more' : ''}`);
        return;
      }

      if (validEntries.length === 0) {
        Alert.alert("No Data", "No valid data rows found in the uploaded file.");
        return;
      }

      // Show confirmation with summary
      const confirmMessage = `Found ${validEntries.length} valid entries to upload${errors.length > 0 ? ` (${errors.length} rows had errors and will be skipped)` : ''}.`;

      Alert.alert(
        "Confirm Upload",
        confirmMessage,
        [
          { text: "Cancel", style: "cancel" },
          { text: "Upload", onPress: () => uploadValidEntries(validEntries) }
        ]
      );

    } catch (error) {
      console.error("File processing error:", error);
      Alert.alert("Error", "Failed to process the uploaded file. Please ensure it's a valid Excel file.");
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const validateUploadRow = (row, rowNum) => {
    const dateStr = row["Date (YYYY-MM-DD)"]?.toString().trim();
    const periodEndStr = row["Period End (YYYY-MM-DD)"]?.toString().trim();
    const benchmarkAmountStr = row["Benchmark Amount"]?.toString().trim();
    const amountStr = row["Actual Amount"]?.toString().trim();
    const periodType = row["Period Type"]?.toString().trim().toLowerCase();
    const occupancyStr = row["Occupancy"]?.toString().trim();

    // Validate required fields
    if (!dateStr) {
      return { isValid: false, error: "Date is required" };
    }

    if (!benchmarkAmountStr) {
      return { isValid: false, error: "Benchmark Amount is required" };
    }

    if (!amountStr) {
      return { isValid: false, error: "Actual Amount is required" };
    }

    if (!periodType) {
      return { isValid: false, error: "Period Type is required" };
    }

    // Validate date format
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(dateStr)) {
      return { isValid: false, error: "Date must be in YYYY-MM-DD format" };
    }

    const startDate = new Date(dateStr);
    if (isNaN(startDate.getTime())) {
      return { isValid: false, error: "Invalid date" };
    }

    // Validate period end date if provided
    let endDate = startDate;
    if (periodEndStr && periodEndStr !== "") {
      if (!dateRegex.test(periodEndStr)) {
        return { isValid: false, error: "Period End must be in YYYY-MM-DD format" };
      }
      endDate = new Date(periodEndStr);
      if (isNaN(endDate.getTime())) {
        return { isValid: false, error: "Invalid period end date" };
      }
      if (endDate < startDate) {
        return { isValid: false, error: "Period End cannot be before start date" };
      }
    }

    // Validate benchmark amount
    const benchmarkAmount = parseFloat(benchmarkAmountStr);
    if (isNaN(benchmarkAmount) || benchmarkAmount < 0) {
      return { isValid: false, error: "Benchmark Amount must be a positive number" };
    }

    // Validate actual amount
    const amount = parseFloat(amountStr);
    if (isNaN(amount) || amount < 0) {
      return { isValid: false, error: "Actual Amount must be a positive number" };
    }

    // Validate period type
    if (!["daily", "weekly", "monthly"].includes(periodType)) {
      return { isValid: false, error: "Period Type must be 'daily', 'weekly', or 'monthly'" };
    }

    // Validate occupancy
    let occupancy = 1;
    if (occupancyStr && occupancyStr !== "") {
      occupancy = parseInt(occupancyStr);
      if (isNaN(occupancy) || occupancy < 1) {
        return { isValid: false, error: "Occupancy must be a positive number" };
      }
    }

    return {
      isValid: true,
      data: {
        periodStart: dateStr,
        periodEnd: periodEndStr || dateStr,
        benchmarkAmount: benchmarkAmount,
        amount: amount,
        periodType: periodType,
        occupancy: occupancy
      }
    };
  };

  const uploadValidEntries = async (entries) => {
    setIsUploading(true);
    let successCount = 0;
    let errorCount = 0;

    try {
      for (let i = 0; i < entries.length; i++) {
        const entry = entries[i];
        setUploadProgress(Math.round((i / entries.length) * 100));

        try {
          await DatabaseService.createActualSpend({
            benchmarkId: benchmark.id,
            amount: entry.amount,
            periodStart: entry.periodStart,
            periodEnd: entry.periodEnd,
            periodType: entry.periodType,
            benchmarkAmountAtTime: entry.benchmarkAmount,
            occupancyAtTime: entry.occupancy,
            createdBy: user.id
          });
          successCount++;
        } catch (error) {
          console.error(`Error uploading entry ${i + 1}:`, error);
          errorCount++;
        }
      }

      // Reload data
      await loadData();
      setShowUploadModal(false);

      // Show success message
      const message = errorCount > 0
        ? `Upload completed: ${successCount} entries uploaded successfully, ${errorCount} failed. Charts and reports have been updated with the new data.`
        : `Upload completed successfully! ${successCount} entries uploaded. Charts and reports have been updated with the historical data.`;

      Alert.alert("Upload Complete", message);

    } catch (error) {
      console.error("Upload error:", error);
      Alert.alert("Error", "Failed to upload data. Please try again.");
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };



  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
        <Text variant="bodyMedium" style={styles.loadingText}>
          Loading historical data...
        </Text>
      </View>
    );
  }

  if (!benchmark) {
    return (
      <View style={styles.container}>
        <Card style={styles.card}>
          <Card.Content>
            <Text variant="headlineSmall">No Data Available</Text>
            <Text variant="bodyMedium">
              No benchmark data found. Please go back and try again.
            </Text>
            <Button
              mode="outlined"
              onPress={() => navigation.goBack()}
              style={styles.backButton}
            >
              Go Back
            </Button>
          </Card.Content>
        </Card>
      </View>
    );
  }

  return (
    <View style={commonStyles.container}>
      {/* Split Layout - Blue section on left, content on right */}
      <View style={styles.mainContent}>
        {/* Left Side - Blue Header Section (Desktop only) */}
        {Platform.OS === 'web' && width > 600 && (
          <View style={styles.leftSide}>
            <LinearGradient
              colors={colors.gradientPrimary}
              style={styles.leftSideGradient}
            >
              <View style={styles.leftSideContent}>
                {/* App Logo and Name */}
                <View style={styles.logoSection}>
                  <View style={styles.appLogoContainer}>
                    <Image
                      source={require("../../assets/logo.png")}
                      style={styles.appLogo}
                      resizeMode="contain"
                      onError={(error) => console.log('Historical Data logo load error:', error)}
                      onLoad={() => console.log('Historical Data logo loaded successfully')}
                    />
                  </View>
                  <Text style={styles.appNameBig}>CostCompass</Text>
                </View>

                {/* Page Header */}
                <View style={styles.pageHeader}>
                  <View style={styles.headerIconContainer}>
                    <Ionicons
                      name="analytics"
                      size={32}
                      color={colors.textOnPrimary}
                    />
                  </View>
                  <Text style={styles.leftSideTitle}>
                    Historical Data
                  </Text>
                  <Text style={styles.leftSideSubtitle}>
                    View and export historical spending data
                  </Text>
                </View>

                {/* Benchmark Stats */}
                <View style={styles.leftSideInfo}>
                  <View style={styles.leftSideInfoItem}>
                    <Ionicons name="document-text" size={20} color={colors.textOnPrimary} />
                    <Text style={styles.leftSideInfoText}>
                      {benchmark?.name || 'Historical Data'}
                    </Text>
                  </View>
                  <View style={styles.leftSideInfoItem}>
                    <Ionicons name="cash" size={20} color={colors.textOnPrimary} />
                    <Text style={styles.leftSideInfoText}>
                      {benchmark ? formatCurrency(benchmark.amount, benchmark.currency) : 'N/A'}/{benchmark?.period || 'period'}
                    </Text>
                  </View>
                  <View style={styles.leftSideInfoItem}>
                    <Ionicons name="list" size={20} color={colors.textOnPrimary} />
                    <Text style={styles.leftSideInfoText}>
                      {actualSpends.length} historical records
                    </Text>
                  </View>
                </View>
              </View>
            </LinearGradient>
          </View>
        )}

        {/* Right Side - Main Content */}
        <View style={styles.rightSide}>
          {/* Mobile Header (only shown on mobile) */}
          {(Platform.OS !== 'web' || width <= 600) && (
            <LinearGradient
              colors={colors.gradientPrimary}
              style={styles.mobileHeader}
            >
              <View style={styles.mobileHeaderContent}>
                <View style={styles.headerIconContainer}>
                  <Ionicons
                    name="analytics"
                    size={32}
                    color={colors.textOnPrimary}
                  />
                </View>
                <Text style={styles.mobileHeaderTitle}>
                  Historical Data
                </Text>
                <Text style={styles.mobileHeaderSubtitle}>
                  View and export historical spending data
                </Text>
              </View>
            </LinearGradient>
          )}

          <ScrollView
            contentContainerStyle={styles.rightSideScrollContainer}
            style={styles.rightSideScrollView}
            showsVerticalScrollIndicator={false}
          >
        {/* Benchmark Info Card - Soldo-inspired */}
        <Card style={commonStyles.cardLarge}>
        <Card.Content>
          <Text variant="headlineSmall" style={styles.benchmarkName}>
            {benchmark.name}
          </Text>
          <Text variant="bodyLarge" style={styles.benchmarkAmount}>
            {formatCurrency(benchmark.amount, benchmark.currency)}/
            {benchmark.period}
          </Text>
          <Text variant="bodySmall" style={styles.benchmarkType}>
            Type: {benchmark.type}
          </Text>
        </Card.Content>
      </Card>

        {/* Historical Data Card - Soldo-inspired */}
        <Card style={commonStyles.cardLarge}>
        <Card.Content>
          <View style={styles.sectionHeader}>
            <Text variant="titleLarge" style={styles.sectionTitle}>
              All Historical Data
            </Text>
            <View style={styles.headerActions}>
              {canUploadData && (
                <IconButton
                  icon="upload"
                  size={20}
                  onPress={() => setShowUploadModal(true)}
                  style={styles.uploadButton}
                />
              )}
              {canExportData && (
                <IconButton
                  icon="download"
                  size={20}
                  onPress={() => setShowDownloadModal(true)}
                  style={styles.downloadButton}
                />
              )}
            </View>
          </View>
          <Divider style={styles.divider} />

          {actualSpends.length === 0 ? (
            <View style={styles.emptyState}>
              <Ionicons name="analytics-outline" size={48} color="#ccc" />
              <Text variant="bodyLarge" style={styles.emptyStateText}>
                No historical data found
              </Text>
              <Text variant="bodyMedium" style={styles.emptyStateSubtext}>
                No actual spend entries have been recorded for this benchmark
                yet.
              </Text>
            </View>
          ) : (
            <DataTable style={styles.dataTable}>
              <DataTable.Header style={styles.tableHeader}>
                <DataTable.Title
                  style={[styles.dateColumn, styles.headerTitle]}
                >
                  <Text style={styles.headerText}>Date</Text>
                </DataTable.Title>
                <DataTable.Title
                  numeric
                  style={[styles.occupancyColumn, styles.headerTitle]}
                >
                  <Text style={styles.headerText}>Occupancy</Text>
                </DataTable.Title>
                <DataTable.Title
                  numeric
                  style={[styles.amountColumn, styles.headerTitle]}
                >
                  <Text style={styles.headerText}>Benchmark</Text>
                </DataTable.Title>
                <DataTable.Title
                  numeric
                  style={[styles.amountColumn, styles.headerTitle]}
                >
                  <Text style={styles.headerText}>Actual</Text>
                </DataTable.Title>
                <DataTable.Title
                  numeric
                  style={[styles.varianceColumn, styles.headerTitle]}
                >
                  <Text style={styles.headerText}>Variance</Text>
                </DataTable.Title>
                <DataTable.Title
                  style={[styles.userColumn, styles.headerTitle]}
                >
                  <Text style={styles.headerText}>Posted By</Text>
                </DataTable.Title>
                <DataTable.Title
                  style={[styles.postedDateColumn, styles.headerTitle]}
                >
                  <Text style={styles.headerText}>Posted</Text>
                </DataTable.Title>
                {user?.is_admin && (
                  <DataTable.Title
                    style={[styles.actionsColumn, styles.headerTitle]}
                  >
                    <Text style={styles.headerText}>Actions</Text>
                  </DataTable.Title>
                )}
              </DataTable.Header>

              {actualSpends.map((spend) => (
                <DataTable.Row key={spend.id} style={styles.tableRow}>
                  <DataTable.Cell style={[styles.dateColumn, styles.dataCell]}>
                    <Text style={styles.dateText}>
                      {formatDate(spend.period_start)}
                      {spend.period_start !== spend.period_end &&
                        ` - ${formatDate(spend.period_end)}`}
                    </Text>
                  </DataTable.Cell>
                  <DataTable.Cell
                    numeric
                    style={[styles.occupancyColumn, styles.dataCell]}
                  >
                    <Text style={styles.occupancyText}>
                      {spend.occupancy_at_time || 1}
                    </Text>
                  </DataTable.Cell>
                  <DataTable.Cell
                    numeric
                    style={[styles.amountColumn, styles.dataCell]}
                  >
                    <Text style={styles.benchmarkText}>
                      {formatCurrency(
                        spend.benchmark_amount_at_time || benchmark.amount,
                        benchmark.currency,
                      )}
                    </Text>
                  </DataTable.Cell>
                  <DataTable.Cell
                    numeric
                    style={[styles.amountColumn, styles.dataCell]}
                  >
                    <Text style={styles.actualText}>
                      {formatCurrency(spend.amount, benchmark.currency)}
                    </Text>
                  </DataTable.Cell>
                  <DataTable.Cell
                    numeric
                    style={[styles.varianceColumn, styles.dataCell]}
                  >
                    <View style={styles.varianceCell}>
                      <Ionicons
                        name={getVarianceIcon(spend.variance)}
                        size={14}
                        color={getVarianceColor(spend.variance)}
                      />
                      <Text
                        style={[
                          styles.varianceCellText,
                          { color: getVarianceColor(spend.variance) },
                        ]}
                      >
                        {spend.variance > 0 ? "+" : ""}
                        {formatCurrency(
                          Math.abs(spend.variance),
                          benchmark.currency,
                        )}
                      </Text>
                    </View>
                  </DataTable.Cell>
                  <DataTable.Cell style={[styles.userColumn, styles.dataCell]}>
                    <Text style={styles.userText}>
                      {spend.created_by_name || "Unknown"}
                    </Text>
                  </DataTable.Cell>
                  <DataTable.Cell
                    style={[styles.postedDateColumn, styles.dataCell]}
                  >
                    <Text style={styles.postedDateText}>
                      {formatDate(spend.created_at)}
                    </Text>
                  </DataTable.Cell>
                  {user?.is_admin && (
                    <DataTable.Cell
                      style={[styles.actionsColumn, styles.dataCell]}
                    >
                      {Platform.OS === 'web' ? (
                        <button
                          onClick={async () => {
                            if (confirm(`Are you sure you want to delete this entry from ${formatDate(spend.period_start)}? This action cannot be undone.`)) {
                              try {
                                const result = await DatabaseService.deleteActualSpend(spend.id, user.id);
                                await loadData();
                                alert("Entry deleted successfully! Charts have been updated.");
                              } catch (error) {
                                console.error("Delete error:", error);
                                alert("Error: " + error.message);
                              }
                            }
                          }}
                          style={{
                            backgroundColor: colors.error,
                            color: 'white',
                            padding: '4px 8px',
                            border: 'none',
                            borderRadius: '4px',
                            fontSize: '12px',
                            cursor: 'pointer'
                          }}
                        >
                          Delete
                        </button>
                      ) : (
                        <TouchableOpacity
                          onPress={() => {
                            Alert.alert(
                              "Delete Entry",
                              `Are you sure you want to delete this entry from ${formatDate(spend.period_start)}? This action cannot be undone.`,
                              [
                                { text: "Cancel", style: "cancel" },
                                {
                                  text: "Delete",
                                  style: "destructive",
                                  onPress: async () => {
                                    try {
                                      await DatabaseService.deleteActualSpend(spend.id, user.id);
                                      await loadData();
                                      Alert.alert("Success", "Entry deleted successfully! Charts have been updated.");
                                    } catch (error) {
                                      console.error("Delete error:", error);
                                      Alert.alert("Error", error.message);
                                    }
                                  }
                                }
                              ]
                            );
                          }}
                          style={{
                            backgroundColor: colors.error,
                            padding: 8,
                            borderRadius: 4,
                            minWidth: 60,
                            alignItems: 'center'
                          }}
                        >
                          <Text style={{color: 'white', fontSize: 12}}>Delete</Text>
                        </TouchableOpacity>
                      )}
                    </DataTable.Cell>
                  )}
                </DataTable.Row>
              ))}
            </DataTable>
          )}
        </Card.Content>
      </Card>

      {/* Download Modal */}
      <Portal>
        <Modal
          visible={showDownloadModal}
          onDismiss={() => setShowDownloadModal(false)}
          contentContainerStyle={styles.downloadModal}
        >
          <Card>
            <Card.Content>
              <Text variant="titleLarge" style={styles.downloadModalTitle}>
                Download Historical Data
              </Text>

              <Text variant="bodyMedium" style={styles.downloadModalSubtitle}>
                Select date range and format for your report
              </Text>

              <View style={styles.downloadFormContainer}>
                <Text variant="labelLarge" style={styles.downloadLabel}>
                  Date Range
                </Text>

                <DateInput
                  label="Start Date *"
                  value={downloadStartDate}
                  onPress={showStartDatePickerModal}
                  onChangeText={setDownloadStartDate}
                  disabled={isDownloading}
                  placeholder="YYYY-MM-DD"
                />

                <DateInput
                  label="End Date *"
                  value={downloadEndDate}
                  onPress={showEndDatePickerModal}
                  onChangeText={setDownloadEndDate}
                  disabled={isDownloading}
                  placeholder="YYYY-MM-DD"
                />

                <Text variant="labelLarge" style={styles.downloadLabel}>
                  Format
                </Text>

                <SegmentedButtons
                  value={downloadFormat}
                  onValueChange={setDownloadFormat}
                  buttons={[
                    { value: "pdf", label: "PDF" },
                    { value: "excel", label: "Excel" },
                  ]}
                  style={styles.downloadFormatButtons}
                  disabled={isDownloading}
                  theme={{
                    colors: {
                      primary: colors.primary,
                      onPrimary: colors.textOnPrimary,
                      outline: colors.primary,
                      onSurface: colors.primary,
                    }
                  }}
                />

                <View style={styles.downloadModalActions}>
                  <Button
                    mode="outlined"
                    onPress={() => setShowDownloadModal(false)}
                    disabled={isDownloading}
                    style={styles.downloadCancelButton}
                    textColor={colors.primary}
                  >
                    Cancel
                  </Button>
                  <Button
                    mode="contained"
                    onPress={handleDownloadData}
                    disabled={isDownloading}
                    style={styles.downloadConfirmButton}
                    buttonColor={colors.primary}
                  >
                    {isDownloading ? (
                      <ActivityIndicator color="white" size="small" />
                    ) : (
                      "Download"
                    )}
                  </Button>
                </View>
              </View>
            </Card.Content>
          </Card>
        </Modal>
      </Portal>

      {/* Upload Modal */}
      <Portal>
        <Modal
          visible={showUploadModal}
          onDismiss={() => setShowUploadModal(false)}
          contentContainerStyle={styles.uploadModal}
        >
          <Card>
            <Card.Content>
              <Text variant="titleLarge" style={styles.uploadModalTitle}>
                Upload Historical Data
              </Text>

              <Text variant="bodySmall" style={styles.uploadModalSubtitle}>
                Upload historical spending data from an Excel file
              </Text>

              <View style={styles.uploadFormContainer}>
                <Text variant="bodySmall" style={styles.uploadInstructions}>
                  1. Download template → 2. Fill data (including benchmark amounts) → 3. Upload file
                </Text>

                <View style={styles.uploadActions}>
                  <Button
                    mode="outlined"
                    onPress={generateTemplate}
                    style={styles.templateButton}
                    icon="download"
                    textColor={colors.primary}
                    compact
                  >
                    Template
                  </Button>

                  <Button
                    mode="contained"
                    onPress={handleFileUpload}
                    style={styles.uploadFileButton}
                    icon="upload"
                    disabled={isUploading}
                    buttonColor={colors.primary}
                    compact
                  >
                    {isUploading ? "Uploading..." : "Upload"}
                  </Button>
                </View>

                {isUploading && (
                  <View style={styles.uploadProgressContainer}>
                    <Text variant="bodySmall" style={styles.uploadProgressText}>
                      Uploading... {uploadProgress}%
                    </Text>
                    <View style={styles.progressBar}>
                      <View
                        style={[
                          styles.progressFill,
                          { width: `${uploadProgress}%` }
                        ]}
                      />
                    </View>
                  </View>
                )}

                <View style={styles.uploadModalActions}>
                  <Button
                    mode="outlined"
                    onPress={() => setShowUploadModal(false)}
                    disabled={isUploading}
                    style={styles.uploadCancelButton}
                    textColor={colors.primary}
                    compact
                  >
                    Close
                  </Button>
                </View>
              </View>
            </Card.Content>
          </Card>
        </Modal>
      </Portal>

      {/* Date Pickers - Mobile Only */}
      {Platform.OS !== "web" && showStartPicker && (
        <DateTimePicker
          value={downloadStartDate ? new Date(downloadStartDate) : new Date()}
          mode="date"
          display={Platform.OS === "ios" ? "spinner" : "default"}
          onChange={handleStartDateChange}
        />
      )}

      {Platform.OS !== "web" && showEndPicker && (
        <DateTimePicker
          value={downloadEndDate ? new Date(downloadEndDate) : new Date()}
          mode="date"
          display={Platform.OS === "ios" ? "spinner" : "default"}
          onChange={handleEndDateChange}
        />
      )}
          </ScrollView>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  // Split Layout Styles
  mainContent: {
    flex: 1,
    flexDirection: Platform.OS === 'web' && width > 600 ? 'row' : 'column',
  },
  // Left Side - Blue Section (Desktop only)
  leftSide: {
    flex: 0.4,
    minWidth: 350,
    maxWidth: 450,
  },
  leftSideGradient: {
    flex: 1,
    padding: spacing.lg,
    paddingTop: spacing.md,
    justifyContent: 'flex-start',
  },
  leftSideContent: {
    alignItems: 'flex-start',
    maxWidth: 350,
    alignSelf: 'center',
    width: '100%',
  },
  // Logo Section
  logoSection: {
    alignItems: 'flex-start',
    alignSelf: 'flex-start',
    marginBottom: spacing.xl,
    paddingBottom: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.2)',
    width: '100%',
  },
  appLogoContainer: {
    width: '100%', // Full width of the blue sidebar content area
    height: 120, // Taller to accommodate larger logo
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.lg,
    marginTop: spacing.sm,
    // Remove artificial shadows and borders for more natural integration
  },
  appLogo: {
    width: 280, // Almost full width of the blue sidebar content
    height: 100, // Proportional height
    // Remove tintColor to show original logo colors
  },
  appNameBig: {
    color: colors.textOnPrimary,
    fontSize: 32,
    fontWeight: typography.fontWeights.bold,
    textAlign: 'left',
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
    letterSpacing: 0.5,
  },
  // Page Header Section
  pageHeader: {
    alignItems: 'flex-start',
    marginBottom: spacing.lg,
    width: '100%',
  },
  leftSideTitle: {
    color: colors.textOnPrimary,
    fontSize: 24,
    fontWeight: typography.fontWeights.bold,
    textAlign: 'left',
    marginBottom: spacing.sm,
    textShadowColor: 'rgba(0,0,0,0.1)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  leftSideSubtitle: {
    color: colors.textOnPrimary,
    fontSize: 16,
    textAlign: 'left',
    opacity: 0.95,
    marginBottom: spacing.lg,
    textShadowColor: 'rgba(0,0,0,0.1)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  leftSideInfo: {
    marginTop: spacing.lg,
    alignSelf: 'stretch',
  },
  leftSideInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    padding: spacing.md,
    borderRadius: borderRadius.lg,
  },
  leftSideInfoText: {
    color: colors.textOnPrimary,
    fontSize: 14,
    fontWeight: '500',
    marginLeft: spacing.sm,
    flex: 1,
  },
  // Right Side - Main Content
  rightSide: {
    flex: 1.6,
    backgroundColor: colors.background,
  },
  rightSideScrollContainer: {
    padding: spacing.lg,
    paddingBottom: spacing.xxxl,
  },
  rightSideScrollView: {
    flex: 1,
  },
  // Mobile Header
  mobileHeader: {
    paddingTop: spacing.xxxxl,
    paddingBottom: spacing.xl,
    paddingHorizontal: spacing.xl,
    borderBottomLeftRadius: borderRadius.xxl,
    borderBottomRightRadius: borderRadius.xxl,
  },
  mobileHeaderContent: {
    alignItems: 'center',
  },
  mobileHeaderTitle: {
    color: colors.textOnPrimary,
    fontSize: 24,
    fontWeight: typography.fontWeights.bold,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  mobileHeaderSubtitle: {
    color: colors.textOnPrimary,
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.95,
  },
  headerIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
    elevation: elevation.sm,
    shadowColor: 'rgba(0, 0, 0, 0.3)',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: colors.background,
  },
  loadingText: {
    marginTop: spacing.lg,
    color: colors.textSecondary,
  },
  benchmarkName: {
    color: colors.text,
    fontWeight: typography.fontWeights.bold,
    marginBottom: spacing.sm,
    fontSize: typography.fontSizes.lg,
  },
  benchmarkAmount: {
    color: colors.success,
    fontWeight: typography.fontWeights.bold,
    marginBottom: spacing.xs,
    fontSize: typography.fontSizes.lg,
  },
  benchmarkType: {
    color: colors.textSecondary,
    textTransform: "capitalize",
    fontSize: typography.fontSizes.sm,
  },
  sectionTitle: {
    color: colors.text,
    fontWeight: typography.fontWeights.bold,
    fontSize: typography.fontSizes.xl,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.sm,
  },
  divider: {
    marginBottom: spacing.lg,
  },
  emptyState: {
    alignItems: "center",
    paddingVertical: 32,
  },
  emptyStateText: {
    color: "#666",
    marginTop: 16,
    textAlign: "center",
  },
  emptyStateSubtext: {
    color: "#999",
    marginTop: 8,
    textAlign: "center",
  },
  dataTable: {
    backgroundColor: "transparent",
  },
  tableHeader: {
    backgroundColor: "#f8f9fa",
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
  },
  tableRow: {
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
    minHeight: 48,
  },
  headerTitle: {
    alignItems: "center",
    justifyContent: "center",
  },
  headerText: {
    fontSize: 12,
    fontWeight: "bold",
    color: "#333",
    textDecorationLine: "underline",
    textAlign: "center",
  },
  dataCell: {
    alignItems: "center",
    justifyContent: "center",
  },
  dateColumn: {
    flex: 1.5,
    paddingHorizontal: 12,
    justifyContent: "center",
  },
  occupancyColumn: {
    flex: 0.8,
    paddingHorizontal: 8,
    justifyContent: "center",
  },
  amountColumn: {
    flex: 1.2,
    paddingHorizontal: 12,
    justifyContent: "center",
  },
  varianceColumn: {
    flex: 1.2,
    paddingHorizontal: 12,
    justifyContent: "center",
  },
  userColumn: {
    flex: 1.2,
    paddingHorizontal: 12,
    justifyContent: "center",
  },
  postedDateColumn: {
    flex: 1.0,
    paddingHorizontal: 12,
    justifyContent: "center",
  },
  dateText: {
    fontSize: 11,
    color: "#333",
    fontWeight: "400",
    textAlign: "left",
  },
  occupancyText: {
    fontSize: 11,
    color: "#42A5F5",
    fontWeight: "500",
    textAlign: "center",
  },
  benchmarkText: {
    fontSize: 11,
    color: "#2196F3",
    fontWeight: "500",
    textAlign: "right",
  },
  actualText: {
    fontSize: 11,
    color: "#333",
    fontWeight: "500",
    textAlign: "right",
  },
  userText: {
    fontSize: 11,
    color: "#666",
    fontWeight: "400",
    textAlign: "left",
  },
  postedDateText: {
    fontSize: 11,
    color: "#999",
    fontWeight: "400",
    textAlign: "left",
  },
  varianceCell: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-end",
    gap: 4,
  },
  varianceCellText: {
    fontSize: 11,
    fontWeight: "500",
    textAlign: "right",
  },
  downloadButton: {
    margin: 0,
  },
  backButton: {
    marginTop: 16,
  },
  downloadModal: {
    backgroundColor: colors.surface,
    padding: spacing.xl,
    margin: spacing.xl,
    borderRadius: borderRadius.lg,
    maxHeight: "80%",
    elevation: elevation.lg,
    shadowColor: colors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  downloadModalTitle: {
    textAlign: "center",
    marginBottom: spacing.sm,
    color: colors.primary,
    fontWeight: typography.fontWeights.bold,
    fontSize: typography.fontSizes.xl,
  },
  downloadModalSubtitle: {
    textAlign: "center",
    marginBottom: spacing.xl,
    color: colors.textSecondary,
    fontSize: typography.fontSizes.md,
  },
  downloadFormContainer: {
    gap: spacing.lg,
  },
  downloadLabel: {
    color: colors.primary,
    marginBottom: spacing.sm,
    fontWeight: typography.fontWeights.semiBold,
    fontSize: typography.fontSizes.md,
  },
  downloadFormatButtons: {
    marginBottom: spacing.sm,
    borderRadius: borderRadius.md,
    elevation: elevation.xs,
    shadowColor: colors.primary,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  downloadModalActions: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: spacing.xl,
    gap: spacing.md,
  },
  downloadCancelButton: {
    flex: 1,
    borderColor: colors.primary,
  },
  downloadConfirmButton: {
    flex: 1,
    elevation: elevation.sm,
    shadowColor: colors.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  // Upload styles
  headerActions: {
    flexDirection: "row",
    alignItems: "center",
  },
  uploadButton: {
    margin: 0,
    marginRight: 4,
  },
  uploadModal: {
    backgroundColor: colors.surface,
    padding: spacing.lg,
    margin: spacing.xl,
    marginHorizontal: Platform.OS === 'web' ? '20%' : spacing.xl,
    maxWidth: Platform.OS === 'web' ? 500 : undefined,
    alignSelf: 'center',
    borderRadius: borderRadius.lg,
    elevation: elevation.lg,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  uploadModalTitle: {
    textAlign: "center",
    marginBottom: spacing.sm,
    color: colors.primary,
    fontWeight: typography.fontWeights.bold,
    fontSize: typography.fontSizes.xl,
  },
  uploadModalSubtitle: {
    textAlign: "center",
    marginBottom: spacing.md,
    color: colors.textSecondary,
    fontSize: typography.fontSizes.sm,
  },
  uploadFormContainer: {
    gap: spacing.md,
  },
  uploadInstructions: {
    backgroundColor: colors.surfaceVariant,
    padding: spacing.sm,
    borderRadius: borderRadius.md,
    color: colors.onSurfaceVariant,
    fontSize: typography.fontSizes.xs,
    lineHeight: 16,
    textAlign: "center",
  },
  uploadActions: {
    flexDirection: "row",
    gap: spacing.md,
  },
  templateButton: {
    flex: 1,
    borderRadius: borderRadius.md,
    borderColor: colors.primary,
  },
  uploadFileButton: {
    flex: 1,
    borderRadius: borderRadius.md,
    elevation: elevation.sm,
    shadowColor: colors.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  uploadProgressContainer: {
    marginTop: spacing.md,
  },
  uploadProgressText: {
    textAlign: "center",
    marginBottom: spacing.sm,
    color: colors.primary,
    fontWeight: typography.fontWeights.medium,
  },
  progressBar: {
    height: 4,
    backgroundColor: colors.surfaceVariant,
    borderRadius: 2,
    overflow: "hidden",
  },
  progressFill: {
    height: "100%",
    backgroundColor: colors.primary,
    borderRadius: 2,
  },
  uploadModalActions: {
    flexDirection: "row",
    justifyContent: "center",
    marginTop: spacing.md,
  },
  uploadCancelButton: {
    borderRadius: borderRadius.md,
    borderColor: colors.primary,
    minWidth: 100,
  },
  // Delete functionality styles
  actionsColumn: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  deleteButton: {
    margin: 0,
    padding: 0,
  },
});

export default HistoricalDataScreen;
