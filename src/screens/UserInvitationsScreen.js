import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, RefreshControl, Platform, Dimensions, Image } from 'react-native';
import {
  Text,
  Card,
  Button,
  ActivityIndicator,
  Chip,
  Dialog,
  Portal,
  Divider
} from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { colors, spacing, borderRadius, commonStyles, typography, elevation } from '../styles/theme';
import { useClerkAuth } from '../contexts/ClerkAuthContext';
import DatabaseService from '../services/database';

const { width } = Dimensions.get("window");

const UserInvitationsScreen = () => {
  const { user, selectCompany } = useClerkAuth();
  const [invitations, setInvitations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedInvitation, setSelectedInvitation] = useState(null);
  const [confirmDialogVisible, setConfirmDialogVisible] = useState(false);
  const [actionType, setActionType] = useState(null); // 'accept' or 'decline'
  const [processing, setProcessing] = useState(false);

  useEffect(() => {
    loadInvitations();
  }, []);

  const loadInvitations = async () => {
    try {
      setLoading(true);
      // Get invitations for current user's email
      const userInvitations = await DatabaseService.getUserInvitations(user.email);
      setInvitations(userInvitations);
    } catch (error) {
      console.error('Error loading invitations:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadInvitations();
    setRefreshing(false);
  };

  const handleInvitationAction = (invitation, action) => {
    setSelectedInvitation(invitation);
    setActionType(action);
    setConfirmDialogVisible(true);
  };

  const confirmAction = async () => {
    if (!selectedInvitation || !actionType) return;

    try {
      setProcessing(true);
      setConfirmDialogVisible(false);

      if (actionType === 'accept') {
        // Accept the invitation
        const result = await DatabaseService.acceptInvitation(selectedInvitation.token);
        
        if (result.success) {
          // Refresh invitations list
          await loadInvitations();
          
          // Show success message and offer to switch to new company
          const company = await DatabaseService.getCompanyById(result.companyId);
          
          // You might want to show a success dialog here
          console.log('Invitation accepted successfully!');
          
          // Optionally auto-switch to the new company
          // await selectCompany(company);
        }
      } else if (actionType === 'decline') {
        // Mark invitation as declined (you'll need to implement this)
        await DatabaseService.declineInvitation(selectedInvitation.token);
        await loadInvitations();
      }
    } catch (error) {
      console.error('Error processing invitation:', error);
      // Show error message to user
    } finally {
      setProcessing(false);
      setSelectedInvitation(null);
      setActionType(null);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return colors.warning;
      case 'accepted': return colors.success;
      case 'expired': return colors.error;
      case 'declined': return colors.textSecondary;
      default: return colors.textSecondary;
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending': return 'clock-outline';
      case 'accepted': return 'check-circle';
      case 'expired': return 'close-circle';
      case 'declined': return 'minus-circle';
      default: return 'help-circle';
    }
  };

  if (loading) {
    return (
      <View style={commonStyles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text variant="bodyLarge" style={styles.loadingText}>
            Loading invitations...
          </Text>
        </View>
      </View>
    );
  }

  const pendingInvitations = invitations.filter(inv => inv.status === 'pending');
  const otherInvitations = invitations.filter(inv => inv.status !== 'pending');

  return (
    <View style={commonStyles.container}>
      {/* Split Layout - Blue section on left, content on right */}
      <View style={styles.mainContent}>
        {/* Left Side - Blue Header Section (Desktop only) */}
        {Platform.OS === 'web' && width > 600 && (
          <View style={styles.leftSide}>
            <LinearGradient
              colors={colors.gradientPrimary}
              style={styles.leftSideGradient}
            >
              <View style={styles.leftSideContent}>
                {/* App Logo and Name */}
                <View style={styles.logoSection}>
                  <View style={styles.appLogoContainer}>
                    <Image
                      source={require("../../assets/logo.png")}
                      style={styles.appLogo}
                      resizeMode="contain"
                    />
                  </View>
                  <Text style={styles.appNameBig}>CostCompass</Text>
                </View>

                {/* Page Header */}
                <View style={styles.pageHeader}>
                  <View style={styles.headerIconContainer}>
                    <Ionicons
                      name="mail-open"
                      size={32}
                      color={colors.textOnPrimary}
                    />
                  </View>
                  <Text style={styles.leftSideTitle}>
                    Company Invitations
                  </Text>
                  <Text style={styles.leftSideSubtitle}>
                    Invitations to join companies
                  </Text>
                </View>

                {/* Invitation Stats */}
                <View style={styles.leftSideInfo}>
                  <View style={styles.leftSideInfoItem}>
                    <Ionicons name="time" size={20} color={colors.textOnPrimary} />
                    <Text style={styles.leftSideInfoText}>
                      {pendingInvitations.length} pending invitations
                    </Text>
                  </View>
                  <View style={styles.leftSideInfoItem}>
                    <Ionicons name="archive" size={20} color={colors.textOnPrimary} />
                    <Text style={styles.leftSideInfoText}>
                      {otherInvitations.length} previous invitations
                    </Text>
                  </View>
                </View>
              </View>
            </LinearGradient>
          </View>
        )}

        {/* Right Side - Main Content */}
        <View style={styles.rightSide}>
          {/* Mobile Header (only shown on mobile) */}
          {(Platform.OS !== 'web' || width <= 600) && (
            <LinearGradient
              colors={colors.gradientPrimary}
              style={styles.mobileHeader}
            >
              <View style={styles.mobileHeaderContent}>
                <View style={styles.headerIconContainer}>
                  <Ionicons
                    name="mail-open"
                    size={32}
                    color={colors.textOnPrimary}
                  />
                </View>
                <Text style={styles.mobileHeaderTitle}>
                  Company Invitations
                </Text>
                <Text style={styles.mobileHeaderSubtitle}>
                  Invitations to join companies
                </Text>
              </View>
            </LinearGradient>
          )}

          <ScrollView
            contentContainerStyle={styles.rightSideScrollContainer}
            style={styles.rightSideScrollView}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
            }
          >

      {/* Pending Invitations */}
      {pendingInvitations.length > 0 && (
        <Card style={commonStyles.cardLarge}>
          <Card.Content>
            <View style={commonStyles.sectionHeader}>
              <Ionicons name="time" size={24} color={colors.warning} />
              <Text variant="titleMedium" style={commonStyles.sectionTitle}>
                Pending Invitations ({pendingInvitations.length})
              </Text>
            </View>
            
            {pendingInvitations.map((invitation) => (
              <Card key={invitation.id} style={styles.invitationCard}>
                <Card.Content>
                  <View style={styles.invitationHeader}>
                    <View style={styles.invitationInfo}>
                      <Text variant="titleMedium" style={styles.companyName}>
                        {invitation.company_name}
                      </Text>
                      {invitation.company_registration && (
                        <Text variant="bodySmall" style={styles.companyDetail}>
                          Registration: {invitation.company_registration}
                        </Text>
                      )}
                      <Text variant="bodySmall" style={styles.invitationDate}>
                        Invited {new Date(invitation.created_at).toLocaleDateString()}
                      </Text>
                      <Text variant="bodySmall" style={styles.expiryDate}>
                        Expires {new Date(invitation.expires_at).toLocaleDateString()}
                      </Text>
                    </View>
                    <Chip
                      icon={getStatusIcon(invitation.status)}
                      style={[styles.statusChip, { backgroundColor: getStatusColor(invitation.status) + '20' }]}
                      textStyle={{ color: getStatusColor(invitation.status) }}
                    >
                      {invitation.status.charAt(0).toUpperCase() + invitation.status.slice(1)}
                    </Chip>
                  </View>

                  <Divider style={styles.divider} />

                  <View style={styles.actionButtons}>
                    <Button
                      mode="outlined"
                      onPress={() => handleInvitationAction(invitation, 'decline')}
                      style={styles.declineButton}
                      textColor={colors.error}
                      disabled={processing}
                    >
                      Decline
                    </Button>
                    <Button
                      mode="contained"
                      onPress={() => handleInvitationAction(invitation, 'accept')}
                      style={styles.acceptButton}
                      buttonColor={colors.success}
                      disabled={processing}
                    >
                      Accept
                    </Button>
                  </View>
                </Card.Content>
              </Card>
            ))}
          </Card.Content>
        </Card>
      )}

      {/* Previous Invitations */}
      {otherInvitations.length > 0 && (
        <Card style={commonStyles.cardLarge}>
          <Card.Content>
            <View style={commonStyles.sectionHeader}>
              <Ionicons name="archive" size={24} color={colors.textSecondary} />
              <Text variant="titleMedium" style={commonStyles.sectionTitle}>
                Previous Invitations
              </Text>
            </View>
            
            {otherInvitations.map((invitation) => (
              <View key={invitation.id} style={styles.historyItem}>
                <View style={styles.historyInfo}>
                  <Text variant="titleMedium" style={styles.historyCompanyName}>
                    {invitation.company_name}
                  </Text>
                  <Text variant="bodySmall" style={styles.historyDate}>
                    {new Date(invitation.created_at).toLocaleDateString()}
                  </Text>
                </View>
                <Chip
                  icon={getStatusIcon(invitation.status)}
                  style={[styles.statusChip, { backgroundColor: getStatusColor(invitation.status) + '20' }]}
                  textStyle={{ color: getStatusColor(invitation.status) }}
                >
                  {invitation.status.charAt(0).toUpperCase() + invitation.status.slice(1)}
                </Chip>
              </View>
            ))}
          </Card.Content>
        </Card>
      )}

      {/* Empty State */}
      {invitations.length === 0 && (
        <Card style={commonStyles.cardLarge}>
          <Card.Content>
            <View style={commonStyles.emptyState}>
              <View style={commonStyles.emptyStateIcon}>
                <Ionicons name="mail-outline" size={48} color={colors.textLight} />
              </View>
              <Text style={commonStyles.emptyStateTitle}>
                No invitations found
              </Text>
              <Text style={commonStyles.emptyStateText}>
                You'll see company invitations here when administrators invite you to join their companies.
              </Text>
            </View>
          </Card.Content>
        </Card>
      )}

      {/* Confirmation Dialog */}
      <Portal>
        <Dialog visible={confirmDialogVisible} onDismiss={() => setConfirmDialogVisible(false)}>
          <Dialog.Title>
            {actionType === 'accept' ? 'Accept Invitation' : 'Decline Invitation'}
          </Dialog.Title>
          <Dialog.Content>
            <Text variant="bodyMedium">
              {actionType === 'accept' 
                ? `Are you sure you want to accept the invitation to join ${selectedInvitation?.company_name}?`
                : `Are you sure you want to decline the invitation to join ${selectedInvitation?.company_name}?`
              }
            </Text>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setConfirmDialogVisible(false)} disabled={processing}>
              Cancel
            </Button>
            <Button
              onPress={confirmAction}
              disabled={processing}
              loading={processing}
              buttonColor={actionType === 'accept' ? colors.success : colors.error}
            >
              {actionType === 'accept' ? 'Accept' : 'Decline'}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
          </ScrollView>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  // Split Layout Styles
  mainContent: {
    flex: 1,
    flexDirection: Platform.OS === 'web' && width > 600 ? 'row' : 'column',
  },
  // Left Side - Blue Section (Desktop only)
  leftSide: {
    flex: 0.4,
    minWidth: 350,
    maxWidth: 450,
  },
  leftSideGradient: {
    flex: 1,
    padding: spacing.lg,
    paddingTop: spacing.md,
    justifyContent: 'flex-start',
  },
  leftSideContent: {
    alignItems: 'flex-start',
    maxWidth: 350,
    alignSelf: 'center',
    width: '100%',
  },
  // Logo Section
  logoSection: {
    alignItems: 'flex-start',
    alignSelf: 'flex-start',
    marginBottom: spacing.xl,
    paddingBottom: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.2)',
    width: '100%',
  },
  appLogoContainer: {
    width: '100%', // Full width of the blue sidebar content area
    height: 120, // Taller to accommodate larger logo
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.lg,
    marginTop: spacing.sm,
    // Remove artificial shadows and borders for more natural integration
  },
  appLogo: {
    width: 280, // Almost full width of the blue sidebar content
    height: 100, // Proportional height
    // Remove tintColor to show original logo colors
  },
  appNameBig: {
    color: colors.textOnPrimary,
    fontSize: 32,
    fontWeight: typography.fontWeights.bold,
    textAlign: 'left',
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
    letterSpacing: 0.5,
  },
  // Page Header Section
  pageHeader: {
    alignItems: 'flex-start',
    marginBottom: spacing.lg,
    width: '100%',
  },
  leftSideTitle: {
    color: colors.textOnPrimary,
    fontSize: 24,
    fontWeight: typography.fontWeights.bold,
    textAlign: 'left',
    marginBottom: spacing.sm,
    textShadowColor: 'rgba(0,0,0,0.1)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  leftSideSubtitle: {
    color: colors.textOnPrimary,
    fontSize: 16,
    textAlign: 'left',
    opacity: 0.95,
    marginBottom: spacing.lg,
    textShadowColor: 'rgba(0,0,0,0.1)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  leftSideInfo: {
    marginTop: spacing.lg,
    alignSelf: 'stretch',
  },
  leftSideInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    padding: spacing.md,
    borderRadius: borderRadius.lg,
  },
  leftSideInfoText: {
    color: colors.textOnPrimary,
    fontSize: 14,
    fontWeight: '500',
    marginLeft: spacing.sm,
    flex: 1,
  },
  // Right Side - Main Content
  rightSide: {
    flex: 1.6,
    backgroundColor: colors.background,
  },
  rightSideScrollContainer: {
    padding: spacing.lg,
    paddingBottom: spacing.xxxl,
  },
  rightSideScrollView: {
    flex: 1,
  },
  // Mobile Header
  mobileHeader: {
    paddingTop: spacing.xxxxl,
    paddingBottom: spacing.xl,
    paddingHorizontal: spacing.xl,
    borderBottomLeftRadius: borderRadius.xxl,
    borderBottomRightRadius: borderRadius.xxl,
  },
  mobileHeaderContent: {
    alignItems: 'center',
  },
  mobileHeaderTitle: {
    color: colors.textOnPrimary,
    fontSize: 24,
    fontWeight: typography.fontWeights.bold,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  mobileHeaderSubtitle: {
    color: colors.textOnPrimary,
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.95,
  },
  headerIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
    elevation: elevation.sm,
    shadowColor: 'rgba(0, 0, 0, 0.3)',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: spacing.md,
    color: colors.textSecondary,
  },
  invitationCard: {
    marginBottom: spacing.md,
    backgroundColor: colors.surface,
    borderRadius: borderRadius.md,
    elevation: elevation.sm,
  },
  invitationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  invitationInfo: {
    flex: 1,
    marginRight: spacing.sm,
  },
  companyName: {
    fontWeight: typography.fontWeights.bold,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  companyDetail: {
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  invitationDate: {
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  expiryDate: {
    color: colors.warning,
    fontWeight: typography.fontWeights.medium,
  },
  statusChip: {
    alignSelf: 'flex-start',
  },
  divider: {
    marginVertical: spacing.md,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  declineButton: {
    flex: 1,
    borderColor: colors.error,
    borderRadius: borderRadius.sm,
  },
  acceptButton: {
    flex: 1,
    borderRadius: borderRadius.sm,
  },
  historyItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  historyInfo: {
    flex: 1,
  },
  historyCompanyName: {
    color: colors.text,
    marginBottom: spacing.xs,
    fontWeight: typography.fontWeights.medium,
  },
  historyDate: {
    color: colors.textSecondary,
    fontSize: typography.fontSizes.sm,
  },
});

export default UserInvitationsScreen;
