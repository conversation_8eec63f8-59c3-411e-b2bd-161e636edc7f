import React, { useState } from "react";
import {
  View,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Platform,
  Image,
} from "react-native";
import { Text } from "react-native-paper";
import { Ionicons } from "@expo/vector-icons";

const LandingScreen = ({ navigation }) => {
  const [selectedIndustry, setSelectedIndustry] = useState("care-homes");
  // Removed annual billing - only monthly subscriptions

  const industries = [
    {
      id: "care-homes",
      name: "Care Homes",
      icon: "home",
      color: "#4CAF50",
      description: "Manage resident care costs, staffing budgets, and operational expenses",
    },
    {
      id: "hotels",
      name: "Hotels",
      icon: "business",
      color: "#2196F3",
      description: "Track room service costs, housekeeping, and guest amenity expenses",
    },
    {
      id: "hostels",
      name: "Hostels",
      icon: "bed",
      color: "#FF9800",
      description: "Monitor dormitory costs, utilities, and shared facility expenses",
    },
    {
      id: "hospitals",
      name: "Hospitals",
      icon: "medical",
      color: "#F44336",
      description: "Control medical supply costs, patient care, and equipment expenses",
    },
  ];

  const pricingPlans = [
    {
      id: "starter",
      name: "Starter",
      description: "Perfect for small businesses getting started",
      monthlyPrice: 20,
      features: [
        "Up to 5 users",
        "Up to 5 benchmark/budget",
        "Benchmark/budget reports",
        "Email notifications",
        "Basic benchmarking"
      ],
      popular: false,
    },
    {
      id: "professional",
      name: "Professional",
      description: "Ideal for growing businesses",
      monthlyPrice: 30,
      features: [
        "Up to 10 users",
        "Up to 10 benchmark/budget",
        "Benchmark/budget reports",
        "Email notifications",
        "Basic benchmarking",
        "Upload historical data",
        "Data export"
      ],
      popular: true,
    },
    {
      id: "enterprise",
      name: "Enterprise",
      description: "For large organizations",
      monthlyPrice: 40,
      features: [
        "Unlimited users",
        "Up to 10 benchmark/budget",
        "Benchmark/budget reports",
        "Email notifications",
        "Basic benchmarking",
        "Upload historical data",
        "Data export"
      ],
      popular: false,
    },
  ];

  return (
    <View style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Soldo-style Header Navigation */}
        <View style={styles.headerNav}>
          <View style={styles.headerNavContent}>
            <View style={styles.logoSection}>
              <Text style={styles.logoText}>CostCompass</Text>
            </View>
            <View style={styles.headerButtons}>
              <TouchableOpacity
                onPress={() => navigation.navigate("SignIn")}
                style={styles.signInButton}
              >
                <Text style={styles.signInText}>Sign in</Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => navigation.navigate("SignUp")}
                style={styles.trialButton}
              >
                <Text style={styles.trialText}>Start free trial</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Soldo-style Hero Section */}
        <View style={styles.heroSection}>
          <View style={styles.heroContent}>
            <View style={styles.heroLeft}>
              <Text style={styles.heroTitle}>
                Smart cost management for modern businesses
              </Text>
              <Text style={styles.heroSubtitle}>
                Take control of your operational expenses with real-time tracking, intelligent benchmarking, and automated budget management designed for healthcare and hospitality industries.
              </Text>
              <View style={styles.heroButtons}>
                <TouchableOpacity
                  onPress={() => navigation.navigate("SignUp")}
                  style={styles.primaryButton}
                >
                  <Text style={styles.primaryButtonText}>Start free trial</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => navigation.navigate("SignIn")}
                  style={styles.secondaryButton}
                >
                  <Text style={styles.secondaryButtonText}>Sign in</Text>
                </TouchableOpacity>
              </View>
              <View style={styles.heroFeatures}>
                <View style={styles.heroFeature}>
                  <Ionicons name="checkmark-circle" size={16} color="#4CAF50" />
                  <Text style={styles.heroFeatureText}>No setup fees</Text>
                </View>
                <View style={styles.heroFeature}>
                  <Ionicons name="checkmark-circle" size={16} color="#4CAF50" />
                  <Text style={styles.heroFeatureText}>Cancel anytime</Text>
                </View>
              </View>
            </View>
            <View style={styles.heroRight}>
              <View style={styles.heroImageContainer}>
                <Ionicons name="analytics" size={80} color="#1976D2" />
              </View>
            </View>
          </View>
        </View>

        {/* Soldo-style Features Section */}
        <View style={styles.featuresSection}>
          <View style={styles.sectionContent}>
            <Text style={styles.sectionTitle}>
              Everything you need to manage costs effectively
            </Text>
            <Text style={styles.sectionSubtitle}>
              Powerful tools designed specifically for healthcare and hospitality businesses
            </Text>

            <View style={styles.featuresGrid}>
              <View style={styles.featureCard}>
                <View style={styles.featureIcon}>
                  <Ionicons name="analytics" size={32} color="#1976D2" />
                </View>
                <Text style={styles.featureTitle}>Real-time tracking</Text>
                <Text style={styles.featureDescription}>
                  Monitor your spending against benchmarks with instant alerts and detailed analytics
                </Text>
              </View>

              <View style={styles.featureCard}>
                <View style={styles.featureIcon}>
                  <Ionicons name="people" size={32} color="#1976D2" />
                </View>
                <Text style={styles.featureTitle}>Team collaboration</Text>
                <Text style={styles.featureDescription}>
                  Invite team members, set permissions, and work together across departments
                </Text>
              </View>

              <View style={styles.featureCard}>
                <View style={styles.featureIcon}>
                  <Ionicons name="shield-checkmark" size={32} color="#1976D2" />
                </View>
                <Text style={styles.featureTitle}>Budget protection</Text>
                <Text style={styles.featureDescription}>
                  Set spending limits and receive alerts before exceeding your budgets
                </Text>
              </View>

              <View style={styles.featureCard}>
                <View style={styles.featureIcon}>
                  <Ionicons name="trending-up" size={32} color="#1976D2" />
                </View>
                <Text style={styles.featureTitle}>Performance insights</Text>
                <Text style={styles.featureDescription}>
                  Visualize trends with interactive charts and comprehensive reporting
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* Soldo-style Industries Section */}
        <View style={styles.industriesSection}>
          <View style={styles.sectionContent}>
            <Text style={styles.sectionTitle}>
              Built for your industry
            </Text>
            <Text style={styles.sectionSubtitle}>
              Specialized solutions for healthcare and hospitality businesses
            </Text>

            <View style={styles.industriesGrid}>
              {industries.map((industry) => (
                <TouchableOpacity
                  key={industry.id}
                  style={[
                    styles.industryCard,
                    selectedIndustry === industry.id && styles.selectedIndustryCard
                  ]}
                  onPress={() => setSelectedIndustry(industry.id)}
                >
                  <View style={[styles.industryIcon, { backgroundColor: industry.color + '20' }]}>
                    <Ionicons name={industry.icon} size={24} color={industry.color} />
                  </View>
                  <Text style={styles.industryTitle}>{industry.name}</Text>
                  <Text style={styles.industryDescription}>{industry.description}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>

        {/* Pricing Section */}
        <View style={styles.pricingSection}>
          <View style={styles.sectionContent}>
            <Text style={styles.sectionTitle}>
              Simple, transparent pricing
            </Text>
            <Text style={styles.sectionSubtitle}>
              Choose the plan that's right for your business. All plans include a 30-day free trial.
            </Text>



            {/* Pricing Cards */}
            <View style={styles.pricingGrid}>
              {pricingPlans.map((plan) => (
                <View
                  key={plan.id}
                  style={[
                    styles.pricingCard,
                    plan.popular && styles.popularPricingCard
                  ]}
                >
                  {plan.popular && (
                    <View style={styles.popularBadge}>
                      <Text style={styles.popularBadgeText}>Most Popular</Text>
                    </View>
                  )}
                  <Text style={styles.planName}>{plan.name}</Text>
                  <Text style={styles.planDescription}>{plan.description}</Text>
                  <View style={styles.priceContainer}>
                    <Text style={styles.price}>
                      £{plan.monthlyPrice}
                    </Text>
                    <Text style={styles.priceUnit}>
                      /month
                    </Text>
                  </View>
                  <TouchableOpacity
                    style={[
                      styles.planButton,
                      plan.popular && styles.popularPlanButton
                    ]}
                    onPress={() => navigation.navigate("SignUp", { selectedPlan: plan.id })}
                  >
                    <Text style={[
                      styles.planButtonText,
                      plan.popular && styles.popularPlanButtonText
                    ]}>
                      Choose this plan
                    </Text>
                  </TouchableOpacity>
                  <View style={styles.featuresList}>
                    {plan.features.map((feature, index) => (
                      <View key={index} style={styles.featureItem}>
                        <Ionicons name="checkmark" size={16} color="#4CAF50" />
                        <Text style={styles.featureText}>{feature}</Text>
                      </View>
                    ))}
                  </View>
                </View>
              ))}
            </View>
          </View>
        </View>

        {/* Soldo-style CTA Section */}
        <View style={styles.ctaSection}>
          <View style={styles.ctaContent}>
            <Text style={styles.ctaTitle}>
              Ready to take control of your costs?
            </Text>
            <Text style={styles.ctaSubtitle}>
              Join hundreds of businesses already saving money with CostCompass. Start your free trial today.
            </Text>
            <View style={styles.ctaButtons}>
              <TouchableOpacity
                onPress={() => navigation.navigate("SignUp")}
                style={styles.ctaPrimaryButton}
              >
                <Text style={styles.ctaPrimaryText}>Start free trial</Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => navigation.navigate("SignIn")}
                style={styles.ctaSecondaryButton}
              >
                <Text style={styles.ctaSecondaryText}>Sign in</Text>
              </TouchableOpacity>
            </View>
            <Text style={styles.ctaNote}>No credit card required • Cancel anytime</Text>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#ffffff",
  },

  // Soldo-style Header
  headerNav: {
    backgroundColor: "#ffffff",
    paddingTop: Platform.OS === "web" ? 20 : 50,
    paddingBottom: 20,
    paddingHorizontal: 24,
    borderBottomWidth: 1,
    borderBottomColor: "#f1f3f4",
  },
  headerNavContent: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    maxWidth: 1200,
    alignSelf: "center",
    width: "100%",
  },
  logoSection: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  logoText: {
    fontSize: 24,
    fontWeight: "700",
    color: "#1976D2",
  },
  headerButtons: {
    flexDirection: "row",
    gap: 16,
    alignItems: "center",
  },
  signInButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  signInText: {
    fontSize: 16,
    fontWeight: "500",
    color: "#5f6368",
  },
  trialButton: {
    backgroundColor: "#1976D2",
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 24,
  },
  trialText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#ffffff",
  },

  // Soldo-style Hero Section
  heroSection: {
    backgroundColor: "#ffffff",
    paddingTop: 80,
    paddingBottom: 80,
    paddingHorizontal: 24,
  },
  heroContent: {
    flexDirection: Platform.OS === "web" ? "row" : "column",
    alignItems: "center",
    maxWidth: 1200,
    alignSelf: "center",
    width: "100%",
    gap: 60,
  },
  heroLeft: {
    flex: 1,
    alignItems: Platform.OS === "web" ? "flex-start" : "center",
  },
  heroTitle: {
    fontSize: Platform.OS === "web" ? 56 : 40,
    fontWeight: "700",
    color: "#202124",
    lineHeight: Platform.OS === "web" ? 64 : 48,
    marginBottom: 24,
    textAlign: Platform.OS === "web" ? "left" : "center",
  },
  heroSubtitle: {
    fontSize: 20,
    fontWeight: "400",
    color: "#5f6368",
    lineHeight: 28,
    marginBottom: 32,
    textAlign: Platform.OS === "web" ? "left" : "center",
  },
  heroButtons: {
    flexDirection: "row",
    gap: 16,
    marginBottom: 24,
  },
  primaryButton: {
    backgroundColor: "#1976D2",
    paddingHorizontal: 40,
    paddingVertical: 18,
    borderRadius: 32, // Very rounded Soldo-style buttons
    shadowColor: "#1976D2",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#ffffff",
  },
  secondaryButton: {
    borderWidth: 2,
    borderColor: "#dadce0",
    paddingHorizontal: 40,
    paddingVertical: 18,
    borderRadius: 32, // Very rounded Soldo-style buttons
    backgroundColor: "#ffffff",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#5f6368",
  },
  heroFeatures: {
    flexDirection: "row",
    gap: 24,
  },
  heroFeature: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  heroFeatureText: {
    fontSize: 14,
    color: "#5f6368",
  },
  heroRight: {
    flex: 1,
    alignItems: "center",
  },
  heroImageContainer: {
    width: 400,
    height: 300,
    backgroundColor: "#f8f9fa",
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
  },

  // Soldo-style Sections
  featuresSection: {
    backgroundColor: "#f8f9fa",
    paddingTop: 80,
    paddingBottom: 80,
    paddingHorizontal: 24,
  },
  industriesSection: {
    backgroundColor: "#ffffff",
    paddingTop: 80,
    paddingBottom: 80,
    paddingHorizontal: 24,
  },
  sectionContent: {
    maxWidth: 1200,
    alignSelf: "center",
    width: "100%",
  },
  sectionTitle: {
    fontSize: 40,
    fontWeight: "700",
    color: "#202124",
    textAlign: "center",
    marginBottom: 16,
    lineHeight: 48,
  },
  sectionSubtitle: {
    fontSize: 18,
    fontWeight: "400",
    color: "#5f6368",
    textAlign: "center",
    marginBottom: 64,
    lineHeight: 26,
  },

  // Features Grid
  featuresGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 32,
    justifyContent: "center",
  },
  featureCard: {
    backgroundColor: "#ffffff",
    padding: 40,
    borderRadius: 24, // Very obvious Soldo-style rounded corners
    width: Platform.OS === "web" ? 280 : "100%",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 24,
    elevation: 8,
    borderWidth: 1,
    borderColor: "#f1f3f4",
  },
  featureIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: "#1976D2" + "20",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 24,
  },
  featureTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#202124",
    marginBottom: 12,
    textAlign: "center",
  },
  featureDescription: {
    fontSize: 16,
    color: "#5f6368",
    textAlign: "center",
    lineHeight: 24,
  },

  // Industries Grid
  industriesGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 24,
    justifyContent: "center",
  },
  industryCard: {
    backgroundColor: "#ffffff",
    padding: 32,
    borderRadius: 24, // Very obvious Soldo-style rounded corners
    width: Platform.OS === "web" ? 280 : "100%",
    borderWidth: 2,
    borderColor: "#f1f3f4",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.12,
    shadowRadius: 16,
    elevation: 6,
  },
  selectedIndustryCard: {
    borderColor: "#1976D2",
  },
  industryIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 16,
  },
  industryTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#202124",
    marginBottom: 8,
  },
  industryDescription: {
    fontSize: 14,
    color: "#5f6368",
    lineHeight: 20,
  },

  // Pricing Section
  pricingSection: {
    backgroundColor: "#f8f9fa",
    paddingTop: 80,
    paddingBottom: 80,
    paddingHorizontal: 24,
  },
  pricingToggle: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 48,
    gap: 16,
  },
  toggleText: {
    fontSize: 16,
    fontWeight: "500",
    color: "#5f6368",
  },
  activeToggleText: {
    color: "#1976D2",
    fontWeight: "600",
  },
  toggleSwitch: {
    width: 48,
    height: 24,
    backgroundColor: "#dadce0",
    borderRadius: 12,
    padding: 2,
    justifyContent: "center",
  },
  toggleSlider: {
    width: 20,
    height: 20,
    backgroundColor: "#ffffff",
    borderRadius: 10,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  toggleSliderActive: {
    backgroundColor: "#1976D2",
    alignSelf: "flex-end",
  },
  savingsBadge: {
    color: "#4CAF50",
    fontWeight: "600",
  },
  pricingGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 24,
    justifyContent: "center",
  },
  pricingCard: {
    backgroundColor: "#ffffff",
    padding: 32,
    borderRadius: 24,
    width: Platform.OS === "web" ? 320 : "100%",
    borderWidth: 2,
    borderColor: "#f1f3f4",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.12,
    shadowRadius: 16,
    elevation: 6,
    position: "relative",
  },
  popularPricingCard: {
    borderColor: "#1976D2",
    transform: [{ scale: 1.05 }],
  },
  popularBadge: {
    position: "absolute",
    top: -12,
    left: "50%",
    transform: [{ translateX: -50 }],
    backgroundColor: "#1976D2",
    paddingHorizontal: 16,
    paddingVertical: 6,
    borderRadius: 16,
  },
  popularBadgeText: {
    color: "#ffffff",
    fontSize: 12,
    fontWeight: "600",
  },
  planName: {
    fontSize: 24,
    fontWeight: "700",
    color: "#202124",
    textAlign: "center",
    marginBottom: 8,
  },
  planDescription: {
    fontSize: 14,
    color: "#5f6368",
    textAlign: "center",
    marginBottom: 24,
  },
  priceContainer: {
    alignItems: "center",
    marginBottom: 24,
  },
  price: {
    fontSize: 48,
    fontWeight: "700",
    color: "#202124",
  },
  priceUnit: {
    fontSize: 14,
    color: "#5f6368",
    marginTop: 4,
  },
  planButton: {
    backgroundColor: "#ffffff",
    borderWidth: 2,
    borderColor: "#1976D2",
    paddingVertical: 16,
    borderRadius: 24,
    marginBottom: 24,
  },
  popularPlanButton: {
    backgroundColor: "#1976D2",
    borderColor: "#1976D2",
  },
  planButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1976D2",
    textAlign: "center",
  },
  popularPlanButtonText: {
    color: "#ffffff",
  },
  featuresList: {
    gap: 12,
  },
  featureItem: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  featureText: {
    fontSize: 14,
    color: "#5f6368",
    flex: 1,
  },

  // CTA Section
  ctaSection: {
    backgroundColor: "#f8f9fa",
    paddingTop: 80,
    paddingBottom: 80,
    paddingHorizontal: 24,
  },
  ctaContent: {
    maxWidth: 600,
    alignSelf: "center",
    alignItems: "center",
  },
  ctaTitle: {
    fontSize: 40,
    fontWeight: "700",
    color: "#202124",
    textAlign: "center",
    marginBottom: 16,
    lineHeight: 48,
  },
  ctaSubtitle: {
    fontSize: 18,
    color: "#5f6368",
    textAlign: "center",
    marginBottom: 32,
    lineHeight: 26,
  },
  ctaButtons: {
    flexDirection: "row",
    gap: 16,
    marginBottom: 16,
  },
  ctaPrimaryButton: {
    backgroundColor: "#1976D2",
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 24,
  },
  ctaPrimaryText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#ffffff",
  },
  ctaSecondaryButton: {
    borderWidth: 1,
    borderColor: "#dadce0",
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 24,
  },
  ctaSecondaryText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#5f6368",
  },
  ctaNote: {
    fontSize: 14,
    color: "#5f6368",
    textAlign: "center",
  },
});

export default LandingScreen;
