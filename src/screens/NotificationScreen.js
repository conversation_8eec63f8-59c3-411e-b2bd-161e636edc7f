import React, { useState, useEffect, useCallback } from "react";
import { View, StyleSheet, ScrollView, RefreshControl, Alert } from "react-native";
import {
  Text,
  Card,
  ActivityIndicator,
  Button,
  Chip,
  IconButton,
  Surface,
  Divider,
} from "react-native-paper";
import { Ionicons } from "@expo/vector-icons";
import { useFocusEffect } from "@react-navigation/native";
import { useClerkAuth } from "../contexts/ClerkAuthContext";
import DatabaseService from "../services/database";
import { colors, spacing, borderRadius, commonStyles, typography } from "../styles/theme";

const NotificationScreen = ({ navigation }) => {
  const { company } = useClerkAuth();
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Load notifications when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      loadNotifications();
    }, [company])
  );

  const loadNotifications = async () => {
    if (!company) return;

    try {
      setLoading(true);
      const notificationData = await DatabaseService.getNotificationsByCompany(company.id);
      setNotifications(notificationData);
    } catch (error) {
      console.error("Error loading notifications:", error);
      Alert.alert("Error", "Failed to load notifications");
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadNotifications();
    setRefreshing(false);
  };

  const markAsRead = async (notificationId) => {
    try {
      await DatabaseService.markNotificationAsRead(notificationId);
      
      // Update local state
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === notificationId 
            ? { ...notification, is_read: 1 }
            : notification
        )
      );
    } catch (error) {
      console.error("Error marking notification as read:", error);
    }
  };

  const markAllAsRead = async () => {
    try {
      const unreadNotifications = notifications.filter(n => n.is_read === 0);
      
      for (const notification of unreadNotifications) {
        await DatabaseService.markNotificationAsRead(notification.id);
      }
      
      // Update local state
      setNotifications(prev => 
        prev.map(notification => ({ ...notification, is_read: 1 }))
      );
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
      Alert.alert("Error", "Failed to mark notifications as read");
    }
  };

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'budget_exceeded':
        return 'wallet-outline';
      case 'benchmark_exceeded':
        return 'trending-up-outline';
      case 'spend_update_overdue':
        return 'time-outline';
      default:
        return 'notifications-outline';
    }
  };

  const getNotificationColor = (type) => {
    switch (type) {
      case 'budget_exceeded':
        return colors.error;
      case 'benchmark_exceeded':
        return colors.warning;
      case 'spend_update_overdue':
        return colors.info;
      default:
        return colors.info;
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);
    
    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)} hours ago`;
    } else if (diffInHours < 48) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString();
    }
  };

  const unreadCount = notifications.filter(n => n.is_read === 0).length;

  if (loading) {
    return (
      <View style={commonStyles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={{ marginTop: spacing.md }}>Loading notifications...</Text>
      </View>
    );
  }

  return (
    <View style={commonStyles.container}>
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Notifications</Text>
          {unreadCount > 0 && (
            <View style={styles.headerActions}>
              <Chip icon="bell" style={styles.unreadChip}>
                {unreadCount} unread
              </Chip>
              <Button
                mode="outlined"
                onPress={markAllAsRead}
                style={styles.markAllButton}
                compact
              >
                Mark all read
              </Button>
            </View>
          )}
        </View>

        {notifications.length === 0 ? (
          <Surface style={styles.emptyState} elevation={1}>
            <Ionicons name="notifications-off-outline" size={64} color={colors.textLight} />
            <Text style={styles.emptyStateTitle}>No notifications</Text>
            <Text style={styles.emptyStateText}>
              You'll receive notifications here when spending exceeds your benchmarks or budgets.
            </Text>
          </Surface>
        ) : (
          <View style={styles.notificationsList}>
            {notifications.map((notification, index) => (
              <Card
                key={notification.id}
                style={[
                  styles.notificationCard,
                  notification.is_read === 0 && styles.unreadCard
                ]}
              >
                <Card.Content style={styles.cardContent}>
                  <View style={styles.notificationHeader}>
                    <View style={styles.iconContainer}>
                      <Ionicons
                        name={getNotificationIcon(notification.type)}
                        size={24}
                        color={getNotificationColor(notification.type)}
                      />
                    </View>
                    
                    <View style={styles.notificationContent}>
                      <View style={styles.titleRow}>
                        <Text style={styles.notificationTitle}>
                          {notification.type === 'budget_exceeded' ? 'Budget Exceeded' :
                           notification.type === 'benchmark_exceeded' ? 'Benchmark Exceeded' :
                           notification.type === 'spend_update_overdue' ? 'Spend Update Overdue' :
                           'Notification'}
                        </Text>
                        {notification.is_read === 0 && (
                          <View style={styles.unreadDot} />
                        )}
                      </View>
                      
                      <Text style={styles.notificationMessage}>
                        {notification.message}
                      </Text>
                      
                      <Text style={styles.notificationDate}>
                        {formatDate(notification.created_at)}
                      </Text>
                    </View>
                    
                    {notification.is_read === 0 && (
                      <IconButton
                        icon="check"
                        size={20}
                        onPress={() => markAsRead(notification.id)}
                        style={styles.markReadButton}
                      />
                    )}
                  </View>
                </Card.Content>
              </Card>
            ))}
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  scrollContainer: {
    padding: spacing.lg,
    paddingBottom: spacing.xxxl,
  },
  header: {
    marginBottom: spacing.xl,
  },
  title: {
    fontSize: typography.fontSizes.xxxl,
    fontWeight: typography.fontWeights.bold,
    color: colors.text,
    marginBottom: spacing.md,
  },
  headerActions: {
    flexDirection: "row",
    alignItems: "center",
    gap: spacing.md,
  },
  unreadChip: {
    backgroundColor: colors.primary + '20',
  },
  markAllButton: {
    borderColor: colors.primary,
  },
  emptyState: {
    alignItems: "center",
    padding: spacing.xxxl,
    borderRadius: borderRadius.lg,
    backgroundColor: colors.surface,
  },
  emptyStateTitle: {
    fontSize: typography.fontSizes.xl,
    fontWeight: typography.fontWeights.semibold,
    color: colors.text,
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
  },
  emptyStateText: {
    fontSize: typography.fontSizes.md,
    color: colors.textSecondary,
    textAlign: "center",
    lineHeight: 22,
  },
  notificationsList: {
    gap: spacing.md,
  },
  notificationCard: {
    borderRadius: borderRadius.lg,
    backgroundColor: colors.surface,
  },
  unreadCard: {
    borderLeftWidth: 4,
    borderLeftColor: colors.primary,
    backgroundColor: colors.primary + '05',
  },
  cardContent: {
    padding: spacing.lg,
  },
  notificationHeader: {
    flexDirection: "row",
    alignItems: "flex-start",
    gap: spacing.md,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    alignItems: "center",
    justifyContent: "center",
    elevation: 2,
  },
  notificationContent: {
    flex: 1,
  },
  titleRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.xs,
  },
  notificationTitle: {
    fontSize: typography.fontSizes.lg,
    fontWeight: typography.fontWeights.semibold,
    color: colors.text,
    flex: 1,
  },
  unreadDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.primary,
    marginLeft: spacing.sm,
  },
  notificationMessage: {
    fontSize: typography.fontSizes.md,
    color: colors.textSecondary,
    lineHeight: 20,
    marginBottom: spacing.sm,
  },
  notificationDate: {
    fontSize: typography.fontSizes.sm,
    color: colors.textLight,
  },
  markReadButton: {
    margin: 0,
  },
});

export default NotificationScreen;
