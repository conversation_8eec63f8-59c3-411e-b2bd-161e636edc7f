import React, { useState, useEffect, useCallback } from "react";
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
  Platform,
  TouchableOpacity,
  Dimensions,
  Image,
} from "react-native";
import {
  Text,
  Card,
  TextInput,
  Button,
  SegmentedButtons,
  ActivityIndicator,
  Divider,
  Chip,
  DataTable,
  IconButton,
  Menu,
  Modal,
  Portal,
} from "react-native-paper";
import { Ionicons } from "@expo/vector-icons";
import DateTimePicker from "@react-native-community/datetimepicker";
// Conditional import for Victory components - use victory-native for mobile only
let VictoryChart, VictoryBar, VictoryAxis, VictoryGroup, VictoryLegend, VictoryTooltip;
if (Platform.OS !== 'web') {
  // For mobile, use victory-native
  try {
    const victory = require("victory-native");
    VictoryChart = victory.VictoryChart;
    VictoryBar = victory.VictoryBar;
    VictoryAxis = victory.VictoryAxis;
    VictoryGroup = victory.VictoryGroup;
    VictoryLegend = victory.VictoryLegend;
    VictoryTooltip = victory.VictoryTooltip;
  } catch (error) {
    console.warn('Victory Native not available:', error);
    // Fallback to placeholder components
    VictoryChart = ({ children, ...props }) => <View {...props}>{children}</View>;
    VictoryBar = (props) => <View />;
    VictoryAxis = (props) => <View />;
    VictoryGroup = ({ children, ...props }) => <View {...props}>{children}</View>;
    VictoryLegend = (props) => <View />;
    VictoryTooltip = (props) => <View />;
  }
} else {
  // For web, create placeholder components
  VictoryChart = ({ children, ...props }) => <View {...props}>{children}</View>;
  VictoryBar = (props) => <View />;
  VictoryAxis = (props) => <View />;
  VictoryGroup = ({ children, ...props }) => <View {...props}>{children}</View>;
  VictoryLegend = (props) => <View />;
  VictoryTooltip = (props) => <View />;
}
import * as XLSX from "xlsx";
import * as FileSystem from "expo-file-system";
import * as Sharing from "expo-sharing";
import * as DocumentPicker from "expo-document-picker";
import DatabaseService from "../services/database";
import { useAuth } from "../contexts/AuthContext";
import { useFocusEffect } from "@react-navigation/native";
import { colors, spacing, borderRadius, commonStyles, typography, elevation } from '../styles/theme';
import { LinearGradient } from 'expo-linear-gradient';

const { width } = Dimensions.get("window");

// Custom DateInput component
const DateInput = ({
  label,
  value,
  onPress,
  onChangeText,
  disabled,
  placeholder,
}) => {
  if (Platform.OS === "web") {
    return (
      <View style={{ marginBottom: spacing.md }}>
        <Text style={{
          marginBottom: spacing.sm,
          color: colors.primary,
          fontSize: typography.fontSizes.sm,
          fontWeight: typography.fontWeights.medium,
        }}>
          {label}
        </Text>
        <View
          style={{
            borderWidth: 2,
            borderColor: colors.primary,
            borderRadius: borderRadius.md,
            backgroundColor: disabled ? colors.surfaceVariant : colors.surface,
            flexDirection: "row",
            alignItems: "center",
            paddingLeft: spacing.md,
            minHeight: 56,
            elevation: elevation.xs,
            shadowColor: colors.primary,
            shadowOffset: { width: 0, height: 1 },
            shadowOpacity: 0.1,
            shadowRadius: 2,
          }}
        >
          <Ionicons
            name="calendar"
            size={20}
            color={colors.primary}
            style={{ marginRight: spacing.sm }}
          />
          <input
            type="date"
            value={value}
            onChange={(e) => onChangeText && onChangeText(e.target.value)}
            disabled={disabled}
            placeholder={placeholder}
            style={{
              border: "none",
              outline: "none",
              background: "transparent",
              fontSize: `${typography.fontSizes.md}px`,
              flex: 1,
              padding: `${spacing.sm}px ${spacing.sm}px`,
              color: disabled ? colors.textLight : colors.text,
            }}
          />
        </View>
      </View>
    );
  }

  return (
    <TouchableOpacity onPress={disabled ? null : onPress} disabled={disabled}>
      <TextInput
        label={label}
        value={value}
        mode="outlined"
        style={{ marginBottom: spacing.lg }}
        disabled={true}
        placeholder={placeholder}
        outlineColor={colors.primary}
        activeOutlineColor={colors.primary}
        textColor={colors.text}
        left={
          <TextInput.Icon
            icon="calendar"
            iconColor={colors.primary}
            onPress={disabled ? null : onPress}
          />
        }
        pointerEvents="none"
      />
    </TouchableOpacity>
  );
};

const BenchmarkDetailScreen = ({ route, navigation }) => {
  const { benchmarkId } = route.params;
  const { user, company } = useAuth();

  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [benchmark, setBenchmark] = useState(null);
  const [actualSpends, setActualSpends] = useState([]);

  // Edit mode state
  const [isEditing, setIsEditing] = useState(false);
  const [editLoading, setEditLoading] = useState(false);
  const [menuVisible, setMenuVisible] = useState(false);

  // Edit form state
  const [editName, setEditName] = useState("");
  const [editAmount, setEditAmount] = useState("");
  const [editPeriod, setEditPeriod] = useState("monthly");
  const [editType, setEditType] = useState("benchmark");
  const [editCurrency, setEditCurrency] = useState("USD");

  // Form state for new actual spend entry
  const [actualAmount, setActualAmount] = useState("");
  const [selectedPeriod, setSelectedPeriod] = useState("daily");
  const [periodStartDate, setPeriodStartDate] = useState("");
  const [periodEndDate, setPeriodEndDate] = useState("");

  // Date picker state
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);

  // Chart data state
  const [chartData, setChartData] = useState(null);
  const [chartLoading, setChartLoading] = useState(false);
  const [hoveredBar, setHoveredBar] = useState(null);

  // Download functionality state
  const [showDownloadModal, setShowDownloadModal] = useState(false);
  const [downloadStartDate, setDownloadStartDate] = useState("");
  const [downloadEndDate, setDownloadEndDate] = useState("");
  const [downloadFormat, setDownloadFormat] = useState("pdf");
  const [isDownloading, setIsDownloading] = useState(false);
  const [showDownloadStartPicker, setShowDownloadStartPicker] = useState(false);
  const [showDownloadEndPicker, setShowDownloadEndPicker] = useState(false);

  // Plan limits state
  const [canExportData, setCanExportData] = useState(false);
  const [canUploadData, setCanUploadData] = useState(false);

  // Upload functionality state
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  const currencies = {
    USD: "$",
    EUR: "€",
    GBP: "£",
    CAD: "C$",
    AUD: "A$",
    JPY: "¥",
    CHF: "CHF",
    CNY: "¥",
    INR: "₹",
  };

  const currencyOptions = [
    { code: "USD", symbol: "$", name: "US Dollar" },
    { code: "EUR", symbol: "€", name: "Euro" },
    { code: "GBP", symbol: "£", name: "British Pound" },
    { code: "CAD", symbol: "C$", name: "Canadian Dollar" },
    { code: "AUD", symbol: "A$", name: "Australian Dollar" },
    { code: "JPY", symbol: "¥", name: "Japanese Yen" },
    { code: "CHF", symbol: "CHF", name: "Swiss Franc" },
    { code: "CNY", symbol: "¥", name: "Chinese Yuan" },
    { code: "INR", symbol: "₹", name: "Indian Rupee" },
  ];

  useEffect(() => {
    loadBenchmarkData();
  }, [benchmarkId]);

  useEffect(() => {
    if (benchmark) {
      prepareChartData();
    }
  }, [benchmark, actualSpends]);

  // Check plan limits for data export and upload
  useEffect(() => {
    const checkPermissions = async () => {
      if (company?.id) {
        try {
          const exportLimitCheck = await DatabaseService.checkPlanLimits(company.id, 'exportData');
          setCanExportData(exportLimitCheck.allowed);

          const uploadLimitCheck = await DatabaseService.checkPlanLimits(company.id, 'uploadHistoricalData');
          setCanUploadData(uploadLimitCheck.allowed);
        } catch (error) {
          console.error('Error checking permissions:', error);
          setCanExportData(false);
          setCanUploadData(false);
        }
      }
    };

    checkPermissions();
  }, [company?.id]);

  // Refresh data when screen comes into focus (e.g., returning from historical data upload)
  useFocusEffect(
    useCallback(() => {
      console.log("BenchmarkDetailScreen focused, reloading data and chart...");
      loadBenchmarkData();
    }, [benchmarkId]),
  );

  const loadBenchmarkData = async () => {
    try {
      console.log("Loading benchmark data for ID:", benchmarkId);

      const benchmarkData = await DatabaseService.getBenchmarkById(benchmarkId);
      const spendsData = await DatabaseService.getActualSpendsByBenchmark(
        benchmarkId,
        10,
      );

      console.log("Loaded benchmark:", benchmarkData);
      console.log("Loaded spends:", spendsData);

      setBenchmark(benchmarkData);
      setActualSpends(spendsData || []);

      if (benchmarkData) {
        setSelectedPeriod(benchmarkData.period);
        setDefaultDatesForPeriod(benchmarkData.period);

        // Populate edit form with current values
        setEditName(benchmarkData.name);
        setEditAmount(benchmarkData.amount.toString());
        setEditPeriod(benchmarkData.period);
        setEditType(benchmarkData.type);
        setEditCurrency(benchmarkData.currency || "USD");
      }
    } catch (error) {
      console.error("Error loading benchmark data:", error);
      Alert.alert("Error", "Failed to load benchmark data");
    } finally {
      setLoading(false);
    }
  };

  const setDefaultDatesForPeriod = (period) => {
    const today = new Date();
    const todayStr = today.toISOString().split("T")[0];

    if (period === "daily") {
      // For daily: same day
      setPeriodStartDate(todayStr);
      setPeriodEndDate(todayStr);
    } else if (period === "weekly") {
      // For weekly: start of current week to end of current week
      const startOfWeek = new Date(today);
      const day = startOfWeek.getDay();
      const diff = startOfWeek.getDate() - day; // First day is Sunday (0)
      startOfWeek.setDate(diff);

      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(startOfWeek.getDate() + 6);

      setPeriodStartDate(startOfWeek.toISOString().split("T")[0]);
      setPeriodEndDate(endOfWeek.toISOString().split("T")[0]);
    } else if (period === "monthly") {
      // For monthly: start of current month to end of current month
      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

      setPeriodStartDate(startOfMonth.toISOString().split("T")[0]);
      setPeriodEndDate(endOfMonth.toISOString().split("T")[0]);
    }
  };

  const calculateVariance = (actualAmount, benchmarkAmount) => {
    // Since actual spend period must match benchmark period, no conversion needed
    return actualAmount - benchmarkAmount;
  };

  const getBenchmarkAmountWithOccupancy = (benchmarkAmount) => {
    if (!company || !company.current_occupancy) return benchmarkAmount;
    return benchmarkAmount * company.current_occupancy;
  };

  const formatCurrencyWithOccupancy = (
    amount,
    currency = "USD",
    showOccupancy = true,
  ) => {
    const symbol = currencies[currency] || "$";
    const adjustedAmount = showOccupancy
      ? getBenchmarkAmountWithOccupancy(amount)
      : amount;
    const occupancyText =
      showOccupancy && company?.current_occupancy
        ? ` (${amount.toFixed(2)} × ${company.current_occupancy})`
        : "";
    return `${symbol}${adjustedAmount.toFixed(2)}${occupancyText}`;
  };

  const validateSpendForm = () => {
    if (!actualAmount.trim()) {
      Alert.alert("Error", "Please enter the actual spend amount");
      return false;
    }

    const numAmount = parseFloat(actualAmount);
    if (isNaN(numAmount) || numAmount < 0) {
      Alert.alert("Error", "Please enter a valid amount (0 or greater)");
      return false;
    }

    if (!periodStartDate) {
      Alert.alert(
        "Error",
        selectedPeriod === "daily"
          ? "Please select the date"
          : "Please select the period start date",
      );
      return false;
    }

    if (selectedPeriod !== "daily" && !periodEndDate) {
      Alert.alert("Error", "Please select the period end date");
      return false;
    }

    if (
      selectedPeriod !== "daily" &&
      new Date(periodStartDate) > new Date(periodEndDate)
    ) {
      Alert.alert("Error", "Start date cannot be after end date");
      return false;
    }

    return true;
  };

  const handleSubmitActualSpend = async () => {
    if (!validateSpendForm()) return;

    setSubmitting(true);

    try {
      const spendData = {
        benchmarkId: benchmark.id,
        amount: parseFloat(actualAmount),
        periodStart: periodStartDate,
        periodEnd: selectedPeriod === "daily" ? periodStartDate : periodEndDate,
        periodType: selectedPeriod,
        createdBy: user.id,
      };

      console.log("Submitting actual spend:", spendData);

      const spendId = await DatabaseService.createActualSpend(spendData);

      console.log("Actual spend created with ID:", spendId);

      // Clear form
      setActualAmount("");
      setDefaultDatesForPeriod(benchmark.period);

      // Reload data
      await loadBenchmarkData();

      Alert.alert("Success", "Actual spend recorded successfully!");
    } catch (error) {
      console.error("Error submitting actual spend:", error);
      Alert.alert("Error", "Failed to record actual spend. Please try again.");
    } finally {
      setSubmitting(false);
    }
  };

  const formatCurrency = (amount, currency = "USD") => {
    const symbol = currencies[currency] || "$";
    return `${symbol}${amount.toFixed(2)}`;
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getVarianceColor = (variance) => {
    if (variance > 0) return colors.error; // Red for over budget
    if (variance < 0) return colors.success; // Green for under budget
    return colors.textSecondary; // Gray for exact match
  };

  const getVarianceIcon = (variance) => {
    if (variance > 0) return "trending-up";
    if (variance < 0) return "trending-down";
    return "trending-flat";
  };

  const validateEditForm = () => {
    if (!editName.trim()) {
      Alert.alert("Error", "Please enter a benchmark name");
      return false;
    }

    if (!editAmount.trim()) {
      Alert.alert("Error", "Please enter an amount");
      return false;
    }

    const numAmount = parseFloat(editAmount);
    if (isNaN(numAmount) || numAmount <= 0) {
      Alert.alert("Error", "Please enter a valid amount greater than 0");
      return false;
    }

    return true;
  };

  const handleSaveEdit = async () => {
    if (!validateEditForm()) return;

    setEditLoading(true);

    try {
      const updateData = {
        name: editName.trim(),
        amount: parseFloat(editAmount),
        period: editPeriod,
        type: editType,
        currency: editCurrency,
      };

      console.log("Updating benchmark:", updateData);

      await DatabaseService.updateBenchmark(benchmark.id, updateData);

      // Reload data to show updated values
      await loadBenchmarkData();

      setIsEditing(false);
      Alert.alert("Success", "Benchmark updated successfully!");
    } catch (error) {
      console.error("Error updating benchmark:", error);
      Alert.alert("Error", "Failed to update benchmark. Please try again.");
    } finally {
      setEditLoading(false);
    }
  };

  const handleCancelEdit = () => {
    // Reset form to original values
    if (benchmark) {
      setEditName(benchmark.name);
      setEditAmount(benchmark.amount.toString());
      setEditPeriod(benchmark.period);
      setEditType(benchmark.type);
      setEditCurrency(benchmark.currency || "USD");
    }
    setIsEditing(false);
  };

  // Date picker handlers
  const handleStartDateChange = (event, selectedDate) => {
    setShowStartDatePicker(Platform.OS === "ios");
    if (selectedDate) {
      const dateString = selectedDate.toISOString().split("T")[0];
      setPeriodStartDate(dateString);
    }
  };

  const handleEndDateChange = (event, selectedDate) => {
    setShowEndDatePicker(Platform.OS === "ios");
    if (selectedDate) {
      const dateString = selectedDate.toISOString().split("T")[0];
      setPeriodEndDate(dateString);
    }
  };

  const showStartDatePickerModal = () => {
    setShowStartDatePicker(true);
  };

  const showEndDatePickerModal = () => {
    setShowEndDatePicker(true);
  };

  // Chart data preparation - Weekly data for last 3 months
  const prepareChartData = async () => {
    if (!benchmark) return;

    setChartLoading(true);
    try {
      // Get last 3 months of data
      const threeMonthsAgo = new Date();
      threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);

      // Get all actual spends for this benchmark
      const allSpends = await DatabaseService.getActualSpendsByBenchmark(
        benchmark.id,
      );

      // Filter to last 3 months
      const recentSpends = allSpends.filter(
        (spend) => new Date(spend.period_start) >= threeMonthsAgo,
      );



      // Helper function to get week key (YYYY-WW format)
      const getWeekKey = (date) => {
        const startOfYear = new Date(date.getFullYear(), 0, 1);
        const pastDaysOfYear = (date - startOfYear) / 86400000;
        const weekNumber = Math.ceil(
          (pastDaysOfYear + startOfYear.getDay() + 1) / 7,
        );
        return `${date.getFullYear()}-W${String(weekNumber).padStart(2, "0")}`;
      };

      // Helper function to get week label based on actual date
      const getWeekLabel = (date) => {
        const monthName = date.toLocaleDateString("en-US", { month: "short" });
        const year = date.getFullYear().toString().slice(-2);

        // Calculate the week number within the month (starting from W1)
        const firstDayOfMonth = new Date(date.getFullYear(), date.getMonth(), 1);
        const dayOfMonth = date.getDate();

        // Calculate which week of the month this date falls in
        // Week 1 starts from day 1, regardless of what day of the week it is
        const weekOfMonth = Math.ceil(dayOfMonth / 7);

        return `W${weekOfMonth} ${monthName} ${year}`;
      };

      // Group by week
      const weeklyData = {};
      recentSpends.forEach((spend) => {
        const date = new Date(spend.period_start);
        const weekKey = getWeekKey(date);

        if (!weeklyData[weekKey]) {
          weeklyData[weekKey] = {
            week: weekKey,
            date: date,
            actualTotal: 0,
            count: 0,
            benchmarkAmount: spend.benchmark_amount_at_time || benchmark.amount,
          };
        }

        weeklyData[weekKey].actualTotal += spend.amount;
        weeklyData[weekKey].count += 1;
      });

      // Only generate weeks that have actual data
      const weeks = [];
      const benchmarkData = [];
      const actualData = [];

      // Get all weeks with data, sorted by date
      const weeksWithData = Object.keys(weeklyData)
        .map(weekKey => ({
          weekKey,
          date: weeklyData[weekKey].date,
          data: weeklyData[weekKey]
        }))
        .sort((a, b) => a.date - b.date);

      // Only show weeks that have actual spending data
      weeksWithData.forEach(({ weekKey, date, data }) => {
        const weekLabel = getWeekLabel(date);
        weeks.push(weekLabel);

        const avgActual = data.actualTotal / data.count;
        actualData.push(Math.round(avgActual * 100) / 100);
        benchmarkData.push(data.benchmarkAmount);
      });



      // Use only weeks with actual data
      const chartWeeks = weeks;
      const chartBenchmarkData = benchmarkData;
      const chartActualData = actualData;

      // Create Victory data format for grouped bars
      const victoryData = chartWeeks.map((week, index) => {
        // Use the already correctly formatted week labels
        return {
          week: week,
          benchmark: chartBenchmarkData[index] || 0,
          actual: chartActualData[index] || 0,
        };
      });

      // Chart should show if we have benchmark data (even if no actual spends yet)
      // This allows users to see the benchmark line even before adding actual data
      const hasData = victoryData.length > 0 && benchmark.amount > 0;

      // Debug logging
      console.log("Chart data prepared:", {
        totalWeeks: victoryData.length,
        weeksWithActualData: victoryData.filter(d => d.actual > 0).length,
        weeksWithBenchmarkData: victoryData.filter(d => d.benchmark > 0).length,
        maxValue: Math.max(...victoryData.map(d => Math.max(d.benchmark, d.actual)), 1),
        sampleData: victoryData.slice(0, 3),
        allWeekLabels: victoryData.map(d => d.week)
      });

      setChartData({
        victoryData: victoryData,
        hasData: hasData,
      });
    } catch (error) {
      console.error("Error preparing chart data:", error);
    } finally {
      setChartLoading(false);
    }
  };

  // Download functionality
  const handleDownloadData = async () => {
    if (!downloadStartDate || !downloadEndDate) {
      Alert.alert("Error", "Please select both start and end dates");
      return;
    }

    if (new Date(downloadStartDate) > new Date(downloadEndDate)) {
      Alert.alert("Error", "Start date must be before end date");
      return;
    }

    setIsDownloading(true);
    try {
      // Get filtered data for the selected period
      const allSpends = await DatabaseService.getActualSpendsByBenchmark(
        benchmark.id,
      );
      const filteredSpends = allSpends.filter((spend) => {
        const spendDate = new Date(spend.period_start);
        const startDate = new Date(downloadStartDate);
        const endDate = new Date(downloadEndDate);
        return spendDate >= startDate && spendDate <= endDate;
      });

      if (filteredSpends.length === 0) {
        Alert.alert("No Data", "No data found for the selected period");
        setIsDownloading(false);
        return;
      }

      if (downloadFormat === "excel") {
        await generateExcelReport(filteredSpends);
      } else {
        await generatePDFReport(filteredSpends);
      }

      setShowDownloadModal(false);
    } catch (error) {
      console.error("Download error:", error);
      Alert.alert("Error", "Failed to generate report. Please try again.");
    } finally {
      setIsDownloading(false);
    }
  };

  const generateExcelReport = async (data) => {
    try {
      // Prepare data for Excel
      const excelData = data.map((spend) => ({
        Date: formatDate(spend.period_start),
        "Period End":
          spend.period_start !== spend.period_end
            ? formatDate(spend.period_end)
            : "",
        Occupancy: spend.occupancy_at_time || 1,
        "Benchmark Amount": spend.benchmark_amount_at_time || benchmark.amount,
        "Actual Amount": spend.amount,
        Variance: spend.variance,
        Currency: benchmark.currency,
        "Posted By": spend.created_by_name || "Unknown",
        "Posted Date": formatDate(spend.created_at),
        "Period Type": spend.period_type,
      }));

      // Create workbook and worksheet
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.json_to_sheet(excelData);

      // Add some styling (column widths)
      const colWidths = [
        { wch: 12 }, // Date
        { wch: 12 }, // Period End
        { wch: 10 }, // Occupancy
        { wch: 15 }, // Benchmark Amount
        { wch: 15 }, // Actual Amount
        { wch: 12 }, // Variance
        { wch: 10 }, // Currency
        { wch: 15 }, // Posted By
        { wch: 12 }, // Posted Date
        { wch: 12 }, // Period Type
      ];
      ws["!cols"] = colWidths;

      // Add worksheet to workbook
      XLSX.utils.book_append_sheet(wb, ws, "Historical Data");

      // Generate file
      const wbout = XLSX.write(wb, { type: "base64", bookType: "xlsx" });
      const filename = `${benchmark.name}_${downloadStartDate}_to_${downloadEndDate}.xlsx`;
      const fileUri = FileSystem.documentDirectory + filename;

      await FileSystem.writeAsStringAsync(fileUri, wbout, {
        encoding: FileSystem.EncodingType.Base64,
      });

      // Share the file
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(fileUri);
      } else {
        Alert.alert("Success", `Excel file saved to: ${fileUri}`);
      }
    } catch (error) {
      console.error("Excel generation error:", error);
      throw error;
    }
  };

  const generatePDFReport = async (data) => {
    try {
      // Create HTML content for PDF
      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>Historical Data Report</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .benchmark-info { background-color: #f5f5f5; padding: 15px; margin-bottom: 20px; border-radius: 5px; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; font-weight: bold; }
            .currency { text-align: right; }
            .variance-positive { color: #d32f2f; }
            .variance-negative { color: #388e3c; }
            .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>Historical Data Report</h1>
            <h2>${benchmark.name}</h2>
          </div>

          <div class="benchmark-info">
            <p><strong>Benchmark:</strong> ${formatCurrency(benchmark.amount, benchmark.currency)} per ${benchmark.period}</p>
            <p><strong>Type:</strong> ${benchmark.type}</p>
            <p><strong>Report Period:</strong> ${formatDate(downloadStartDate)} to ${formatDate(downloadEndDate)}</p>
            <p><strong>Generated:</strong> ${new Date().toLocaleDateString()}</p>
          </div>

          <table>
            <thead>
              <tr>
                <th>Date</th>
                <th>Occupancy</th>
                <th>Benchmark</th>
                <th>Actual</th>
                <th>Variance</th>
                <th>Posted By</th>
                <th>Posted Date</th>
              </tr>
            </thead>
            <tbody>
              ${data
                .map(
                  (spend) => `
                <tr>
                  <td>${formatDate(spend.period_start)}${spend.period_start !== spend.period_end ? ` - ${formatDate(spend.period_end)}` : ""}</td>
                  <td style="text-align: center;">${spend.occupancy_at_time || 1}</td>
                  <td class="currency">${formatCurrency(spend.benchmark_amount_at_time || benchmark.amount, benchmark.currency)}</td>
                  <td class="currency">${formatCurrency(spend.amount, benchmark.currency)}</td>
                  <td class="currency ${spend.variance >= 0 ? "variance-positive" : "variance-negative"}">
                    ${spend.variance >= 0 ? "+" : ""}${formatCurrency(spend.variance, benchmark.currency)}
                  </td>
                  <td>${spend.created_by_name || "Unknown"}</td>
                  <td>${formatDate(spend.created_at)}</td>
                </tr>
              `,
                )
                .join("")}
            </tbody>
          </table>

          <div class="footer">
            <p>Generated by CostCompass - Budget Management System</p>
          </div>
        </body>
        </html>
      `;

      // For web platform, create and download HTML file
      if (Platform.OS === "web") {
        const blob = new Blob([htmlContent], { type: "text/html" });
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `${benchmark.name}_${downloadStartDate}_to_${downloadEndDate}.html`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
        Alert.alert("Success", "Report downloaded successfully");
      } else {
        // For mobile, we'll save as HTML since PDF generation is complex
        const filename = `${benchmark.name}_${downloadStartDate}_to_${downloadEndDate}.html`;
        const fileUri = FileSystem.documentDirectory + filename;

        await FileSystem.writeAsStringAsync(fileUri, htmlContent);

        if (await Sharing.isAvailableAsync()) {
          await Sharing.shareAsync(fileUri);
        } else {
          Alert.alert("Success", `Report saved to: ${fileUri}`);
        }
      }
    } catch (error) {
      console.error("PDF generation error:", error);
      throw error;
    }
  };

  // Download date picker handlers
  const handleDownloadStartDateChange = (event, selectedDate) => {
    setShowDownloadStartPicker(Platform.OS === "ios");
    if (selectedDate) {
      const dateString = selectedDate.toISOString().split("T")[0];
      setDownloadStartDate(dateString);
    }
  };

  const handleDownloadEndDateChange = (event, selectedDate) => {
    setShowDownloadEndPicker(Platform.OS === "ios");
    if (selectedDate) {
      const dateString = selectedDate.toISOString().split("T")[0];
      setDownloadEndDate(dateString);
    }
  };

  const showDownloadStartDatePickerModal = () => {
    setShowDownloadStartPicker(true);
  };

  const showDownloadEndDatePickerModal = () => {
    setShowDownloadEndPicker(true);
  };

  // Upload functionality (simplified version)
  const handleUploadClick = () => {
    navigation.navigate("HistoricalData", { benchmarkId: benchmark.id });
  };

  if (loading) {
    return (
      <View style={commonStyles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text variant="bodyMedium" style={styles.loadingText}>
            Loading benchmark details...
          </Text>
        </View>
      </View>
    );
  }

  if (!benchmark) {
    return (
      <View style={commonStyles.container}>
        <Card style={commonStyles.card}>
          <Card.Content>
            <Text variant="headlineSmall" style={commonStyles.sectionTitle}>Benchmark Not Found</Text>
            <Text variant="bodyMedium" style={commonStyles.sectionSubtitle}>
              The requested benchmark could not be found.
            </Text>
            <Button
              mode="outlined"
              onPress={() => navigation.goBack()}
              style={styles.backButton}
            >
              Go Back
            </Button>
          </Card.Content>
        </Card>
      </View>
    );
  }

  return (
    <View style={commonStyles.container}>
      {/* Split Layout - Blue section on left, content on right */}
      <View style={styles.mainContent}>
        {/* Left Side - Blue Header Section (Desktop only) */}
        {Platform.OS === 'web' && width > 600 && (
          <View style={styles.leftSide}>
            <LinearGradient
              colors={colors.gradientPrimary}
              style={styles.leftSideGradient}
            >
              <View style={styles.leftSideContent}>
                {/* App Logo and Name */}
                <View style={styles.logoSection}>
                  <View style={styles.appLogoContainer}>
                    <Image
                      source={require("../../assets/logo.png")}
                      style={styles.appLogo}
                      resizeMode="contain"
                      onError={(error) => console.log('Logo load error:', error)}
                      onLoad={() => console.log('Logo loaded successfully')}
                    />
                  </View>
                  <Text style={styles.appNameBig}>CostCompass</Text>
                </View>

                {/* Page Header */}
                <View style={styles.pageHeader}>
                  <View style={styles.headerIconContainer}>
                    <Ionicons
                      name={benchmark?.type === 'budget' ? 'wallet' : 'analytics'}
                      size={32}
                      color={colors.textOnPrimary}
                    />
                  </View>
                  <Text style={styles.leftSideTitle}>
                    {benchmark?.name || 'Benchmark Details'}
                  </Text>
                  <Text style={styles.leftSideSubtitle}>
                    {benchmark?.type === 'budget' ? 'Budget' : 'Benchmark'} tracking and analysis
                  </Text>
                </View>

                {/* Benchmark Stats */}
                <View style={styles.leftSideInfo}>
                  <View style={styles.leftSideInfoItem}>
                    <Ionicons name="cash" size={20} color={colors.textOnPrimary} />
                    <Text style={styles.leftSideInfoText}>
                      {formatCurrencyWithOccupancy(benchmark.amount, benchmark.currency)}
                    </Text>
                  </View>
                  <View style={styles.leftSideInfoItem}>
                    <Ionicons name="calendar" size={20} color={colors.textOnPrimary} />
                    <Text style={styles.leftSideInfoText}>
                      {benchmark.period.charAt(0).toUpperCase() + benchmark.period.slice(1)} {benchmark.type}
                    </Text>
                  </View>
                  <View style={styles.leftSideInfoItem}>
                    <Ionicons name="list" size={20} color={colors.textOnPrimary} />
                    <Text style={styles.leftSideInfoText}>
                      {actualSpends.length} actual spend records
                    </Text>
                  </View>
                </View>
              </View>
            </LinearGradient>
          </View>
        )}

        {/* Right Side - Main Content */}
        <View style={styles.rightSide}>
          {/* Mobile Header (only shown on mobile) */}
          {(Platform.OS !== 'web' || width <= 600) && (
            <LinearGradient
              colors={colors.gradientPrimary}
              style={styles.mobileHeader}
            >
              <View style={styles.mobileHeaderContent}>
                {/* Logo for mobile */}
                <View style={styles.mobileLogoContainer}>
                  <Image
                    source={require("../../assets/logo.png")}
                    style={styles.mobileLogo}
                    resizeMode="contain"
                    onError={(error) => console.log('Mobile logo load error:', error)}
                    onLoad={() => console.log('Mobile logo loaded successfully')}
                  />
                </View>
                <View style={styles.headerIconContainer}>
                  <Ionicons
                    name={benchmark?.type === 'budget' ? 'wallet' : 'analytics'}
                    size={32}
                    color={colors.textOnPrimary}
                  />
                </View>
                <Text style={styles.mobileHeaderTitle}>
                  {benchmark?.name || 'Benchmark Details'}
                </Text>
                <Text style={styles.mobileHeaderSubtitle}>
                  {benchmark?.type === 'budget' ? 'Budget' : 'Benchmark'} tracking and analysis
                </Text>
              </View>
            </LinearGradient>
          )}

          <ScrollView
            contentContainerStyle={styles.rightSideScrollContainer}
            style={styles.rightSideScrollView}
          >
      {/* Benchmark Info and Record Actual Spend Side by Side */}
      <View style={styles.cardsRow}>
        {/* Benchmark Info Card */}
        <Card style={[commonStyles.card, styles.cardHalf]}>
        <Card.Content>
          {!isEditing ? (
            <>
              <View style={styles.benchmarkHeader}>
                <Text variant="headlineSmall" style={styles.benchmarkName}>
                  {benchmark.name}
                </Text>
                <View style={styles.headerActions}>
                  <Chip
                    mode="outlined"
                    textStyle={{
                      color: benchmark.type === "budget" ? colors.secondary : colors.primary,
                      fontSize: typography.fontSizes.sm,
                      fontWeight: typography.fontWeights.medium,
                    }}
                    style={{
                      borderColor: benchmark.type === "budget" ? colors.secondary : colors.primary,
                      backgroundColor: "transparent",
                      marginRight: spacing.sm,
                    }}
                  >
                    {benchmark.type}
                  </Chip>

                  {user?.is_admin && (
                    <Menu
                      visible={menuVisible}
                      onDismiss={() => setMenuVisible(false)}
                      anchor={
                        <IconButton
                          icon="dots-vertical"
                          size={20}
                          onPress={() => setMenuVisible(true)}
                        />
                      }
                    >
                      <Menu.Item
                        onPress={() => {
                          setMenuVisible(false);
                          setIsEditing(true);
                        }}
                        title="Edit Benchmark"
                        leadingIcon="pencil"
                      />
                    </Menu>
                  )}
                </View>
              </View>

              <Text variant="headlineMedium" style={styles.benchmarkAmount}>
                {formatCurrencyWithOccupancy(
                  benchmark.amount,
                  benchmark.currency,
                )}
                /{benchmark.period}
              </Text>

              {company?.current_occupancy && company.current_occupancy > 1 && (
                <Text variant="bodySmall" style={styles.occupancyNote}>
                  Base amount:{" "}
                  {formatCurrency(benchmark.amount, benchmark.currency)} ×{" "}
                  {company.current_occupancy} occupancy
                </Text>
              )}

              <Text variant="bodySmall" style={styles.benchmarkDate}>
                Created: {formatDate(benchmark.created_at)}
              </Text>

              {benchmark.updated_at !== benchmark.created_at && (
                <Text variant="bodySmall" style={styles.benchmarkDate}>
                  Last updated: {formatDate(benchmark.updated_at)}
                </Text>
              )}
            </>
          ) : (
            <>
              <Text variant="titleLarge" style={styles.editTitle}>
                Edit Benchmark
              </Text>
              <Divider style={styles.divider} />

              <Text variant="bodyMedium" style={styles.label}>
                Type *
              </Text>
              <SegmentedButtons
                value={editType}
                onValueChange={setEditType}
                buttons={[
                  { value: "benchmark", label: "Benchmark" },
                  { value: "budget", label: "Budget" },
                ]}
                style={styles.segmentedButtons}
              />

              <TextInput
                label={`${editType === "benchmark" ? "Benchmark" : "Budget"} Name *`}
                value={editName}
                onChangeText={setEditName}
                mode="outlined"
                style={styles.input}
                disabled={editLoading}
              />

              <Text variant="bodyMedium" style={styles.label}>
                Currency *
              </Text>
              <Menu
                visible={menuVisible}
                onDismiss={() => setMenuVisible(false)}
                anchor={
                  <Button
                    mode="outlined"
                    onPress={() => setMenuVisible(true)}
                    style={styles.currencyButton}
                    contentStyle={styles.currencyButtonContent}
                    disabled={editLoading}
                  >
                    {
                      currencyOptions.find((c) => c.code === editCurrency)
                        ?.symbol
                    }{" "}
                    {editCurrency} -{" "}
                    {currencyOptions.find((c) => c.code === editCurrency)?.name}
                  </Button>
                }
              >
                {currencyOptions.map((curr) => (
                  <Menu.Item
                    key={curr.code}
                    onPress={() => {
                      setEditCurrency(curr.code);
                      setMenuVisible(false);
                    }}
                    title={`${curr.symbol} ${curr.code} - ${curr.name}`}
                  />
                ))}
              </Menu>

              <TextInput
                label="Amount *"
                value={editAmount}
                onChangeText={setEditAmount}
                mode="outlined"
                keyboardType="numeric"
                style={styles.input}
                disabled={editLoading}
                left={
                  <TextInput.Icon
                    icon={() => (
                      <Text style={styles.currencyIcon}>
                        {
                          currencyOptions.find((c) => c.code === editCurrency)
                            ?.symbol
                        }
                      </Text>
                    )}
                  />
                }
              />

              <Text variant="bodyMedium" style={styles.label}>
                Period *
              </Text>
              <SegmentedButtons
                value={editPeriod}
                onValueChange={setEditPeriod}
                buttons={[
                  { value: "daily", label: "Daily" },
                  { value: "weekly", label: "Weekly" },
                  { value: "monthly", label: "Monthly" },
                ]}
                style={styles.segmentedButtons}
              />

              <View style={styles.editActions}>
                <Button
                  mode="outlined"
                  onPress={handleCancelEdit}
                  style={styles.cancelEditButton}
                  disabled={editLoading}
                >
                  Cancel
                </Button>
                <Button
                  mode="contained"
                  onPress={handleSaveEdit}
                  style={styles.saveEditButton}
                  disabled={editLoading}
                >
                  {editLoading ? (
                    <ActivityIndicator color="white" />
                  ) : (
                    "Save Changes"
                  )}
                </Button>
              </View>
            </>
          )}
        </Card.Content>
        </Card>

        {/* Actual Spend Entry Card */}
        <Card style={[commonStyles.card, styles.cardHalf]}>
          <Card.Content>
            <Text variant="titleLarge" style={commonStyles.sectionTitle}>
              Record Actual Spend
            </Text>
            <Divider style={styles.divider} />

          <TextInput
              label="Actual Amount *"
              value={actualAmount}
              onChangeText={setActualAmount}
              mode="outlined"
              keyboardType="numeric"
              style={commonStyles.input}
              disabled={submitting}
              placeholder="0.00"
              left={
                <TextInput.Icon
                  icon={() => (
                    <Text style={styles.currencyIcon}>
                      {currencies[benchmark.currency] || "$"}
                    </Text>
                  )}
                />
              }
            />

          <Card style={styles.periodInfoCard}>
            <Card.Content>
              <Text variant="bodyMedium" style={styles.periodInfoText}>
                <Text style={styles.periodInfoLabel}>Period Type: </Text>
                <Text style={styles.periodInfoValue}>
                  {selectedPeriod.charAt(0).toUpperCase() +
                    selectedPeriod.slice(1)}
                </Text>
              </Text>
              <Text variant="bodySmall" style={styles.periodInfoSubtext}>
                Actual spend must match the benchmark period type
              </Text>
            </Card.Content>
          </Card>

          <DateInput
            label={
              selectedPeriod === "daily" ? "Date *" : "Period Start Date *"
            }
            value={periodStartDate}
            onPress={showStartDatePickerModal}
            onChangeText={setPeriodStartDate}
            disabled={submitting}
            placeholder="YYYY-MM-DD"
          />

          {selectedPeriod !== "daily" && (
            <DateInput
              label="Period End Date *"
              value={periodEndDate}
              onPress={showEndDatePickerModal}
              onChangeText={setPeriodEndDate}
              disabled={submitting}
              placeholder="YYYY-MM-DD"
            />
          )}

          {/* Date Pickers - Mobile Only */}
          {Platform.OS !== "web" && showStartDatePicker && (
            <DateTimePicker
              value={periodStartDate ? new Date(periodStartDate) : new Date()}
              mode="date"
              display={Platform.OS === "ios" ? "spinner" : "default"}
              onChange={handleStartDateChange}
            />
          )}

          {Platform.OS !== "web" && showEndDatePicker && (
            <DateTimePicker
              value={periodEndDate ? new Date(periodEndDate) : new Date()}
              mode="date"
              display={Platform.OS === "ios" ? "spinner" : "default"}
              onChange={handleEndDateChange}
            />
          )}

          {actualAmount && !isNaN(parseFloat(actualAmount)) && (
            <Card style={styles.varianceCard}>
              <Card.Content>
                <Text variant="titleMedium" style={styles.varianceTitle}>
                  Variance Preview
                </Text>
                {(() => {
                  const adjustedBenchmarkAmount =
                    getBenchmarkAmountWithOccupancy(benchmark.amount);
                  const variance = calculateVariance(
                    parseFloat(actualAmount),
                    adjustedBenchmarkAmount,
                  );
                  return (
                    <View style={styles.varianceRow}>
                      <Ionicons
                        name={getVarianceIcon(variance)}
                        size={20}
                        color={getVarianceColor(variance)}
                      />
                      <Text
                        variant="bodyLarge"
                        style={[
                          styles.varianceText,
                          { color: getVarianceColor(variance) },
                        ]}
                      >
                        {variance > 0 ? "+" : ""}
                        {formatCurrency(variance, benchmark.currency)}
                      </Text>
                      <Text variant="bodySmall" style={styles.varianceLabel}>
                        {variance > 0 ? "Over" : variance < 0 ? "Under" : "On"}{" "}
                        {benchmark.type}
                      </Text>
                    </View>
                  );
                })()}
              </Card.Content>
            </Card>
          )}

          <Button
            mode="contained"
            onPress={handleSubmitActualSpend}
            style={styles.submitButton}
            disabled={submitting}
            buttonColor={colors.primary}
          >
            {submitting ? (
              <ActivityIndicator color="white" />
            ) : (
              "Record Actual Spend"
            )}
          </Button>
        </Card.Content>
        </Card>
      </View>

      {/* Historical Data Card */}
        <Card style={commonStyles.card}>
          <Card.Content>
            <View style={commonStyles.sectionHeader}>
              <Text variant="titleLarge" style={commonStyles.sectionTitle}>
                Recent Activity
              </Text>
            <View style={styles.headerActions}>
              {canUploadData && (
                <IconButton
                  icon="upload"
                  size={20}
                  onPress={handleUploadClick}
                  style={styles.uploadButton}
                />
              )}
              {canExportData && (
                <IconButton
                  icon="download"
                  size={20}
                  onPress={() => setShowDownloadModal(true)}
                  style={styles.downloadButton}
                />
              )}
              <Button
                mode="text"
                compact
                onPress={() =>
                  navigation.navigate("HistoricalData", { benchmarkId })
                }
              >
                View All
              </Button>
            </View>
          </View>
          <Divider style={styles.divider} />

          {actualSpends.length === 0 ? (
              <View style={commonStyles.emptyState}>
                <View style={commonStyles.emptyStateIcon}>
                  <Ionicons name="analytics-outline" size={48} color={colors.textLight} />
                </View>
                <Text style={commonStyles.emptyStateTitle}>
                  No actual spend data recorded yet
                </Text>
                <Text style={commonStyles.emptyStateText}>
                  Use the form above to record your first actual spend entry
                </Text>
              </View>
            ) : (
              <>
                {/* Summary Card */}
                <Card style={styles.summaryCard}>
                <Card.Content>
                  <Text variant="titleMedium" style={styles.summaryTitle}>
                    Performance Summary
                  </Text>
                  <View style={styles.summaryRow}>
                    <View style={styles.summaryItem}>
                      <Text variant="bodySmall" style={styles.summaryLabel}>
                        Total Entries
                      </Text>
                      <Text variant="bodyLarge" style={styles.summaryValue}>
                        {actualSpends.length}
                      </Text>
                    </View>
                    <View style={styles.summaryItem}>
                      <Text variant="bodySmall" style={styles.summaryLabel}>
                        Avg Variance
                      </Text>
                      <Text
                        variant="bodyLarge"
                        style={[
                          styles.summaryValue,
                          {
                            color: getVarianceColor(
                              actualSpends.reduce(
                                (sum, spend) => sum + spend.variance,
                                0,
                              ) / actualSpends.length,
                            ),
                          },
                        ]}
                      >
                        {(() => {
                          const avgVariance =
                            actualSpends.reduce(
                              (sum, spend) => sum + spend.variance,
                              0,
                            ) / actualSpends.length;
                          return `${avgVariance > 0 ? "+" : ""}${formatCurrency(avgVariance, benchmark.currency)}`;
                        })()}
                      </Text>
                    </View>
                    <View style={styles.summaryItem}>
                      <Text variant="bodySmall" style={styles.summaryLabel}>
                        Over Budget
                      </Text>
                      <Text
                        variant="bodyLarge"
                        style={[styles.summaryValue, { color: colors.error }]}
                      >
                        {
                          actualSpends.filter((spend) => spend.variance > 0)
                            .length
                        }
                      </Text>
                    </View>
                  </View>
                </Card.Content>
              </Card>

                {/* Chart Card */}
                <Card style={styles.chartCard}>
                <Card.Content>
                  <Text variant="titleMedium" style={styles.chartTitle}>
                    12-Week Comparison: Benchmark vs Actual
                  </Text>

                  {chartLoading ? (
                    <View style={styles.chartLoadingContainer}>
                      <ActivityIndicator size="large" color={colors.primary} />
                      <Text style={styles.chartLoadingText}>
                        Loading chart data...
                      </Text>
                    </View>
                  ) : chartData && chartData.hasData ? (
                    <View style={styles.chartContainer}>
                      {Platform.OS === 'web' ? (
                        // Web chart implementation using CSS-based bars
                        <View style={styles.webChartContainer}>
                          <View style={styles.webChartWithAxis}>
                            {/* Y-Axis Labels */}
                            <View style={styles.webChartYAxis}>
                              {(() => {
                                const maxValue = Math.max(
                                  ...chartData.victoryData.map(d => Math.max(d.benchmark, d.actual)),
                                  1 // Ensure minimum value of 1 to avoid division by zero
                                );
                                // Add 20% padding to the max value for better visualization
                                const scaledMaxValue = Math.ceil(maxValue * 1.2);
                                const currencySymbol = currencyOptions.find(c => c.code === benchmark.currency)?.symbol || '$';
                                const ticks = [];
                                const tickCount = 8; // More ticks for better granularity
                                for (let i = tickCount; i >= 0; i--) {
                                  const value = Math.round((scaledMaxValue * i) / tickCount);
                                  ticks.push(
                                    <Text key={i} style={styles.webChartYAxisLabel}>
                                      {currencySymbol}{value}
                                    </Text>
                                  );
                                }
                                return ticks;
                              })()}
                            </View>
                            <ScrollView
                              horizontal={true}
                              showsHorizontalScrollIndicator={true}
                              style={styles.webChartScrollView}
                            >
                              <View style={styles.webChart}>
                              {chartData.victoryData.map((item, index) => {
                                const maxValue = Math.max(
                                  ...chartData.victoryData.map(d => Math.max(d.benchmark, d.actual)),
                                  1 // Ensure minimum value of 1 to avoid division by zero
                                );
                                // Use the same scaled max value as y-axis for consistent scaling
                                const scaledMaxValue = Math.ceil(maxValue * 1.2);
                                const benchmarkHeight = Math.max((item.benchmark / scaledMaxValue) * 240, item.benchmark > 0 ? 12 : 0);
                                const actualHeight = Math.max((item.actual / scaledMaxValue) * 240, item.actual > 0 ? 10 : 0);

                                const currencySymbol = currencyOptions.find(c => c.code === benchmark.currency)?.symbol || '$';

                                return (
                                  <View key={index} style={styles.webChartBar}>
                                    <TouchableOpacity
                                      style={styles.webChartBarGroup}
                                      onPress={() => {
                                        console.log('Bar pressed:', index);
                                        setHoveredBar(hoveredBar === index ? null : index);
                                      }}
                                      activeOpacity={0.7}
                                    >
                                      <View
                                        style={[
                                          styles.webChartBarBenchmark,
                                          { height: benchmarkHeight }
                                        ]}
                                      />
                                      <View
                                        style={[
                                          styles.webChartBarActual,
                                          { height: actualHeight }
                                        ]}
                                      />
                                    </TouchableOpacity>

                                    {/* Click Tooltip - Show below the bar instead */}
                                    {hoveredBar === index && (
                                      <View style={styles.webChartTooltipBelow}>
                                        <Text style={styles.webChartTooltipTitle}>📊 {item.week}</Text>
                                        <Text style={styles.webChartTooltipText}>
                                          💰 Benchmark: {currencySymbol}{item.benchmark.toFixed(2)}
                                        </Text>
                                        <Text style={styles.webChartTooltipText}>
                                          💸 Actual: {currencySymbol}{item.actual.toFixed(2)}
                                        </Text>
                                        {item.actual > 0 && item.benchmark > 0 && (
                                          <Text style={[
                                            styles.webChartTooltipText,
                                            {
                                              color: item.actual > item.benchmark ? '#FF5722' : '#4CAF50',
                                              fontWeight: 'bold'
                                            }
                                          ]}>
                                            📈 Variance: {currencySymbol}{(item.actual - item.benchmark).toFixed(2)}
                                          </Text>
                                        )}
                                      </View>
                                    )}

                                    <Text style={styles.webChartBarLabel}>
                                      {item.week.replace(/W(\d+) (\w+) (\d+)/, '$2')}
                                    </Text>
                                    <Text style={styles.webChartBarWeek}>
                                      {item.week.replace(/W(\d+) (\w+) (\d+)/, 'W$1')}
                                    </Text>
                                    <Text style={styles.webChartClickHint}>
                                      {hoveredBar === index ? 'Click to hide' : 'Click for details'}
                                    </Text>
                                  </View>
                                );
                              })}
                              </View>
                            </ScrollView>
                          </View>

                          <View style={styles.webChartLegend}>
                            <View style={styles.webChartLegendItem}>
                              <View style={[styles.webChartLegendColor, { backgroundColor: '#2196F3' }]} />
                              <Text style={styles.webChartLegendText}>Benchmark</Text>
                            </View>
                            <View style={styles.webChartLegendItem}>
                              <View style={[styles.webChartLegendColor, { backgroundColor: '#FF5722' }]} />
                              <Text style={styles.webChartLegendText}>Actual</Text>
                            </View>
                          </View>
                        </View>
                      ) : (
                        <>
                          <ScrollView
                            horizontal={true}
                            showsHorizontalScrollIndicator={true}
                            style={styles.chartScrollView}
                          >
                          <VictoryChart
                          width={Math.max(
                            chartData.victoryData.length * 100, // 100px per week for better spacing
                            Platform.OS === 'web' && width > 768 ?
                              (width * 0.6) - 80 : // Minimum width for split layout
                              width - 80 // Minimum width for mobile
                          )}
                          height={320}
                          domainPadding={{ x: 40 }}
                          padding={{ left: 70, top: 20, right: 40, bottom: 80 }}
                          domain={{
                            y: [0, Math.ceil(Math.max(...chartData.victoryData.map(d => Math.max(d.benchmark, d.actual)), 1) * 1.2)]
                          }}
                        >
                          {/* Y-Axis */}
                          <VictoryAxis
                            dependentAxis
                            tickCount={8}
                            tickFormat={(value) => {
                              const currencySymbol = currencyOptions.find(c => c.code === benchmark.currency)?.symbol || '$';
                              return `${currencySymbol}${Math.round(value)}`;
                            }}
                            style={{
                              tickLabels: {
                                fontSize: 11,
                                fill: colors.textSecondary,
                                fontFamily: Platform.OS === 'ios' ? 'System' : 'Roboto'
                              },
                              grid: { stroke: colors.border, strokeWidth: 0.5 },
                              axis: { stroke: colors.border },
                            }}
                          />

                          {/* X-Axis */}
                          <VictoryAxis
                            tickFormat={(week) => week}
                            style={{
                              tickLabels: {
                                fontSize: 10,
                                fill: colors.textSecondary,
                                angle: -30,
                                textAnchor: 'end',
                                fontFamily: Platform.OS === 'ios' ? 'System' : 'Roboto'
                              },
                              axis: { stroke: colors.border },
                            }}
                          />

                          {/* Grouped Bars */}
                          <VictoryGroup offset={15} colorScale={["#2196F3", "#FF5722"]}>
                            <VictoryBar
                              data={chartData.victoryData}
                              x="week"
                              y="benchmark"
                              style={{
                                data: { fill: "#2196F3" } // Bright blue for benchmark
                              }}
                              labelComponent={<VictoryTooltip
                                flyoutStyle={{
                                  fill: colors.surface,
                                  stroke: colors.primary,
                                  strokeWidth: 1
                                }}
                                style={{
                                  fill: colors.text,
                                  fontSize: 12
                                }}
                              />}
                              events={[{
                                target: "data",
                                eventHandlers: {
                                  onMouseOver: () => {
                                    return [{
                                      target: "labels",
                                      mutation: (props) => {
                                        const currencySymbol = currencyOptions.find(c => c.code === benchmark.currency)?.symbol || '$';
                                        return {
                                          text: `${props.datum.week}\nBenchmark: ${currencySymbol}${props.datum.benchmark.toFixed(2)}`
                                        };
                                      }
                                    }];
                                  },
                                  onMouseOut: () => {
                                    return [{ target: "labels", mutation: () => null }];
                                  }
                                }
                              }]}
                            />
                            <VictoryBar
                              data={chartData.victoryData}
                              x="week"
                              y="actual"
                              style={{
                                data: { fill: "#FF5722" } // Bright orange for actual
                              }}
                              labelComponent={<VictoryTooltip
                                flyoutStyle={{
                                  fill: colors.surface,
                                  stroke: "#FF5722",
                                  strokeWidth: 1
                                }}
                                style={{
                                  fill: colors.text,
                                  fontSize: 12
                                }}
                              />}
                              events={[{
                                target: "data",
                                eventHandlers: {
                                  onMouseOver: () => {
                                    return [{
                                      target: "labels",
                                      mutation: (props) => {
                                        const currencySymbol = currencyOptions.find(c => c.code === benchmark.currency)?.symbol || '$';
                                        return {
                                          text: `${props.datum.week}\nActual: ${currencySymbol}${props.datum.actual.toFixed(2)}`
                                        };
                                      }
                                    }];
                                  },
                                  onMouseOut: () => {
                                    return [{ target: "labels", mutation: () => null }];
                                  }
                                }
                              }]}
                            />
                          </VictoryGroup>
                          </VictoryChart>
                          </ScrollView>
                          <View style={styles.chartLegend}>
                            <View style={styles.legendItem}>
                            <View
                              style={[
                                styles.legendColor,
                                { backgroundColor: '#2196F3' }, // Bright blue for benchmark
                              ]}
                            />
                            <Text style={styles.legendText}>Benchmark</Text>
                          </View>
                            <View style={styles.legendItem}>
                            <View
                              style={[
                                styles.legendColor,
                                { backgroundColor: '#FF5722' }, // Bright orange for actual
                              ]}
                            />
                            <Text style={styles.legendText}>Actual</Text>
                          </View>
                          </View>
                        </>
                      )}
                    </View>
                  ) : (
                    <View style={styles.chartEmptyState}>
                      <Ionicons
                        name="bar-chart-outline"
                        size={48}
                        color={colors.textLight}
                      />
                      <Text style={styles.chartEmptyText}>
                        Not enough data for weekly comparison
                      </Text>
                      <Text style={styles.chartEmptySubtext}>
                        Add more actual spend entries to see the 12-week bar chart
                      </Text>
                    </View>
                  )}
                </Card.Content>
                </Card>

                <DataTable style={styles.dataTable}>
                <DataTable.Header style={styles.tableHeader}>
                  <DataTable.Title
                    style={[styles.dateColumn, styles.headerTitle]}
                  >
                    <Text style={styles.headerText}>Date</Text>
                  </DataTable.Title>
                  <DataTable.Title
                    numeric
                    style={[styles.occupancyColumn, styles.headerTitle]}
                  >
                    <Text style={styles.headerText}>Occupancy</Text>
                  </DataTable.Title>
                  <DataTable.Title
                    numeric
                    style={[styles.amountColumn, styles.headerTitle]}
                  >
                    <Text style={styles.headerText}>Benchmark</Text>
                  </DataTable.Title>
                  <DataTable.Title
                    numeric
                    style={[styles.amountColumn, styles.headerTitle]}
                  >
                    <Text style={styles.headerText}>Actual</Text>
                  </DataTable.Title>
                  <DataTable.Title
                    numeric
                    style={[styles.varianceColumn, styles.headerTitle]}
                  >
                    <Text style={styles.headerText}>Variance</Text>
                  </DataTable.Title>
                  <DataTable.Title
                    style={[styles.userColumn, styles.headerTitle]}
                  >
                    <Text style={styles.headerText}>Posted By</Text>
                  </DataTable.Title>
                  <DataTable.Title
                    style={[styles.postedDateColumn, styles.headerTitle]}
                  >
                    <Text style={styles.headerText}>Posted</Text>
                  </DataTable.Title>
                </DataTable.Header>

                {actualSpends.slice(0, 5).map((spend) => (
                  <DataTable.Row key={spend.id} style={styles.tableRow}>
                    <DataTable.Cell
                      style={[styles.dateColumn, styles.dataCell]}
                    >
                      <Text style={styles.dateText}>
                        {formatDate(spend.period_start)}
                        {spend.period_start !== spend.period_end &&
                          ` - ${formatDate(spend.period_end)}`}
                      </Text>
                    </DataTable.Cell>
                    <DataTable.Cell
                      numeric
                      style={[styles.occupancyColumn, styles.dataCell]}
                    >
                      <Text style={styles.occupancyText}>
                        {spend.occupancy_at_time || 1}
                      </Text>
                    </DataTable.Cell>
                    <DataTable.Cell
                      numeric
                      style={[styles.amountColumn, styles.dataCell]}
                    >
                      <Text style={styles.benchmarkText}>
                        {formatCurrency(
                          spend.benchmark_amount_at_time || benchmark.amount,
                          benchmark.currency,
                        )}
                      </Text>
                    </DataTable.Cell>
                    <DataTable.Cell
                      numeric
                      style={[styles.amountColumn, styles.dataCell]}
                    >
                      <Text style={styles.actualText}>
                        {formatCurrency(spend.amount, benchmark.currency)}
                      </Text>
                    </DataTable.Cell>
                    <DataTable.Cell
                      numeric
                      style={[styles.varianceColumn, styles.dataCell]}
                    >
                      <View style={styles.varianceCell}>
                        <Ionicons
                          name={getVarianceIcon(spend.variance)}
                          size={14}
                          color={getVarianceColor(spend.variance)}
                        />
                        <Text
                          style={[
                            styles.varianceCellText,
                            { color: getVarianceColor(spend.variance) },
                          ]}
                        >
                          {spend.variance > 0 ? "+" : ""}
                          {formatCurrency(
                            Math.abs(spend.variance),
                            benchmark.currency,
                          )}
                        </Text>
                      </View>
                    </DataTable.Cell>
                    <DataTable.Cell
                      style={[styles.userColumn, styles.dataCell]}
                    >
                      <Text style={styles.userText}>
                        {spend.created_by_name || "Unknown"}
                      </Text>
                    </DataTable.Cell>
                    <DataTable.Cell
                      style={[styles.postedDateColumn, styles.dataCell]}
                    >
                      <Text style={styles.postedDateText}>
                        {formatDate(spend.created_at)}
                      </Text>
                    </DataTable.Cell>
                  </DataTable.Row>
                ))}
              </DataTable>
            </>
          )}
        </Card.Content>
      </Card>

      {/* Download Modal */}
      <Portal>
        <Modal
          visible={showDownloadModal}
          onDismiss={() => setShowDownloadModal(false)}
          contentContainerStyle={styles.downloadModal}
        >
          <Card>
            <Card.Content>
              <Text variant="titleLarge" style={styles.downloadModalTitle}>
                Download Historical Data
              </Text>

              <Text variant="bodyMedium" style={styles.downloadModalSubtitle}>
                Select date range and format for your report
              </Text>

              <View style={styles.downloadFormContainer}>
                <Text variant="labelLarge" style={styles.downloadLabel}>
                  Date Range
                </Text>

                <DateInput
                  label="Start Date *"
                  value={downloadStartDate}
                  onPress={showDownloadStartDatePickerModal}
                  onChangeText={setDownloadStartDate}
                  disabled={isDownloading}
                  placeholder="YYYY-MM-DD"
                />

                <DateInput
                  label="End Date *"
                  value={downloadEndDate}
                  onPress={showDownloadEndDatePickerModal}
                  onChangeText={setDownloadEndDate}
                  disabled={isDownloading}
                  placeholder="YYYY-MM-DD"
                />

                <Text variant="labelLarge" style={styles.downloadLabel}>
                  Format
                </Text>

                <SegmentedButtons
                  value={downloadFormat}
                  onValueChange={setDownloadFormat}
                  buttons={[
                    { value: "pdf", label: "PDF" },
                    { value: "excel", label: "Excel" },
                  ]}
                  style={styles.downloadFormatButtons}
                  disabled={isDownloading}
                  theme={{
                    colors: {
                      primary: colors.primary,
                      onPrimary: colors.textOnPrimary,
                      outline: colors.primary,
                      onSurface: colors.primary,
                    }
                  }}
                />

                <View style={styles.downloadModalActions}>
                  <Button
                    mode="outlined"
                    onPress={() => setShowDownloadModal(false)}
                    disabled={isDownloading}
                    style={styles.downloadCancelButton}
                    textColor={colors.primary}
                  >
                    Cancel
                  </Button>
                  <Button
                    mode="contained"
                    onPress={handleDownloadData}
                    disabled={isDownloading}
                    style={styles.downloadConfirmButton}
                    buttonColor={colors.primary}
                  >
                    {isDownloading ? (
                      <ActivityIndicator color="white" size="small" />
                    ) : (
                      "Download"
                    )}
                  </Button>
                </View>
              </View>
            </Card.Content>
          </Card>
        </Modal>
      </Portal>

      {/* Download Date Pickers - Mobile Only */}
      {Platform.OS !== "web" && showDownloadStartPicker && (
        <DateTimePicker
          value={downloadStartDate ? new Date(downloadStartDate) : new Date()}
          mode="date"
          display={Platform.OS === "ios" ? "spinner" : "default"}
          onChange={handleDownloadStartDateChange}
        />
      )}

      {Platform.OS !== "web" && showDownloadEndPicker && (
        <DateTimePicker
          value={downloadEndDate ? new Date(downloadEndDate) : new Date()}
          mode="date"
          display={Platform.OS === "ios" ? "spinner" : "default"}
          onChange={handleDownloadEndDateChange}
        />
      )}
          </ScrollView>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  // Split Layout Styles
  mainContent: {
    flex: 1,
    flexDirection: Platform.OS === 'web' && width > 600 ? 'row' : 'column',
  },
  // Left Side - Blue Section (Desktop only)
  leftSide: {
    flex: 0.4,
    minWidth: 350,
    maxWidth: 450,
  },
  leftSideGradient: {
    flex: 1,
    padding: spacing.lg,
    paddingTop: spacing.md,
    justifyContent: 'flex-start',
  },
  leftSideContent: {
    alignItems: 'flex-start',
    maxWidth: 350,
    alignSelf: 'center',
    width: '100%',
  },
  // Logo Section
  logoSection: {
    alignItems: 'flex-start',
    alignSelf: 'flex-start',
    marginBottom: spacing.xl,
    paddingBottom: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.2)',
    width: '100%',
  },
  appLogoContainer: {
    width: '100%', // Full width of the blue sidebar content area
    height: 120, // Taller to accommodate larger logo
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.lg,
    marginTop: spacing.sm,
    // Remove artificial shadows and borders for more natural integration
  },
  appLogo: {
    width: 280, // Almost full width of the blue sidebar content
    height: 100, // Proportional height
    // Remove tintColor to show original logo colors
  },
  appNameBig: {
    color: colors.textOnPrimary,
    fontSize: 32,
    fontWeight: typography.fontWeights.bold,
    textAlign: 'left',
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
    letterSpacing: 0.5,
  },
  // Page Header Section
  pageHeader: {
    alignItems: 'flex-start',
    marginBottom: spacing.lg,
    width: '100%',
  },
  leftSideTitle: {
    color: colors.textOnPrimary,
    fontSize: 24,
    fontWeight: typography.fontWeights.bold,
    textAlign: 'left',
    marginBottom: spacing.sm,
    textShadowColor: 'rgba(0,0,0,0.1)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  leftSideSubtitle: {
    color: colors.textOnPrimary,
    fontSize: 16,
    textAlign: 'left',
    opacity: 0.95,
    marginBottom: spacing.lg,
    textShadowColor: 'rgba(0,0,0,0.1)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  leftSideInfo: {
    marginTop: spacing.lg,
    alignSelf: 'stretch',
  },
  leftSideInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    padding: spacing.md,
    borderRadius: borderRadius.lg,
  },
  leftSideInfoText: {
    color: colors.textOnPrimary,
    fontSize: 14,
    fontWeight: '500',
    marginLeft: spacing.sm,
    flex: 1,
  },
  // Right Side - Main Content
  rightSide: {
    flex: 1.6,
    backgroundColor: colors.background,
  },
  rightSideScrollContainer: {
    padding: spacing.lg,
    paddingBottom: spacing.xxxl,
  },
  rightSideScrollView: {
    flex: 1,
  },
  // Mobile Header
  mobileHeader: {
    paddingTop: spacing.xxxxl,
    paddingBottom: spacing.xl,
    paddingHorizontal: spacing.xl,
    borderBottomLeftRadius: borderRadius.xxl,
    borderBottomRightRadius: borderRadius.xxl,
  },
  mobileHeaderContent: {
    alignItems: 'center',
  },
  mobileHeaderTitle: {
    color: colors.textOnPrimary,
    fontSize: 24,
    fontWeight: typography.fontWeights.bold,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  mobileHeaderSubtitle: {
    color: colors.textOnPrimary,
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.95,
  },
  // Mobile Logo Styles
  mobileLogoContainer: {
    width: 50,
    height: 50,
    borderRadius: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.08)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
    // More natural integration without artificial borders
  },
  mobileLogo: {
    width: 40,
    height: 40,
  },
  // Cards Layout
  cardsRow: {
    flexDirection: width > 768 ? 'row' : 'column',
    gap: spacing.lg,
    marginBottom: spacing.lg,
  },
  cardHalf: {
    flex: width > 768 ? 1 : undefined,
    minHeight: width > 768 ? 400 : undefined,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: spacing.md,
    color: colors.textSecondary,
  },
  benchmarkHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.sm,
  },
  benchmarkName: {
    flex: 1,
    marginRight: spacing.sm,
    color: colors.text,
    fontWeight: typography.fontWeights.bold,
  },
  benchmarkAmount: {
    color: colors.success,
    fontWeight: typography.fontWeights.bold,
    marginBottom: spacing.sm,
  },
  benchmarkDate: {
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  occupancyNote: {
    color: colors.textSecondary,
    fontSize: typography.fontSizes.xs,
    fontStyle: "italic",
    marginTop: spacing.xs,
  },
  divider: {
    marginBottom: spacing.md,
  },
  label: {
    marginBottom: spacing.sm,
    color: colors.textSecondary,
    fontWeight: typography.fontWeights.medium,
  },
  segmentedButtons: {
    marginBottom: spacing.md,
  },
  currencyIcon: {
    fontSize: typography.fontSizes.md,
    fontWeight: typography.fontWeights.bold,
    color: colors.textSecondary,
  },
  varianceCard: {
    backgroundColor: colors.surfaceVariant,
    marginBottom: spacing.md,
  },
  varianceTitle: {
    color: colors.text,
    marginBottom: spacing.sm,
    fontWeight: typography.fontWeights.bold,
  },
  varianceRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: spacing.sm,
  },
  varianceText: {
    fontWeight: typography.fontWeights.bold,
    fontSize: typography.fontSizes.lg,
  },
  varianceLabel: {
    color: colors.textSecondary,
    marginLeft: spacing.xs,
  },
  submitButton: {
    marginTop: spacing.sm,
  },
  backButton: {
    marginTop: spacing.md,
  },
  varianceCell: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-end",
    gap: spacing.xs,
  },
  varianceCellText: {
    fontSize: typography.fontSizes.xs,
    fontWeight: typography.fontWeights.medium,
    textAlign: "right",
  },
  periodInfoCard: {
    backgroundColor: colors.primaryLight + '20',
    marginBottom: spacing.md,
  },
  periodInfoText: {
    marginBottom: spacing.xs,
  },
  periodInfoLabel: {
    fontWeight: typography.fontWeights.medium,
    color: colors.primary,
  },
  periodInfoValue: {
    fontWeight: typography.fontWeights.bold,
    color: colors.primaryDark,
  },
  periodInfoSubtext: {
    color: colors.primary,
    fontStyle: "italic",
  },
  dateColumn: {
    flex: 1.5,
    paddingHorizontal: 12,
    justifyContent: "center",
  },
  occupancyColumn: {
    flex: 0.8,
    paddingHorizontal: 8,
    justifyContent: "center",
  },
  amountColumn: {
    flex: 1.2,
    paddingHorizontal: 12,
    justifyContent: "center",
  },
  varianceColumn: {
    flex: 1.2,
    paddingHorizontal: 12,
    justifyContent: "center",
  },
  userColumn: {
    flex: 1.2,
    paddingHorizontal: 12,
    justifyContent: "center",
  },
  postedDateColumn: {
    flex: 1.0,
    paddingHorizontal: 12,
    justifyContent: "center",
  },
  dataTable: {
    backgroundColor: "transparent",
  },
  tableHeader: {
    backgroundColor: colors.surfaceVariant,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  tableRow: {
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
    minHeight: 48,
  },
  headerTitle: {
    alignItems: "center",
    justifyContent: "center",
  },
  headerText: {
    fontSize: typography.fontSizes.sm,
    fontWeight: typography.fontWeights.bold,
    color: colors.text,
    textDecorationLine: "underline",
    textAlign: "center",
  },
  dataCell: {
    alignItems: "center",
    justifyContent: "center",
  },
  dateText: {
    fontSize: typography.fontSizes.xs,
    color: colors.text,
    fontWeight: typography.fontWeights.normal,
    textAlign: "left",
  },
  occupancyText: {
    fontSize: typography.fontSizes.xs,
    color: colors.accent,
    fontWeight: typography.fontWeights.medium,
    textAlign: "center",
  },
  benchmarkText: {
    fontSize: typography.fontSizes.xs,
    color: colors.primary,
    fontWeight: typography.fontWeights.medium,
    textAlign: "right",
  },
  actualText: {
    fontSize: typography.fontSizes.xs,
    color: colors.text,
    fontWeight: typography.fontWeights.medium,
    textAlign: "right",
  },
  userText: {
    fontSize: typography.fontSizes.xs,
    color: colors.textSecondary,
    fontWeight: typography.fontWeights.normal,
    textAlign: "left",
  },
  postedDateText: {
    fontSize: typography.fontSizes.xs,
    color: colors.textLight,
    fontWeight: typography.fontWeights.normal,
    textAlign: "left",
  },
  summaryCard: {
    backgroundColor: colors.surfaceVariant,
    marginBottom: spacing.md,
    borderRadius: borderRadius.md,
    elevation: elevation.sm,
  },
  summaryTitle: {
    color: colors.text,
    fontWeight: typography.fontWeights.bold,
    fontSize: typography.fontSizes.lg,
    marginBottom: spacing.sm,
    textAlign: "center",
  },
  summaryRow: {
    flexDirection: "row",
    justifyContent: "space-around",
  },
  summaryItem: {
    alignItems: "center",
    flex: 1,
  },
  summaryLabel: {
    color: colors.textSecondary,
    fontSize: typography.fontSizes.sm,
    marginBottom: spacing.xs,
    textAlign: "center",
  },
  summaryValue: {
    fontWeight: typography.fontWeights.bold,
    fontSize: typography.fontSizes.lg,
    color: colors.text,
    textAlign: "center",
  },
  headerActions: {
    flexDirection: "row",
    alignItems: "center",
  },
  editTitle: {
    color: colors.primary,
    fontWeight: typography.fontWeights.bold,
    fontSize: typography.fontSizes.xl,
    textAlign: "center",
    marginBottom: spacing.sm,
  },
  editActions: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: spacing.md,
    gap: spacing.sm,
  },
  cancelEditButton: {
    flex: 1,
    borderRadius: borderRadius.sm,
  },
  saveEditButton: {
    flex: 1,
    borderRadius: borderRadius.sm,
  },
  currencyButton: {
    marginBottom: spacing.md,
    justifyContent: "flex-start",
    borderRadius: borderRadius.sm,
  },
  currencyButtonContent: {
    justifyContent: "flex-start",
  },
  chartCard: {
    backgroundColor: colors.surface,
    marginBottom: spacing.md,
    borderRadius: borderRadius.md,
    elevation: elevation.sm,
    width: '100%',
    alignSelf: 'stretch',
    overflow: 'visible', // Allow tooltips to show outside card
  },
  chartTitle: {
    color: colors.text,
    fontWeight: typography.fontWeights.bold,
    fontSize: typography.fontSizes.lg,
    marginBottom: spacing.md,
    textAlign: "center",
  },
  chartContainer: {
    alignItems: "center",
    width: '100%',
    overflow: 'hidden',
  },
  chart: {
    marginVertical: spacing.sm,
    borderRadius: borderRadius.md,
  },
  chartLegend: {
    flexDirection: "row",
    justifyContent: "center",
    marginTop: spacing.sm,
    gap: spacing.lg,
  },
  legendItem: {
    flexDirection: "row",
    alignItems: "center",
    gap: spacing.sm,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: borderRadius.xs,
  },
  legendText: {
    fontSize: typography.fontSizes.sm,
    color: colors.textSecondary,
    fontWeight: typography.fontWeights.medium,
  },
  chartLoadingContainer: {
    alignItems: "center",
    paddingVertical: spacing.xxl,
  },
  chartLoadingText: {
    marginTop: spacing.sm,
    color: colors.textSecondary,
    fontSize: typography.fontSizes.md,
  },
  chartEmptyState: {
    alignItems: "center",
    paddingVertical: spacing.xxl,
  },
  chartEmptyText: {
    color: colors.textSecondary,
    marginTop: spacing.md,
    textAlign: "center",
    fontSize: typography.fontSizes.lg,
    fontWeight: typography.fontWeights.medium,
  },
  chartEmptySubtext: {
    color: colors.textLight,
    marginTop: spacing.sm,
    textAlign: "center",
    fontSize: typography.fontSizes.md,
  },
  webChartContainer: {
    flex: 1,
    paddingVertical: spacing.xl,
    paddingHorizontal: spacing.lg,
    overflow: 'visible', // Allow tooltips to show outside container
  },
  webChartWithAxis: {
    flexDirection: "row",
    alignItems: "flex-end",
  },
  webChartYAxis: {
    justifyContent: "space-between",
    height: 260,
    paddingRight: spacing.sm,
    paddingBottom: spacing.xl + spacing.md, // Account for x-axis labels
  },
  webChartYAxisLabel: {
    fontSize: typography.fontSizes.xs,
    color: colors.textSecondary,
    textAlign: "right",
    minWidth: 50,
    lineHeight: 16,
  },
  webChartScrollView: {
    marginBottom: spacing.lg,
    flex: 1,
    overflow: 'visible', // Allow tooltips to show outside scroll view
  },
  webChart: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-end",
    height: 320,
    paddingHorizontal: spacing.md,
    paddingTop: 140, // Add top padding for tooltips
    marginBottom: spacing.xl,
    minWidth: 800, // Minimum width for better visibility
    width: '100%',
    overflow: 'visible', // Allow tooltips to show outside chart area
  },
  webChartBar: {
    flex: 1,
    alignItems: "center",
    marginHorizontal: 2,
    position: "relative",
    cursor: Platform.OS === 'web' ? 'pointer' : 'default',
  },
  webChartBarGroup: {
    flexDirection: "row",
    alignItems: "flex-end",
    justifyContent: "center",
    height: 260,
    marginBottom: spacing.sm,
  },
  webChartBarBenchmark: {
    width: 16,
    backgroundColor: "#2196F3",
    marginRight: 3,
    borderRadius: 3,
    minHeight: 8,
  },
  webChartBarActual: {
    width: 16,
    backgroundColor: "#FF5722",
    borderRadius: 3,
    minHeight: 5,
  },
  webChartBarLabel: {
    fontSize: typography.fontSizes.xs,
    color: colors.text,
    textAlign: "center",
    marginTop: spacing.xs,
    fontWeight: typography.fontWeights.medium,
  },
  webChartBarWeek: {
    fontSize: typography.fontSizes.xs,
    color: colors.textSecondary,
    textAlign: "center",
    marginTop: 2,
  },
  webChartClickHint: {
    fontSize: 10,
    color: colors.primary,
    textAlign: "center",
    marginTop: 4,
    fontStyle: 'italic',
  },
  webChartLegend: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginTop: spacing.md,
  },
  webChartLegendItem: {
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: spacing.md,
  },
  webChartLegendColor: {
    width: 16,
    height: 16,
    borderRadius: 2,
    marginRight: spacing.xs,
  },
  webChartLegendText: {
    fontSize: typography.fontSizes.sm,
    color: colors.text,
    fontWeight: typography.fontWeights.medium,
  },

  webChartTooltip: {
    position: "absolute",
    top: -160, // Fixed position above the bar
    left: -60, // Center the tooltip
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    minWidth: 160,
    maxWidth: 200,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.4,
    shadowRadius: 12,
    elevation: 30, // Higher elevation for web
    borderWidth: 3,
    borderColor: '#2196F3',
    zIndex: 999999, // Very high z-index
  },
  webChartTooltipTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#000000',
    textAlign: "center",
    marginBottom: 8,
  },
  webChartTooltipText: {
    fontSize: 12,
    color: '#333333',
    textAlign: "center",
    marginBottom: 4,
  },
  webChartTooltipDivider: {
    height: 1,
    backgroundColor: '#E0E0E0',
    marginVertical: 8,
  },
  webChartTooltipBelow: {
    marginTop: 10,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 12,
    minWidth: 140,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    borderWidth: 2,
    borderColor: '#2196F3',
    alignSelf: 'center',
  },
  webChartTooltipDebug: {
    position: "absolute",
    top: -15,
    right: -15,
    backgroundColor: 'red',
    padding: 2,
    borderRadius: 3,
  },
  webChartTooltipArrow: {
    position: "absolute",
    bottom: -10,
    left: "50%",
    marginLeft: -10,
    width: 0,
    height: 0,
    borderLeftWidth: 10,
    borderRightWidth: 10,
    borderTopWidth: 10,
    borderLeftColor: "transparent",
    borderRightColor: "transparent",
    borderTopColor: '#FFFFFF',
  },
  downloadButton: {
    margin: 0,
  },
  uploadButton: {
    margin: 0,
    marginRight: 4,
  },
  downloadModal: {
    backgroundColor: colors.surface,
    padding: spacing.xl,
    margin: spacing.xl,
    borderRadius: borderRadius.lg,
    maxHeight: "80%",
    elevation: elevation.lg,
    shadowColor: colors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  downloadModalTitle: {
    textAlign: "center",
    marginBottom: spacing.sm,
    color: colors.primary,
    fontWeight: typography.fontWeights.bold,
    fontSize: typography.fontSizes.xl,
  },
  downloadModalSubtitle: {
    textAlign: "center",
    marginBottom: spacing.xl,
    color: colors.textSecondary,
    fontSize: typography.fontSizes.md,
  },
  downloadFormContainer: {
    gap: spacing.lg,
  },
  downloadLabel: {
    color: colors.primary,
    marginBottom: spacing.sm,
    fontWeight: typography.fontWeights.semiBold,
    fontSize: typography.fontSizes.md,
  },
  downloadFormatButtons: {
    marginBottom: spacing.sm,
    borderRadius: borderRadius.md,
    elevation: elevation.xs,
    shadowColor: colors.primary,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  downloadModalActions: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: spacing.xl,
    gap: spacing.md,
  },
  downloadCancelButton: {
    flex: 1,
    borderRadius: borderRadius.md,
    borderColor: colors.primary,
  },
  downloadConfirmButton: {
    flex: 1,
    borderRadius: borderRadius.md,
    elevation: elevation.sm,
    shadowColor: colors.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  // Header styles
  headerIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
    elevation: elevation.sm,
    shadowColor: 'rgba(0, 0, 0, 0.3)',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  chartScrollView: {
    marginHorizontal: -10, // Allow chart to extend slightly beyond card padding
  },
});

export default BenchmarkDetailScreen;
