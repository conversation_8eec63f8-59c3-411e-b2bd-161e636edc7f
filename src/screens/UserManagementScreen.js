import React, { useState, useEffect } from "react";
import { View, StyleSheet, ScrollView, Alert, Platform, Dimensions, Image } from "react-native";
import {
  Text,
  Card,
  TextInput,
  Button,
  ActivityIndicator,
  Divider,
  List,
  Chip,
  FAB,
  Switch,
  IconButton,
  Searchbar,
  Menu,
  Checkbox,
  Dialog,
  Portal,
  RadioButton,
  HelperText,
  Surface,
} from "react-native-paper";
import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from "../contexts/AuthContext";
import DatabaseService from "../services/database";
import {
  colors,
  spacing,
  borderRadius,
  commonStyles,
  typography,
  elevation,
} from "../styles/theme";

const { width } = Dimensions.get("window");

const UserManagementScreen = ({ navigation }) => {
  const { user, company } = useAuth();
  const [loading, setLoading] = useState(true);
  const [users, setUsers] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [inviteLoading, setInviteLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [bulkMode, setBulkMode] = useState(false);
  const [showBulkMenu, setShowBulkMenu] = useState(false);
  const [userStats, setUserStats] = useState(null);

  // Dialog states
  const [editDialogVisible, setEditDialogVisible] = useState(false);
  const [resetPasswordDialogVisible, setResetPasswordDialogVisible] =
    useState(false);
  const [addUserDialogVisible, setAddUserDialogVisible] = useState(false);
  const [confirmDialogVisible, setConfirmDialogVisible] = useState(false);
  const [selectedUserForEdit, setSelectedUserForEdit] = useState(null);
  const [confirmAction, setConfirmAction] = useState(null);

  // Invite form state
  const [inviteEmail, setInviteEmail] = useState("");
  const [inviteName, setInviteName] = useState("");
  const [invitePassword, setInvitePassword] = useState("");
  const [makeAdmin, setMakeAdmin] = useState(false);

  // Spending permissions state
  const [canSubmitSpending, setCanSubmitSpending] = useState(false);
  const [spendingScope, setSpendingScope] = useState("all"); // 'all' or 'specific'
  const [selectedBenchmarks, setSelectedBenchmarks] = useState([]);
  const [availableBenchmarks, setAvailableBenchmarks] = useState([]);

  // Plan limits state
  const [userLimits, setUserLimits] = useState({ allowed: true, limit: -1, current: 0 });

  // Edit form state
  const [editName, setEditName] = useState("");
  const [editEmail, setEditEmail] = useState("");
  const [newPassword, setNewPassword] = useState("");

  useEffect(() => {
    loadUsers();
    loadUserStats();
    loadBenchmarks();
  }, [company]);

  useEffect(() => {
    filterUsers();
  }, [users, searchQuery]);

  const loadUsers = async () => {
    if (!company) return;

    try {
      setLoading(true);
      const companyUsers = await DatabaseService.getUsersByCompany(company.id);
      setUsers(companyUsers);

      // Check user limits
      const limitCheck = await DatabaseService.checkPlanLimits(company.id, 'users');
      setUserLimits(limitCheck);
    } catch (error) {
      console.error("Error loading users:", error);
      Alert.alert("Error", "Failed to load users: " + error.message);
    } finally {
      setLoading(false);
    }
  };

  const loadUserStats = async () => {
    if (!company) return;

    try {
      const stats = await DatabaseService.getUserStats(company.id);
      setUserStats(stats);
    } catch (error) {
      console.error("Error loading user stats:", error);
    }
  };

  const loadBenchmarks = async () => {
    if (!company) return;

    try {
      const benchmarks = await DatabaseService.getBenchmarksByCompany(
        company.id,
      );
      setAvailableBenchmarks(benchmarks);
    } catch (error) {
      console.error("Error loading benchmarks:", error);
    }
  };

  const filterUsers = () => {
    if (!searchQuery.trim()) {
      setFilteredUsers(users);
      return;
    }

    const filtered = users.filter(
      (userItem) =>
        userItem.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        userItem.email.toLowerCase().includes(searchQuery.toLowerCase()),
    );
    setFilteredUsers(filtered);
  };

  const validateInviteForm = () => {
    if (!inviteEmail.trim()) {
      Alert.alert("Error", "Please enter an email address");
      return false;
    }

    if (!inviteName.trim()) {
      Alert.alert("Error", "Please enter a name");
      return false;
    }

    if (!invitePassword.trim()) {
      Alert.alert("Error", "Please enter a password");
      return false;
    }

    if (invitePassword.length < 6) {
      Alert.alert("Error", "Password must be at least 6 characters long");
      return false;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(inviteEmail)) {
      Alert.alert("Error", "Please enter a valid email address");
      return false;
    }

    // Check if email already exists
    const existingUser = users.find(
      (u) => u.email.toLowerCase() === inviteEmail.toLowerCase(),
    );
    if (existingUser) {
      Alert.alert("Error", "A user with this email already exists");
      return false;
    }

    // Validate spending permissions
    if (
      canSubmitSpending &&
      spendingScope === "specific" &&
      selectedBenchmarks.length === 0
    ) {
      Alert.alert(
        "Error",
        "Please select at least one benchmark or budget for specific scope permissions",
      );
      return false;
    }

    return true;
  };

  const handleInviteUser = async () => {
    if (!validateInviteForm()) return;

    setInviteLoading(true);

    try {
      // Check plan limits before creating user
      const limitCheck = await DatabaseService.checkPlanLimits(company.id, 'users');
      if (!limitCheck.allowed) {
        const limitText = limitCheck.limit === -1
          ? "unlimited users"
          : `${limitCheck.limit} users`;
        Alert.alert(
          "User Limit Reached",
          `Your ${company?.subscription_plan || 'current'} plan allows ${limitText} and you currently have ${limitCheck.current}. Please upgrade your plan to add more users.`,
          [
            { text: "Cancel", style: "cancel" },
            { text: "Upgrade Plan", onPress: () => navigation.navigate("Subscription") }
          ]
        );
        setInviteLoading(false);
        return;
      }

      // Build permissions array based on selections
      const permissions = [];

      if (canSubmitSpending) {
        if (spendingScope === "all") {
          permissions.push({
            type: "spending",
            action: "submit",
            scope: "all",
          });
        } else if (
          spendingScope === "specific" &&
          selectedBenchmarks.length > 0
        ) {
          permissions.push({
            type: "spending",
            action: "submit",
            scope: "specific",
            benchmarkIds: selectedBenchmarks,
          });
        }
      } else {
        // View only permission
        permissions.push({
          type: "spending",
          action: "view",
          scope: "all",
        });
      }

      const userData = {
        email: inviteEmail.trim(),
        password: invitePassword,
        name: inviteName.trim(),
        isAdmin: makeAdmin,
        companyId: company.id,
        permissions: permissions,
      };

      console.log("Creating new user:", { ...userData, password: "[HIDDEN]" });

      const userId = await DatabaseService.createUser(userData);

      console.log("User created with ID:", userId);

      // Clear form and close dialog
      setInviteEmail("");
      setInviteName("");
      setInvitePassword("");
      setMakeAdmin(false);
      setCanSubmitSpending(false);
      setSpendingScope("all");
      setSelectedBenchmarks([]);
      setAddUserDialogVisible(false);

      // Reload users
      await loadUsers();
      await loadUserStats();

      Alert.alert(
        "Success",
        `User "${inviteName}" has been added successfully!`,
      );
    } catch (error) {
      console.error("Error creating user:", error);
      Alert.alert("Error", "Failed to create user. Please try again.");
    } finally {
      setInviteLoading(false);
    }
  };

  const handleToggleAdmin = async (targetUser) => {
    if (targetUser.id === user.id) {
      Alert.alert("Error", "You cannot change your own admin status");
      return;
    }

    setConfirmAction({
      type: "toggleAdmin",
      user: targetUser,
      title: "Confirm Action",
      message: `Are you sure you want to ${targetUser.is_admin ? "remove admin rights from" : "make"} ${targetUser.name} ${targetUser.is_admin ? "" : "an admin"}?`,
      action: async () => {
        try {
          await DatabaseService.updateUser(targetUser.id, {
            name: targetUser.name,
            isAdmin: !targetUser.is_admin,
            permissions: targetUser.permissions,
          });

          await loadUsers();
          Alert.alert(
            "Success",
            `${targetUser.name} has been ${targetUser.is_admin ? "removed as admin" : "made an admin"}`,
          );
        } catch (error) {
          console.error("Error updating user:", error);
          Alert.alert(
            "Error",
            "Failed to update user permissions: " + error.message,
          );
        }
      },
    });
    setConfirmDialogVisible(true);
  };

  const handleDeleteUser = async (targetUser) => {
    if (targetUser.id === user.id) {
      Alert.alert("Error", "You cannot delete your own account");
      return;
    }

    setConfirmAction({
      type: "deleteUser",
      user: targetUser,
      title: "Delete User",
      message: `Are you sure you want to delete ${targetUser.name}? This action cannot be undone.`,
      action: async () => {
        try {
          await DatabaseService.deleteUser(targetUser.id);
          await loadUsers();
          await loadUserStats();
          Alert.alert("Success", `${targetUser.name} has been deleted`);
        } catch (error) {
          console.error("Error deleting user:", error);
          Alert.alert("Error", "Failed to delete user: " + error.message);
        }
      },
    });
    setConfirmDialogVisible(true);
  };

  const handleEditUser = (targetUser) => {
    setSelectedUserForEdit(targetUser);
    setEditName(targetUser.name);
    setEditEmail(targetUser.email);
    setEditDialogVisible(true);
  };

  const handleSaveUserEdit = async () => {
    if (!editName.trim() || !editEmail.trim()) {
      Alert.alert("Error", "Please fill in all fields");
      return;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(editEmail)) {
      Alert.alert("Error", "Please enter a valid email address");
      return;
    }

    try {
      await DatabaseService.updateUserProfile(selectedUserForEdit.id, {
        name: editName.trim(),
        email: editEmail.trim(),
      });

      setEditDialogVisible(false);
      await loadUsers();
      Alert.alert("Success", "User profile updated successfully");
    } catch (error) {
      console.error("Error updating user profile:", error);
      Alert.alert("Error", "Failed to update user profile");
    }
  };

  const handleResetPassword = (targetUser) => {
    setSelectedUserForEdit(targetUser);
    setNewPassword("");
    setResetPasswordDialogVisible(true);
  };

  const handleSavePasswordReset = async () => {
    if (!newPassword.trim()) {
      Alert.alert("Error", "Please enter a new password");
      return;
    }

    if (newPassword.length < 6) {
      Alert.alert("Error", "Password must be at least 6 characters long");
      return;
    }

    try {
      await DatabaseService.resetUserPassword(
        selectedUserForEdit.id,
        newPassword,
      );
      setResetPasswordDialogVisible(false);
      Alert.alert("Success", "Password reset successfully");
    } catch (error) {
      console.error("Error resetting password:", error);
      Alert.alert("Error", "Failed to reset password");
    }
  };

  const handleToggleUserStatus = async (targetUser) => {
    if (targetUser.id === user.id) {
      Alert.alert("Error", "You cannot change your own account status");
      return;
    }

    const action = targetUser.is_active ? "deactivate" : "activate";
    setConfirmAction({
      type: "toggleStatus",
      user: targetUser,
      title: "Confirm Action",
      message: `Are you sure you want to ${action} ${targetUser.name}?`,
      action: async () => {
        try {
          await DatabaseService.toggleUserStatus(
            targetUser.id,
            !targetUser.is_active,
          );
          await loadUsers();
          await loadUserStats();
          Alert.alert("Success", `${targetUser.name} has been ${action}d`);
        } catch (error) {
          console.error("Error updating user status:", error);
          Alert.alert(
            "Error",
            "Failed to update user status: " + error.message,
          );
        }
      },
    });
    setConfirmDialogVisible(true);
  };

  const toggleBulkMode = () => {
    setBulkMode(!bulkMode);
    setSelectedUsers([]);
  };

  const toggleUserSelection = (userId) => {
    if (selectedUsers.includes(userId)) {
      setSelectedUsers(selectedUsers.filter((id) => id !== userId));
    } else {
      setSelectedUsers([...selectedUsers, userId]);
    }
  };

  const handleBulkDelete = () => {
    if (selectedUsers.length === 0) {
      Alert.alert("Error", "Please select users to delete");
      return;
    }

    if (selectedUsers.includes(user.id)) {
      Alert.alert("Error", "You cannot delete your own account");
      return;
    }

    Alert.alert(
      "Bulk Delete",
      `Are you sure you want to delete ${selectedUsers.length} user(s)? This action cannot be undone.`,
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: async () => {
            try {
              await DatabaseService.bulkDeleteUsers(selectedUsers);
              await loadUsers();
              await loadUserStats();
              setSelectedUsers([]);
              setBulkMode(false);
              Alert.alert(
                "Success",
                `${selectedUsers.length} user(s) deleted successfully`,
              );
            } catch (error) {
              console.error("Error bulk deleting users:", error);
              Alert.alert("Error", "Failed to delete users");
            }
          },
        },
      ],
    );
  };

  const handleBulkToggleAdmin = (makeAdmin) => {
    if (selectedUsers.length === 0) {
      Alert.alert("Error", "Please select users to modify");
      return;
    }

    if (selectedUsers.includes(user.id)) {
      Alert.alert("Error", "You cannot change your own admin status");
      return;
    }

    const action = makeAdmin ? "make admin" : "remove admin rights";
    Alert.alert(
      "Bulk Admin Change",
      `Are you sure you want to ${action} for ${selectedUsers.length} user(s)?`,
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Confirm",
          onPress: async () => {
            try {
              await DatabaseService.bulkToggleAdmin(selectedUsers, makeAdmin);
              await loadUsers();
              setSelectedUsers([]);
              setBulkMode(false);
              Alert.alert(
                "Success",
                `Admin status updated for ${selectedUsers.length} user(s)`,
              );
            } catch (error) {
              console.error("Error bulk updating admin status:", error);
              Alert.alert("Error", "Failed to update admin status");
            }
          },
        },
      ],
    );
  };

  const formatLastLogin = (lastLogin) => {
    if (!lastLogin) return "Never";
    const date = new Date(lastLogin);
    return (
      date.toLocaleDateString() +
      " " +
      date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
    );
  };

  const handleConfirmAction = async () => {
    setConfirmDialogVisible(false);
    if (confirmAction?.action) {
      try {
        await confirmAction.action();
      } catch (error) {
        console.error("Confirmation action failed:", error);
        Alert.alert("Error", "Action failed: " + error.message);
      }
    }
  };

  if (!company) {
    return (
      <View style={commonStyles.container}>
        <Card style={commonStyles.card}>
          <Card.Content>
            <Text variant="headlineSmall" style={commonStyles.sectionTitle}>
              No Company Data
            </Text>
            <Text variant="bodyMedium" style={commonStyles.sectionSubtitle}>
              No company information available.
            </Text>
          </Card.Content>
        </Card>
      </View>
    );
  }

  return (
    <View style={commonStyles.container}>
      {/* Split Layout - Blue section on left, content on right */}
      <View style={styles.mainContent}>
        {/* Left Side - Blue Header Section (Desktop only) */}
        {Platform.OS === 'web' && width > 600 && (
          <View style={styles.leftSide}>
            <LinearGradient
              colors={colors.gradientPrimary}
              style={styles.leftSideGradient}
            >
              <View style={styles.leftSideContent}>
                {/* App Logo and Name */}
                <View style={styles.logoSection}>
                  <View style={styles.appLogoContainer}>
                    <Image
                      source={require("../../assets/logo.png")}
                      style={styles.appLogo}
                      resizeMode="contain"
                    />
                  </View>
                  <Text style={styles.appNameBig}>CostCompass</Text>
                </View>

                {/* Page Header */}
                <View style={styles.pageHeader}>
                  <View style={styles.headerIconContainer}>
                    <Ionicons
                      name="people"
                      size={32}
                      color={colors.textOnPrimary}
                    />
                  </View>
                  <Text style={styles.leftSideTitle}>
                    User Management
                  </Text>
                  <Text style={styles.leftSideSubtitle}>
                    Manage users and permissions for {company?.name}
                  </Text>
                </View>

                {/* User Stats */}
                {userStats && (
                  <View style={styles.leftSideInfo}>
                    <View style={styles.leftSideInfoItem}>
                      <Ionicons name="people" size={20} color={colors.textOnPrimary} />
                      <Text style={styles.leftSideInfoText}>
                        {userStats.total} total users
                      </Text>
                    </View>
                    <View style={styles.leftSideInfoItem}>
                      <Ionicons name="checkmark-circle" size={20} color={colors.textOnPrimary} />
                      <Text style={styles.leftSideInfoText}>
                        {userStats.active} active users
                      </Text>
                    </View>
                    <View style={styles.leftSideInfoItem}>
                      <Ionicons name="shield-checkmark" size={20} color={colors.textOnPrimary} />
                      <Text style={styles.leftSideInfoText}>
                        {userStats.admins} administrators
                      </Text>
                    </View>
                  </View>
                )}
              </View>
            </LinearGradient>
          </View>
        )}

        {/* Right Side - Main Content */}
        <View style={styles.rightSide}>
          {/* Mobile Header (only shown on mobile) */}
          {(Platform.OS !== 'web' || width <= 600) && (
            <LinearGradient
              colors={colors.gradientPrimary}
              style={styles.mobileHeader}
            >
              <View style={styles.mobileHeaderContent}>
                <View style={styles.headerIconContainer}>
                  <Ionicons
                    name="people"
                    size={32}
                    color={colors.textOnPrimary}
                  />
                </View>
                <Text style={styles.mobileHeaderTitle}>
                  User Management
                </Text>
                <Text style={styles.mobileHeaderSubtitle}>
                  Manage users and permissions for {company?.name}
                </Text>
              </View>
            </LinearGradient>
          )}

          <ScrollView
            contentContainerStyle={styles.rightSideScrollContainer}
            style={styles.rightSideScrollView}
            showsVerticalScrollIndicator={false}
          >
        {/* User Stats Card - Soldo-inspired */}
        {userStats && (
          <Card style={commonStyles.cardLarge}>
            <Card.Content>
              <Text variant="titleLarge" style={styles.sectionTitle}>
                User Statistics
              </Text>
              <View style={styles.statsGrid}>
                <Surface style={styles.statCard} elevation={elevation.sm}>
                  <View style={styles.statContent}>
                    <View style={[styles.statIcon, { backgroundColor: colors.primary + '20' }]}>
                      <Ionicons name="people" size={24} color={colors.primary} />
                    </View>
                    <Text style={styles.statValue}>{userStats.total}</Text>
                    <Text style={styles.statLabel}>Total Users</Text>
                  </View>
                </Surface>
                <Surface style={styles.statCard} elevation={elevation.sm}>
                  <View style={styles.statContent}>
                    <View style={[styles.statIcon, { backgroundColor: colors.success + '20' }]}>
                      <Ionicons name="checkmark-circle" size={24} color={colors.success} />
                    </View>
                    <Text style={[styles.statValue, { color: colors.success }]}>
                      {userStats.active}
                    </Text>
                    <Text style={styles.statLabel}>Active</Text>
                  </View>
                </Surface>
                <Surface style={styles.statCard} elevation={elevation.sm}>
                  <View style={styles.statContent}>
                    <View style={[styles.statIcon, { backgroundColor: colors.error + '20' }]}>
                      <Ionicons name="close-circle" size={24} color={colors.error} />
                    </View>
                    <Text style={[styles.statValue, { color: colors.error }]}>
                      {userStats.inactive}
                    </Text>
                    <Text style={styles.statLabel}>Inactive</Text>
                  </View>
                </Surface>
                <Surface style={styles.statCard} elevation={elevation.sm}>
                  <View style={styles.statContent}>
                    <View style={[styles.statIcon, { backgroundColor: colors.accent + '20' }]}>
                      <Ionicons name="shield-checkmark" size={24} color={colors.accent} />
                    </View>
                    <Text style={[styles.statValue, { color: colors.accent }]}>
                      {userStats.admins}
                    </Text>
                    <Text style={styles.statLabel}>Admins</Text>
                  </View>
                </Surface>
              </View>
            </Card.Content>
          </Card>
        )}

        {/* Search and Controls - Soldo-inspired */}
        <Card style={commonStyles.cardLarge}>
          <Card.Content>
            <Searchbar
              placeholder="Search users..."
              onChangeText={setSearchQuery}
              value={searchQuery}
              style={styles.searchBar}
            />

            <View style={styles.controlsContainer}>
              <Button
                mode={bulkMode ? "contained" : "outlined"}
                onPress={toggleBulkMode}
                compact
                icon="checkbox-multiple-marked"
              >
                {bulkMode ? "Exit Bulk" : "Bulk Mode"}
              </Button>

              <Button
                mode="outlined"
                onPress={() => navigation.navigate("InvitationManagement")}
                compact
                icon="email-plus"
              >
                Invite Users
              </Button>

              <Button
                mode="outlined"
                onPress={() => {
                  if (!userLimits.allowed) {
                    const limitText = userLimits.limit === -1
                      ? "unlimited users"
                      : `${userLimits.limit} users`;
                    Alert.alert(
                      "User Limit Reached",
                      `Your ${company?.subscription_plan || 'current'} plan allows ${limitText} and you currently have ${userLimits.current}. Please upgrade your plan to add more users.`,
                      [
                        { text: "Cancel", style: "cancel" },
                        { text: "Upgrade Plan", onPress: () => navigation.navigate("Subscription") }
                      ]
                    );
                    return;
                  }
                  setAddUserDialogVisible(true);
                }}
                compact
                icon="plus"
                disabled={!userLimits.allowed}
              >
                Add User
              </Button>
            </View>

            {bulkMode && selectedUsers.length > 0 && (
              <View style={styles.bulkActions}>
                <Text variant="bodyMedium" style={styles.bulkText}>
                  {selectedUsers.length} user(s) selected
                </Text>
                <View style={styles.bulkButtons}>
                  <Menu
                    visible={showBulkMenu}
                    onDismiss={() => setShowBulkMenu(false)}
                    anchor={
                      <Button
                        mode="outlined"
                        onPress={() => setShowBulkMenu(true)}
                        compact
                        icon="dots-vertical"
                      >
                        Actions
                      </Button>
                    }
                  >
                    <Menu.Item
                      onPress={() => {
                        setShowBulkMenu(false);
                        handleBulkToggleAdmin(true);
                      }}
                      title="Make Admin"
                    />
                    <Menu.Item
                      onPress={() => {
                        setShowBulkMenu(false);
                        handleBulkToggleAdmin(false);
                      }}
                      title="Remove Admin"
                    />
                    <Menu.Item
                      onPress={() => {
                        setShowBulkMenu(false);
                        handleBulkDelete();
                      }}
                      title="Delete Users"
                    />
                  </Menu>
                </View>
              </View>
            )}
          </Card.Content>
        </Card>

        {/* Users List Card - Soldo-inspired */}
        <Card style={commonStyles.cardLarge}>
          <Card.Content>
            <View style={styles.header}>
              <Text variant="headlineSmall" style={commonStyles.sectionTitle}>
                Company Users ({filteredUsers.length})
              </Text>
            </View>

            <Divider style={styles.divider} />

            {loading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.primary} />
                <Text variant="bodyMedium" style={styles.loadingText}>
                  Loading users...
                </Text>
              </View>
            ) : filteredUsers.length === 0 ? (
              <View style={commonStyles.emptyState}>
                <View style={commonStyles.emptyStateIcon}>
                  <Ionicons
                    name="people-outline"
                    size={48}
                    color={colors.textLight}
                  />
                </View>
                <Text style={commonStyles.emptyStateTitle}>
                  {searchQuery
                    ? "No users match your search"
                    : "No users found"}
                </Text>
              </View>
            ) : (
              filteredUsers.map((userItem) => (
                <Card key={userItem.id} style={commonStyles.listItem}>
                  <Card.Content>
                    <View style={styles.userHeader}>
                      {bulkMode && (
                        <Checkbox
                          status={
                            selectedUsers.includes(userItem.id)
                              ? "checked"
                              : "unchecked"
                          }
                          onPress={() => toggleUserSelection(userItem.id)}
                          disabled={userItem.id === user.id}
                        />
                      )}

                      <View style={styles.userInfo}>
                        <Text variant="titleMedium" style={styles.userName}>
                          {userItem.name}
                        </Text>
                        <Text variant="bodyMedium" style={styles.userEmail}>
                          {userItem.email}
                        </Text>
                        <Text variant="bodySmall" style={styles.userLastLogin}>
                          Last login: {formatLastLogin(userItem.last_login)}
                        </Text>
                        <View style={styles.userTags}>
                          {userItem.is_admin && (
                            <Chip mode="outlined" style={styles.adminChip}>
                              Admin
                            </Chip>
                          )}
                          {!userItem.is_active && (
                            <Chip mode="outlined" style={styles.inactiveChip}>
                              Inactive
                            </Chip>
                          )}
                          {userItem.id === user.id && (
                            <Chip
                              mode="outlined"
                              style={styles.currentUserChip}
                            >
                              You
                            </Chip>
                          )}
                        </View>
                      </View>

                      {!bulkMode && userItem.id !== user.id && (
                        <View style={styles.userActions}>
                          <IconButton
                            icon="account-edit"
                            size={20}
                            onPress={() => handleEditUser(userItem)}
                            iconColor="#2196F3"
                          />
                          <IconButton
                            icon="key"
                            size={20}
                            onPress={() => handleResetPassword(userItem)}
                            iconColor="#FF9800"
                          />
                          <IconButton
                            icon={
                              userItem.is_admin
                                ? "account-minus"
                                : "account-plus"
                            }
                            size={20}
                            onPress={() => handleToggleAdmin(userItem)}
                            iconColor={
                              userItem.is_admin ? "#f44336" : "#4CAF50"
                            }
                          />
                          <IconButton
                            icon={
                              userItem.is_active
                                ? "account-off"
                                : "account-check"
                            }
                            size={20}
                            onPress={() => handleToggleUserStatus(userItem)}
                            iconColor={
                              userItem.is_active ? "#FF5722" : "#4CAF50"
                            }
                          />
                          <IconButton
                            icon="delete"
                            size={20}
                            onPress={() => handleDeleteUser(userItem)}
                            iconColor="#f44336"
                          />
                        </View>
                      )}
                    </View>
                  </Card.Content>
                </Card>
              ))
            )}
          </Card.Content>
        </Card>
          </ScrollView>
        </View>
      </View>

      {/* Edit User Dialog */}
      <Portal>
        <Dialog
          visible={editDialogVisible}
          onDismiss={() => setEditDialogVisible(false)}
        >
          <Dialog.Title>Edit User Profile</Dialog.Title>
          <Dialog.Content>
            <TextInput
              label="Full Name"
              value={editName}
              onChangeText={setEditName}
              mode="outlined"
              style={commonStyles.input}
            />
            <TextInput
              label="Email Address"
              value={editEmail}
              onChangeText={setEditEmail}
              mode="outlined"
              keyboardType="email-address"
              autoCapitalize="none"
              style={commonStyles.input}
            />
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setEditDialogVisible(false)}>Cancel</Button>
            <Button onPress={handleSaveUserEdit}>Save</Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      {/* Reset Password Dialog */}
      <Portal>
        <Dialog
          visible={resetPasswordDialogVisible}
          onDismiss={() => setResetPasswordDialogVisible(false)}
        >
          <Dialog.Title>Reset Password</Dialog.Title>
          <Dialog.Content>
            <Text variant="bodyMedium" style={styles.dialogText}>
              Reset password for {selectedUserForEdit?.name}
            </Text>
            <TextInput
              label="New Password"
              value={newPassword}
              onChangeText={setNewPassword}
              mode="outlined"
              secureTextEntry
              style={commonStyles.input}
            />
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setResetPasswordDialogVisible(false)}>
              Cancel
            </Button>
            <Button onPress={handleSavePasswordReset}>Reset</Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      {/* Add User Dialog */}
      <Portal>
        <Dialog
          visible={addUserDialogVisible}
          onDismiss={() => setAddUserDialogVisible(false)}
        >
          <Dialog.Title>Add New User</Dialog.Title>
          <Dialog.Content>
            <TextInput
              label="Full Name *"
              value={inviteName}
              onChangeText={setInviteName}
              mode="outlined"
              style={commonStyles.input}
              disabled={inviteLoading}
            />

            <TextInput
              label="Email Address *"
              value={inviteEmail}
              onChangeText={setInviteEmail}
              mode="outlined"
              keyboardType="email-address"
              autoCapitalize="none"
              style={commonStyles.input}
              disabled={inviteLoading}
            />

            <TextInput
              label="Password *"
              value={invitePassword}
              onChangeText={setInvitePassword}
              mode="outlined"
              secureTextEntry
              style={commonStyles.input}
              disabled={inviteLoading}
            />

            <View style={styles.adminToggleDialog}>
              <Text variant="bodyMedium">Make this user an admin</Text>
              <Switch
                value={makeAdmin}
                onValueChange={setMakeAdmin}
                disabled={inviteLoading}
              />
            </View>

            <Divider style={styles.divider} />

            <Text variant="titleMedium" style={styles.permissionsTitle}>
              Spending Data Permissions
            </Text>

            <View style={styles.permissionSection}>
              <Text variant="bodyMedium" style={styles.permissionLabel}>
                Data Access Level
              </Text>

              <View style={styles.radioGroup}>
                <View style={styles.radioOption}>
                  <RadioButton
                    value="view"
                    status={!canSubmitSpending ? "checked" : "unchecked"}
                    onPress={() => setCanSubmitSpending(false)}
                    disabled={inviteLoading}
                  />
                  <Text variant="bodyMedium">
                    View Only - Can see spending data but cannot submit
                  </Text>
                </View>

                <View style={styles.radioOption}>
                  <RadioButton
                    value="submit"
                    status={canSubmitSpending ? "checked" : "unchecked"}
                    onPress={() => setCanSubmitSpending(true)}
                    disabled={inviteLoading}
                  />
                  <Text variant="bodyMedium">
                    Can Submit - Can view and submit actual spending figures
                  </Text>
                </View>
              </View>
            </View>

            {canSubmitSpending && (
              <View style={styles.permissionSection}>
                <Text variant="bodyMedium" style={styles.permissionLabel}>
                  Spending Submission Scope
                </Text>

                <View style={styles.radioGroup}>
                  <View style={styles.radioOption}>
                    <RadioButton
                      value="all"
                      status={spendingScope === "all" ? "checked" : "unchecked"}
                      onPress={() => setSpendingScope("all")}
                      disabled={inviteLoading}
                    />
                    <Text variant="bodyMedium">All Benchmarks & Budgets</Text>
                  </View>

                  <View style={styles.radioOption}>
                    <RadioButton
                      value="specific"
                      status={
                        spendingScope === "specific" ? "checked" : "unchecked"
                      }
                      onPress={() => setSpendingScope("specific")}
                      disabled={inviteLoading}
                    />
                    <Text variant="bodyMedium">
                      Specific Benchmarks & Budgets
                    </Text>
                  </View>
                </View>

                {spendingScope === "specific" && (
                  <View style={styles.benchmarkSelection}>
                    <Text variant="bodySmall" style={styles.selectionLabel}>
                      Select Benchmarks & Budgets:
                    </Text>
                    {availableBenchmarks.length === 0 ? (
                      <Text variant="bodySmall" style={styles.noBenchmarks}>
                        No benchmarks or budgets available
                      </Text>
                    ) : (
                      availableBenchmarks.map((benchmark) => (
                        <View key={benchmark.id} style={styles.benchmarkOption}>
                          <Checkbox
                            status={
                              selectedBenchmarks.includes(benchmark.id)
                                ? "checked"
                                : "unchecked"
                            }
                            onPress={() => {
                              if (selectedBenchmarks.includes(benchmark.id)) {
                                setSelectedBenchmarks(
                                  selectedBenchmarks.filter(
                                    (id) => id !== benchmark.id,
                                  ),
                                );
                              } else {
                                setSelectedBenchmarks([
                                  ...selectedBenchmarks,
                                  benchmark.id,
                                ]);
                              }
                            }}
                            disabled={inviteLoading}
                          />
                          <View style={styles.benchmarkInfo}>
                            <Text variant="bodyMedium">{benchmark.name}</Text>
                            <Text
                              variant="bodySmall"
                              style={styles.benchmarkType}
                            >
                              {benchmark.type} - {benchmark.period}
                            </Text>
                          </View>
                        </View>
                      ))
                    )}
                  </View>
                )}
              </View>
            )}
          </Dialog.Content>
          <Dialog.Actions>
            <Button
              onPress={() => {
                setAddUserDialogVisible(false);
                // Clear form
                setInviteEmail("");
                setInviteName("");
                setInvitePassword("");
                setMakeAdmin(false);
                setCanSubmitSpending(false);
                setSpendingScope("all");
                setSelectedBenchmarks([]);
              }}
              disabled={inviteLoading}
            >
              Cancel
            </Button>
            <Button onPress={handleInviteUser} disabled={inviteLoading}>
              {inviteLoading ? <ActivityIndicator size="small" /> : "Add User"}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      {/* Confirmation Dialog */}
      <Portal>
        <Dialog
          visible={confirmDialogVisible}
          onDismiss={() => setConfirmDialogVisible(false)}
        >
          <Dialog.Title>{confirmAction?.title}</Dialog.Title>
          <Dialog.Content>
            <Text variant="bodyMedium">{confirmAction?.message}</Text>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setConfirmDialogVisible(false)}>
              Cancel
            </Button>
            <Button
              onPress={handleConfirmAction}
              buttonColor={
                confirmAction?.type === "deleteUser" ? "#f44336" : undefined
              }
            >
              {confirmAction?.type === "deleteUser" ? "Delete" : "Confirm"}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  // Split Layout Styles
  mainContent: {
    flex: 1,
    flexDirection: Platform.OS === 'web' && width > 600 ? 'row' : 'column',
  },
  // Left Side - Blue Section (Desktop only)
  leftSide: {
    flex: 0.4,
    minWidth: 350,
    maxWidth: 450,
  },
  leftSideGradient: {
    flex: 1,
    padding: spacing.lg,
    paddingTop: spacing.md,
    justifyContent: 'flex-start',
  },
  leftSideContent: {
    alignItems: 'flex-start',
    maxWidth: 350,
    alignSelf: 'center',
    width: '100%',
  },
  // Logo Section
  logoSection: {
    alignItems: 'flex-start',
    alignSelf: 'flex-start',
    marginBottom: spacing.xl,
    paddingBottom: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.2)',
    width: '100%',
  },
  appLogoContainer: {
    width: '100%', // Full width of the blue sidebar content area
    height: 120, // Taller to accommodate larger logo
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.lg,
    marginTop: spacing.sm,
    // Remove artificial shadows and borders for more natural integration
  },
  appLogo: {
    width: 280, // Almost full width of the blue sidebar content
    height: 100, // Proportional height
    // Remove tintColor to show original logo colors
  },
  appNameBig: {
    color: colors.textOnPrimary,
    fontSize: 32,
    fontWeight: typography.fontWeights.bold,
    textAlign: 'left',
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
    letterSpacing: 0.5,
  },
  // Page Header Section
  pageHeader: {
    alignItems: 'flex-start',
    marginBottom: spacing.lg,
    width: '100%',
  },
  leftSideTitle: {
    color: colors.textOnPrimary,
    fontSize: 24,
    fontWeight: typography.fontWeights.bold,
    textAlign: 'left',
    marginBottom: spacing.sm,
    textShadowColor: 'rgba(0,0,0,0.1)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  leftSideSubtitle: {
    color: colors.textOnPrimary,
    fontSize: 16,
    textAlign: 'left',
    opacity: 0.95,
    marginBottom: spacing.lg,
    textShadowColor: 'rgba(0,0,0,0.1)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  leftSideInfo: {
    marginTop: spacing.lg,
    alignSelf: 'stretch',
  },
  leftSideInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    padding: spacing.md,
    borderRadius: borderRadius.lg,
  },
  leftSideInfoText: {
    color: colors.textOnPrimary,
    fontSize: 14,
    fontWeight: '500',
    marginLeft: spacing.sm,
    flex: 1,
  },
  // Right Side - Main Content
  rightSide: {
    flex: 1.6,
    backgroundColor: colors.background,
  },
  rightSideScrollContainer: {
    padding: spacing.lg,
    paddingBottom: spacing.xxxl,
  },
  rightSideScrollView: {
    flex: 1,
  },
  // Mobile Header
  mobileHeader: {
    paddingTop: spacing.xxxxl,
    paddingBottom: spacing.xl,
    paddingHorizontal: spacing.xl,
    borderBottomLeftRadius: borderRadius.xxl,
    borderBottomRightRadius: borderRadius.xxl,
  },
  mobileHeaderContent: {
    alignItems: 'center',
  },
  mobileHeaderTitle: {
    color: colors.textOnPrimary,
    fontSize: 24,
    fontWeight: typography.fontWeights.bold,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  mobileHeaderSubtitle: {
    color: colors.textOnPrimary,
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.95,
  },
  headerIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
    elevation: elevation.sm,
    shadowColor: 'rgba(0, 0, 0, 0.3)',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },

  // Soldo-style Section Title
  sectionTitle: {
    color: colors.text,
    fontWeight: typography.fontWeights.bold,
    fontSize: typography.fontSizes.xl,
    marginBottom: spacing.lg,
  },

  // Soldo-style Stats Grid
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.lg,
    marginTop: spacing.lg,
  },
  statCard: {
    flex: 1,
    minWidth: 120,
    borderRadius: borderRadius.lg,
    backgroundColor: colors.surface,
  },
  statContent: {
    alignItems: 'center',
    padding: spacing.xl,
    gap: spacing.sm,
  },
  statIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  statValue: {
    fontWeight: typography.fontWeights.bold,
    color: colors.text,
    fontSize: typography.fontSizes.xxl,
  },
  statLabel: {
    color: colors.textSecondary,
    textAlign: 'center',
    fontSize: typography.fontSizes.sm,
  },

  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.sm,
  },
  divider: {
    marginBottom: spacing.md,
  },
  loadingContainer: {
    alignItems: "center",
    paddingVertical: spacing.xl,
  },
  loadingText: {
    marginTop: spacing.md,
    color: colors.textSecondary,
  },
  userHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontWeight: typography.fontWeights.bold,
    marginBottom: spacing.xs,
    color: colors.text,
  },
  userEmail: {
    color: colors.textSecondary,
    marginBottom: spacing.sm,
  },
  userTags: {
    flexDirection: "row",
    gap: spacing.sm,
  },
  adminChip: {
    borderColor: colors.success,
  },
  currentUserChip: {
    borderColor: colors.primary,
  },
  userActions: {
    flexDirection: "row",
  },
  searchBar: {
    marginBottom: spacing.md,
  },
  controlsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: spacing.sm,
  },
  bulkActions: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: spacing.md,
    padding: spacing.sm,
    backgroundColor: colors.primaryLight + "20",
    borderRadius: borderRadius.sm,
  },
  bulkText: {
    color: colors.primary,
    fontWeight: typography.fontWeights.bold,
  },
  bulkButtons: {
    flexDirection: "row",
    gap: spacing.sm,
  },
  userLastLogin: {
    color: colors.textLight,
    fontSize: typography.fontSizes.xs,
    marginBottom: spacing.xs,
  },
  inactiveChip: {
    borderColor: colors.error,
  },
  dialogText: {
    marginBottom: spacing.md,
    color: colors.textSecondary,
  },
  adminToggleDialog: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: spacing.sm,
    marginBottom: spacing.sm,
  },
  permissionsTitle: {
    color: colors.primary,
    fontWeight: typography.fontWeights.bold,
    marginBottom: spacing.sm,
  },
  permissionSection: {
    marginBottom: spacing.md,
  },
  permissionLabel: {
    fontWeight: typography.fontWeights.bold,
    marginBottom: spacing.sm,
    color: colors.text,
  },
  radioGroup: {
    marginLeft: spacing.sm,
  },
  radioOption: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.sm,
  },
  benchmarkSelection: {
    marginTop: spacing.sm,
    marginLeft: spacing.md,
    padding: spacing.sm,
    backgroundColor: colors.surfaceVariant,
    borderRadius: borderRadius.sm,
  },
  selectionLabel: {
    fontWeight: typography.fontWeights.bold,
    marginBottom: spacing.sm,
    color: colors.text,
  },
  noBenchmarks: {
    fontStyle: "italic",
    color: colors.textSecondary,
  },
  benchmarkOption: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.sm,
  },
  benchmarkInfo: {
    marginLeft: spacing.sm,
    flex: 1,
  },
  benchmarkType: {
    color: colors.textSecondary,
    fontSize: typography.fontSizes.xs,
  },
});

export default UserManagementScreen;
