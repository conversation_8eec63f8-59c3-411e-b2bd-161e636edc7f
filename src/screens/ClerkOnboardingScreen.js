import React, { useState } from 'react';
import { View, StyleSheet, Alert, Dimensions, Platform, ScrollView, TouchableOpacity } from 'react-native';
import {
  Text,
  TextInput,
  Button,
  Card,
  ActivityIndicator,
} from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useUser } from '@clerk/clerk-expo';
import DatabaseService from '../services/database';
import SubscriptionModal from '../components/SubscriptionModal';

import {
  colors,
  spacing,
  borderRadius,
  commonStyles,
  typography,
} from '../styles/theme';

const { width } = Dimensions.get("window");
const isDesktop = Platform.OS === "web" && width > 768;

const ClerkOnboardingScreen = ({ onComplete }) => {
  const { user: clerkUser } = useUser();
  const [companyName, setCompanyName] = useState('');
  const [registrationNumber, setRegistrationNumber] = useState('');
  const [address, setAddress] = useState('');
  const [capacity, setCapacity] = useState('');
  const [currentOccupancy, setCurrentOccupancy] = useState('');
  const [capacityType, setCapacityType] = useState('beds');
  const [loading, setLoading] = useState(false);
  const [subscriptionModalVisible, setSubscriptionModalVisible] = useState(false);
  const [subscriptionData, setSubscriptionData] = useState(null);
  const [step, setStep] = useState(1); // 1: Company details, 2: Subscription

  const handleCreateCompany = async () => {
    if (!companyName.trim()) {
      Alert.alert('Error', 'Please enter a company name');
      return;
    }

    if (!capacity.trim()) {
      Alert.alert('Error', 'Please enter capacity');
      return;
    }

    // Show subscription modal first
    setSubscriptionModalVisible(true);
  };

  const handleSubscriptionComplete = async (subscriptionInfo) => {
    console.log('=== SUBSCRIPTION COMPLETED ===');
    console.log('Subscription info received:', subscriptionInfo);

    setSubscriptionData(subscriptionInfo);
    setSubscriptionModalVisible(false);
    setLoading(true);
    
    try {
      const email = clerkUser.emailAddresses[0]?.emailAddress;
      const name = clerkUser.fullName || clerkUser.firstName || 'User';
      
      console.log('Creating company and user for Clerk user:', { email, name });

      // Check if registration number already exists (if provided)
      if (registrationNumber.trim()) {
        const registrationExists = await DatabaseService.checkRegistrationNumberExists(
          registrationNumber.trim()
        );
        if (registrationExists) {
          Alert.alert(
            'Registration Number Exists',
            'A company with this registration number already exists. Please contact the administrator to add you to the existing company account.'
          );
          setLoading(false);
          return;
        }
      }

      // Create company
      const companyData = {
        name: companyName.trim(),
        registration: registrationNumber.trim() || null,
        address: address.trim() || null,
        capacity: parseInt(capacity),
        currentOccupancy: currentOccupancy ? parseInt(currentOccupancy) : 0,
        capacityType,
      };

      const companyId = await DatabaseService.createCompany(companyData);
      console.log('Company created with ID:', companyId);

      // Create user in database
      const userData = {
        email,
        password: 'clerk_managed', // Placeholder since Clerk manages auth
        name,
        isAdmin: true, // First user is admin
        companyId,
        permissions: ['all'], // Admin permissions
      };

      const userId = await DatabaseService.createUser(userData);
      console.log('User created with ID:', userId);

      // Add user to company with admin role
      await DatabaseService.addUserToCompany(userId, companyId, {
        isAdmin: true,
        permissions: ['all'],
        isActive: true,
      });
      console.log('Added user to company with ID:', companyId);

      // Create subscription with payment data
      if (subscriptionData) {
        const subscriptionInfo = {
          company_id: companyId,
          plan_type: subscriptionData.plan,
          status: 'active',
          billing_cycle: subscriptionData.billing,
          subscription_start_date: new Date().toISOString(),
          subscription_end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
        };

        await DatabaseService.createSubscription(subscriptionInfo);
        console.log('Subscription created');
      }

      Alert.alert(
        'Welcome to CostCompass!',
        'Your company has been set up successfully. You can now start managing your benchmarks.',
        [
          {
            text: 'Get Started',
            onPress: () => onComplete?.(),
          },
        ]
      );

    } catch (error) {
      console.error('Company creation error:', error);
      Alert.alert('Setup Failed', error.message || 'An error occurred while setting up your company');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={commonStyles.container}>
      {/* Split Layout - Blue section on left, content on right */}
      <View style={styles.mainContent}>
        {/* Left Side - Blue Gradient Section */}
        <View style={styles.leftSide}>
          <LinearGradient
            colors={colors.gradientPrimary}
            style={styles.leftSideGradient}
          >
            <View style={styles.leftSideContent}>
              {/* Logo and App Name */}
              <View style={styles.leftSideHeader}>
                <Text style={styles.leftSideTitle}>CostCompass</Text>
                <Text style={styles.leftSideSubtitle}>Company Setup</Text>
              </View>

              {/* Welcome Message */}
              <View style={styles.leftSideInfo}>
                <Text style={styles.leftSideWelcome}>Welcome, {clerkUser?.firstName}!</Text>
                <Text style={styles.leftSideDescription}>
                  Let's set up your company to get started with professional cost management and benchmarking.
                </Text>
                <Text style={styles.leftSideFeature}>
                  ✓ 30-day free trial{'\n'}
                  ✓ Complete company setup{'\n'}
                  ✓ Admin access granted{'\n'}
                  ✓ Ready to start tracking
                </Text>
              </View>
            </View>
          </LinearGradient>
        </View>

        {/* Right Side - Setup Form */}
        <View style={styles.rightSide}>
          <ScrollView
            contentContainerStyle={styles.rightSideScrollContainer}
            showsVerticalScrollIndicator={true}
          >
            <Card style={styles.formCard}>
              <Card.Content style={styles.cardContent}>
                <Text style={styles.formTitle}>Company Setup</Text>
                <Text style={styles.formSubtitle}>
                  Let's set up your company to get started with CostCompass
                </Text>

                <TextInput
                  label="Company Name *"
                  value={companyName}
                  onChangeText={setCompanyName}
                  mode="outlined"
                  style={styles.input}
                  disabled={loading}
                  placeholder="Enter your company name"
                />

                <TextInput
                  label="Registration Number (Optional)"
                  value={registrationNumber}
                  onChangeText={setRegistrationNumber}
                  mode="outlined"
                  style={styles.input}
                  disabled={loading}
                  placeholder="Company registration number"
                />

                <TextInput
                  label="Company Address (Optional)"
                  value={address}
                  onChangeText={setAddress}
                  mode="outlined"
                  style={styles.input}
                  disabled={loading}
                  multiline
                  numberOfLines={3}
                  placeholder="Enter company address"
                />

                <TextInput
                  label="Capacity *"
                  value={capacity}
                  onChangeText={setCapacity}
                  mode="outlined"
                  style={styles.input}
                  disabled={loading}
                  keyboardType="numeric"
                  placeholder="Enter total capacity (e.g., 50)"
                />

                <TextInput
                  label="Current Occupancy (Optional)"
                  value={currentOccupancy}
                  onChangeText={setCurrentOccupancy}
                  mode="outlined"
                  style={styles.input}
                  disabled={loading}
                  keyboardType="numeric"
                  placeholder="Enter current occupancy (e.g., 42)"
                />

                <Button
                  mode="contained"
                  onPress={handleCreateCompany}
                  style={styles.createAccountButton}
                  disabled={loading}
                >
                  {loading ? (
                    <ActivityIndicator color="white" size="small" />
                  ) : (
                    'Create Company & Get Started'
                  )}
                </Button>

                {/* Info Section */}
                <View style={styles.infoSection}>
                  <Text style={styles.infoTitle}>What happens next?</Text>
                  <Text style={styles.infoText}>
                    • Your company will be created with a 30-day free trial
                  </Text>
                  <Text style={styles.infoText}>
                    • You'll be set as the company administrator
                  </Text>
                  <Text style={styles.infoText}>
                    • You can start creating benchmarks and inviting team members
                  </Text>
                </View>
              </Card.Content>
            </Card>
          </ScrollView>
        </View>
      </View>

      {/* Mobile Header - Only visible on mobile */}
      <View style={styles.mobileHeaderContainer}>
        <LinearGradient
          colors={colors.gradientPrimary}
          style={styles.mobileHeader}
        >
          <View style={styles.mobileHeaderContent}>
            <Text style={styles.mobileHeaderTitle}>CostCompass</Text>
            <Text style={styles.mobileHeaderSubtitle}>Company Setup</Text>
          </View>
        </LinearGradient>
      </View>

      {/* Subscription Modal */}
      <SubscriptionModal
        visible={subscriptionModalVisible}
        onDismiss={() => setSubscriptionModalVisible(false)}
        onSubscriptionComplete={handleSubscriptionComplete}
        companyName={companyName}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  // Main Layout
  mainContent: {
    flex: 1,
    flexDirection: isDesktop ? "row" : "column",
  },

  // Left Side - Blue Section
  leftSide: {
    flex: isDesktop ? 0.4 : 0,
    minHeight: isDesktop ? "100vh" : 0,
    display: isDesktop ? "flex" : "none",
  },
  leftSideGradient: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.xxxxl,
  },
  leftSideContent: {
    maxWidth: 400,
    alignItems: "center",
  },
  leftSideHeader: {
    alignItems: "center",
    marginBottom: spacing.xxxxl,
  },
  leftSideTitle: {
    ...typography.h1,
    color: colors.textOnPrimary,
    fontWeight: "bold",
    marginBottom: spacing.sm,
    textAlign: "center",
  },
  leftSideSubtitle: {
    ...typography.h3,
    color: colors.textOnPrimary,
    opacity: 0.9,
    textAlign: "center",
  },
  leftSideInfo: {
    alignItems: "center",
  },
  leftSideWelcome: {
    ...typography.h2,
    color: colors.textOnPrimary,
    fontWeight: "bold",
    marginBottom: spacing.lg,
    textAlign: "center",
  },
  leftSideDescription: {
    ...typography.body1,
    color: colors.textOnPrimary,
    opacity: 0.9,
    textAlign: "center",
    marginBottom: spacing.xl,
    lineHeight: 24,
  },
  leftSideFeature: {
    ...typography.body1,
    color: colors.textOnPrimary,
    opacity: 0.9,
    textAlign: "left",
    lineHeight: 28,
  },

  // Right Side - Form
  rightSide: {
    flex: isDesktop ? 0.6 : 1,
    backgroundColor: colors.background,
    justifyContent: "center",
    alignItems: "center",
  },
  rightSideScrollContainer: {
    flexGrow: 1,
    justifyContent: "center",
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.xl,
    width: "100%",
    maxWidth: 500,
  },

  // Form Card
  formCard: {
    margin: 0,
    marginBottom: spacing.lg,
    backgroundColor: colors.surface,
    borderRadius: borderRadius.xl,
    elevation: 2,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    width: "100%",
  },
  cardContent: {
    padding: spacing.xl,
    alignItems: "stretch",
    width: "100%",
  },

  // Form Elements
  formTitle: {
    ...typography.h2,
    color: colors.textPrimary,
    fontWeight: "bold",
    marginBottom: spacing.sm,
    textAlign: "center",
  },
  formSubtitle: {
    ...typography.body1,
    color: colors.textSecondary,
    textAlign: "center",
    marginBottom: spacing.xl,
  },
  input: {
    marginBottom: spacing.md,
    backgroundColor: colors.surface,
    width: "100%",
  },
  createAccountButton: {
    marginTop: spacing.lg,
    backgroundColor: colors.primary,
    borderRadius: borderRadius.lg,
    paddingVertical: spacing.sm,
  },

  // Info Section
  infoSection: {
    marginTop: spacing.xl,
    padding: spacing.lg,
    backgroundColor: '#f8f9fa',
    borderRadius: borderRadius.md,
    borderLeftWidth: 4,
    borderLeftColor: '#1976D2',
  },
  infoTitle: {
    ...typography.subtitle1,
    fontWeight: 'bold',
    color: colors.textPrimary,
    marginBottom: spacing.sm,
  },
  infoText: {
    ...typography.body2,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },

  // Mobile Header
  mobileHeaderContainer: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
    display: isDesktop ? "none" : "flex",
  },
  mobileHeader: {
    paddingTop: spacing.xxxxl,
    paddingBottom: spacing.lg,
    paddingHorizontal: spacing.lg,
    position: "relative",
  },
  mobileHeaderContent: {
    alignItems: "center",
    marginTop: spacing.xl,
  },
  mobileHeaderTitle: {
    ...typography.h2,
    color: colors.textOnPrimary,
    fontWeight: "bold",
    textAlign: "center",
  },
  mobileHeaderSubtitle: {
    ...typography.body1,
    color: colors.textOnPrimary,
    opacity: 0.9,
    textAlign: "center",
    marginTop: spacing.xs,
  },
});

export default ClerkOnboardingScreen;
