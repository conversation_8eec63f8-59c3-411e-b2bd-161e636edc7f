import React, { useState } from 'react';
import { View, StyleSheet, Alert, Dimensions, Platform, ScrollView, TouchableOpacity } from 'react-native';
import {
  Text,
  TextInput,
  Button,
  Card,
  ActivityIndicator,
} from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useUser } from '@clerk/clerk-expo';
import { useClerkAuth } from '../contexts/ClerkAuthContext';
import DatabaseService from '../services/database';
import SubscriptionModal from '../components/SubscriptionModal';

import {
  colors,
  spacing,
  borderRadius,
  commonStyles,
  typography,
} from '../styles/theme';

const { width } = Dimensions.get("window");
const isDesktop = Platform.OS === "web" && width > 768;

const ClerkOnboardingScreen = ({ onComplete }) => {
  const { user: clerkUser } = useUser();
  const { user: authUser, refreshAuthState } = useClerkAuth();

  // Use authUser as primary, fallback to clerkUser
  const currentUser = authUser || clerkUser;
  const [companyName, setCompanyName] = useState('');
  const [registrationNumber, setRegistrationNumber] = useState('');
  const [address, setAddress] = useState('');
  const [capacity, setCapacity] = useState('');
  const [currentOccupancy, setCurrentOccupancy] = useState('');
  const [capacityType, setCapacityType] = useState('beds');
  const [loading, setLoading] = useState(false);
  const [subscriptionModalVisible, setSubscriptionModalVisible] = useState(false);
  const [subscriptionData, setSubscriptionData] = useState(null);
  const [step, setStep] = useState(1); // 1: Company details, 2: Subscription

  const handleCreateCompany = async () => {
    console.log('🚀 === HANDLE CREATE COMPANY CLICKED ===');
    console.log('🚀 Company name:', companyName);
    console.log('🚀 Capacity:', capacity);
    console.log('🚀 Current occupancy:', currentOccupancy);
    console.log('🚀 Loading state:', loading);

    if (!companyName.trim()) {
      console.log('❌ Validation failed: Company name is empty');
      Alert.alert('Error', 'Please enter a company name');
      return;
    }

    if (!capacity.trim()) {
      console.log('❌ Validation failed: Capacity is empty');
      Alert.alert('Error', 'Please enter capacity');
      return;
    }

    console.log('✅ Validation passed, proceeding with company creation...');

    // TEMPORARY: Skip subscription modal for testing
    console.log('🧪 TESTING MODE: Skipping subscription, creating account directly...');

    // Create mock subscription info for testing
    const mockSubscriptionInfo = {
      subscriptionId: 'test_subscription_' + Date.now(),
      planId: 'starter',
      planName: 'Starter Plan (Test Mode)',
      amount: 2000, // £20.00 in pence
      currency: 'gbp',
      interval: 'month',
      status: 'active',
      customerId: 'test_customer_' + Date.now(),
    };

    console.log('🧪 Using mock subscription info:', mockSubscriptionInfo);

    try {
      await handleSubscriptionComplete(mockSubscriptionInfo);
      console.log('✅ handleSubscriptionComplete finished successfully');
    } catch (error) {
      console.error('❌ Error in handleSubscriptionComplete:', error);
      console.error('❌ Error details:', JSON.stringify(error, null, 2));
      Alert.alert('Error', `Failed to create account: ${error.message}`);
    }

    // Original code (commented out for testing):
    // setSubscriptionModalVisible(true);
  };

  const handleSubscriptionComplete = async (subscriptionInfo) => {
    console.log('🎯 === SUBSCRIPTION COMPLETED ===');
    console.log('🎯 Subscription info received:', subscriptionInfo);
    console.log('🎯 Current clerkUser:', clerkUser);
    console.log('🎯 Current authUser:', authUser);
    console.log('🎯 Current user (combined):', currentUser);
    console.log('🎯 User available:', !!currentUser);
    console.log('🎯 User ID:', currentUser?.id);
    console.log('🎯 User email:', currentUser?.emailAddresses?.[0]?.emailAddress);

    setSubscriptionData(subscriptionInfo);
    setSubscriptionModalVisible(false);
    setLoading(true);

    try {
      // Wait a moment for user to be available (timing issue)
      let userToUse = currentUser;
      let retries = 0;
      const maxRetries = 5;

      while (!userToUse && retries < maxRetries) {
        console.log(`⏳ Waiting for user... attempt ${retries + 1}/${maxRetries}`);
        console.log(`⏳ ClerkUser: ${!!clerkUser}, AuthUser: ${!!authUser}`);
        await new Promise(resolve => setTimeout(resolve, 500));

        // Try to refresh auth state
        if (refreshAuthState) {
          console.log('🔄 Refreshing auth state...');
          await refreshAuthState();
        }

        userToUse = authUser || clerkUser;
        retries++;
      }

      // Check if user is available after retries
      if (!userToUse) {
        console.error('❌ User is still null after retries - cannot proceed with onboarding');
        console.log('❌ Final state - ClerkUser:', !!clerkUser, 'AuthUser:', !!authUser);
        Alert.alert('Error', 'User authentication failed. Please try signing up again.');
        setLoading(false);
        return;
      }

      console.log('✅ User found after retries:', userToUse?.id);

      const email = userToUse.emailAddresses?.[0]?.emailAddress;
      const name = userToUse.fullName || userToUse.firstName || 'User';

      if (!email) {
        console.error('❌ No email found in user');
        Alert.alert('Error', 'Email address not found. Please try signing up again.');
        setLoading(false);
        return;
      }

      console.log('Creating company and user for authenticated user:', { email, name });
      console.log('Using user ID:', userToUse.id);

      // Check if registration number already exists (if provided)
      if (registrationNumber.trim()) {
        console.log('🔍 Checking if registration number exists:', registrationNumber.trim());
        const registrationExists = await DatabaseService.checkRegistrationNumberExists(
          registrationNumber.trim()
        );
        console.log('🔍 Registration exists:', registrationExists);

        if (registrationExists) {
          console.log('❌ Registration number already exists, stopping process');
          Alert.alert(
            'Registration Number Exists',
            'A company with this registration number already exists. Please contact the administrator to add you to the existing company account.'
          );
          setLoading(false);
          return;
        }
      }

      // Create company
      const companyData = {
        name: companyName.trim(),
        registration: registrationNumber.trim() || null,
        address: address.trim() || null,
        capacity: parseInt(capacity),
        currentOccupancy: currentOccupancy ? parseInt(currentOccupancy) : 0,
        capacityType,
        subscriptionId: subscriptionInfo.subscriptionId,
        subscriptionPlan: subscriptionInfo.planId,
        subscriptionStatus: subscriptionInfo.status,
      };

      console.log('🏢 Creating company with data:', companyData);
      const companyId = await DatabaseService.createCompany(companyData);
      console.log('✅ Company created with ID:', companyId);

      // Create user in database
      const userData = {
        email,
        password: 'clerk_managed', // Placeholder since Clerk manages auth
        name,
        isAdmin: true, // First user is admin
        companyId,
        permissions: ['all'], // Admin permissions
        clerkUserId: userToUse.id, // Store Clerk user ID for reference
      };

      console.log('🔄 Creating database user with data:', userData);
      const userId = await DatabaseService.createUser(userData);
      console.log('✅ Database user created with ID:', userId);

      // Add user to company with admin role
      await DatabaseService.addUserToCompany(userId, companyId, {
        isAdmin: true,
        permissions: ['all'],
        isActive: true,
      });
      console.log('Added user to company with ID:', companyId);

      // Create subscription with payment data
      if (subscriptionData) {
        const subscriptionInfo = {
          company_id: companyId,
          plan_type: subscriptionData.plan,
          status: 'active',
          billing_cycle: subscriptionData.billing,
          subscription_start_date: new Date().toISOString(),
          subscription_end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
        };

        await DatabaseService.createSubscription(subscriptionInfo);
        console.log('Subscription created');
      }

      console.log('=== ONBOARDING COMPLETE - CALLING onComplete ===');

      // Show success alert first, then complete onboarding
      Alert.alert(
        'Welcome to CostCompass!',
        'Your company has been set up successfully. You can now start managing your benchmarks.',
        [
          {
            text: 'Get Started',
            onPress: () => {
              console.log('User clicked Get Started - calling onComplete callback');
              // Force a small delay to ensure UI updates
              setTimeout(() => {
                console.log('Executing onComplete callback...');
                onComplete?.();
              }, 100);
            },
          },
        ],
        { cancelable: false }
      );

    } catch (error) {
      console.error('Company creation error:', error);
      Alert.alert('Setup Failed', error.message || 'An error occurred while setting up your company');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={commonStyles.container}>
      {/* Split Layout - Blue section on left, content on right */}
      <View style={styles.mainContent}>
        {/* Left Side - Blue Gradient Section */}
        <View style={styles.leftSide}>
          <LinearGradient
            colors={colors.gradientPrimary}
            style={styles.leftSideGradient}
          >
            <View style={styles.leftSideContent}>
              {/* Logo and App Name */}
              <View style={styles.leftSideHeader}>
                <Text style={styles.leftSideTitle}>CostCompass</Text>
                <Text style={styles.leftSideSubtitle}>Company Setup</Text>
              </View>

              {/* Welcome Message */}
              <View style={styles.leftSideInfo}>
                <Text style={styles.leftSideWelcome}>Welcome, {clerkUser?.firstName}!</Text>
                <Text style={styles.leftSideDescription}>
                  Let's set up your company to get started with professional cost management and benchmarking.
                </Text>
                <Text style={styles.leftSideFeature}>
                  ✓ 30-day free trial{'\n'}
                  ✓ Complete company setup{'\n'}
                  ✓ Admin access granted{'\n'}
                  ✓ Ready to start tracking
                </Text>
              </View>
            </View>
          </LinearGradient>
        </View>

        {/* Right Side - Setup Form */}
        <View style={styles.rightSide}>
          <ScrollView
            contentContainerStyle={styles.rightSideScrollContainer}
            showsVerticalScrollIndicator={true}
          >
            <Card style={styles.formCard}>
              <Card.Content style={styles.cardContent}>
                <Text style={styles.formTitle}>Company Setup</Text>
                <Text style={styles.formSubtitle}>
                  Let's set up your company to get started with CostCompass
                </Text>

                <TextInput
                  label="Company Name *"
                  value={companyName}
                  onChangeText={setCompanyName}
                  mode="outlined"
                  style={styles.input}
                  disabled={loading}
                  placeholder="Enter your company name"
                />

                <TextInput
                  label="Registration Number (Optional)"
                  value={registrationNumber}
                  onChangeText={setRegistrationNumber}
                  mode="outlined"
                  style={styles.input}
                  disabled={loading}
                  placeholder="Company registration number"
                />

                <TextInput
                  label="Company Address (Optional)"
                  value={address}
                  onChangeText={setAddress}
                  mode="outlined"
                  style={styles.input}
                  disabled={loading}
                  multiline
                  numberOfLines={3}
                  placeholder="Enter company address"
                />

                <TextInput
                  label="Capacity *"
                  value={capacity}
                  onChangeText={setCapacity}
                  mode="outlined"
                  style={styles.input}
                  disabled={loading}
                  keyboardType="numeric"
                  placeholder="Enter total capacity (e.g., 50)"
                />

                <TextInput
                  label="Current Occupancy (Optional)"
                  value={currentOccupancy}
                  onChangeText={setCurrentOccupancy}
                  mode="outlined"
                  style={styles.input}
                  disabled={loading}
                  keyboardType="numeric"
                  placeholder="Enter current occupancy (e.g., 42)"
                />

                <Button
                  mode="contained"
                  onPress={handleCreateCompany}
                  style={styles.createAccountButton}
                  disabled={loading}
                >
                  {loading ? (
                    <ActivityIndicator color="white" size="small" />
                  ) : (
                    'Create Company & Get Started'
                  )}
                </Button>

                {/* Debug button to test form state */}
                <Button
                  mode="outlined"
                  onPress={() => {
                    console.log('🧪 DEBUG: Current form state:', {
                      companyName,
                      registrationNumber,
                      address,
                      capacity,
                      currentOccupancy,
                      capacityType,
                      loading
                    });
                    Alert.alert('Debug Info', `Company: ${companyName}\nCapacity: ${capacity}\nOccupancy: ${currentOccupancy}`);
                  }}
                  style={[styles.createAccountButton, { marginTop: 10 }]}
                  disabled={loading}
                >
                  Debug Form State
                </Button>

                {/* Info Section */}
                <View style={styles.infoSection}>
                  <Text style={styles.infoTitle}>What happens next?</Text>
                  <Text style={styles.infoText}>
                    • Your company will be created with a 30-day free trial
                  </Text>
                  <Text style={styles.infoText}>
                    • You'll be set as the company administrator
                  </Text>
                  <Text style={styles.infoText}>
                    • You can start creating benchmarks and inviting team members
                  </Text>
                </View>
              </Card.Content>
            </Card>
          </ScrollView>
        </View>
      </View>

      {/* Mobile Header - Only visible on mobile */}
      <View style={styles.mobileHeaderContainer}>
        <LinearGradient
          colors={colors.gradientPrimary}
          style={styles.mobileHeader}
        >
          <View style={styles.mobileHeaderContent}>
            <Text style={styles.mobileHeaderTitle}>CostCompass</Text>
            <Text style={styles.mobileHeaderSubtitle}>Company Setup</Text>
          </View>
        </LinearGradient>
      </View>

      {/* Subscription Modal */}
      <SubscriptionModal
        visible={subscriptionModalVisible}
        onDismiss={() => setSubscriptionModalVisible(false)}
        onSubscriptionComplete={handleSubscriptionComplete}
        companyName={companyName}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  // Main Layout
  mainContent: {
    flex: 1,
    flexDirection: isDesktop ? "row" : "column",
  },

  // Left Side - Blue Section
  leftSide: {
    flex: isDesktop ? 0.4 : 0,
    minHeight: isDesktop ? "100vh" : 0,
    display: isDesktop ? "flex" : "none",
  },
  leftSideGradient: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.xxxxl,
  },
  leftSideContent: {
    maxWidth: 400,
    alignItems: "center",
  },
  leftSideHeader: {
    alignItems: "center",
    marginBottom: spacing.xxxxl,
  },
  leftSideTitle: {
    ...typography.h1,
    color: colors.textOnPrimary,
    fontWeight: "bold",
    marginBottom: spacing.sm,
    textAlign: "center",
  },
  leftSideSubtitle: {
    ...typography.h3,
    color: colors.textOnPrimary,
    opacity: 0.9,
    textAlign: "center",
  },
  leftSideInfo: {
    alignItems: "center",
  },
  leftSideWelcome: {
    ...typography.h2,
    color: colors.textOnPrimary,
    fontWeight: "bold",
    marginBottom: spacing.lg,
    textAlign: "center",
  },
  leftSideDescription: {
    ...typography.body1,
    color: colors.textOnPrimary,
    opacity: 0.9,
    textAlign: "center",
    marginBottom: spacing.xl,
    lineHeight: 24,
  },
  leftSideFeature: {
    ...typography.body1,
    color: colors.textOnPrimary,
    opacity: 0.9,
    textAlign: "left",
    lineHeight: 28,
  },

  // Right Side - Form
  rightSide: {
    flex: isDesktop ? 0.6 : 1,
    backgroundColor: colors.background,
    justifyContent: "center",
    alignItems: "center",
  },
  rightSideScrollContainer: {
    flexGrow: 1,
    justifyContent: "center",
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.xl,
    width: "100%",
    maxWidth: 500,
  },

  // Form Card
  formCard: {
    margin: 0,
    marginBottom: spacing.lg,
    backgroundColor: colors.surface,
    borderRadius: borderRadius.xl,
    elevation: 2,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    width: "100%",
  },
  cardContent: {
    padding: spacing.xl,
    alignItems: "stretch",
    width: "100%",
  },

  // Form Elements
  formTitle: {
    ...typography.h2,
    color: colors.textPrimary,
    fontWeight: "bold",
    marginBottom: spacing.sm,
    textAlign: "center",
  },
  formSubtitle: {
    ...typography.body1,
    color: colors.textSecondary,
    textAlign: "center",
    marginBottom: spacing.xl,
  },
  input: {
    marginBottom: spacing.md,
    backgroundColor: colors.surface,
    width: "100%",
  },
  createAccountButton: {
    marginTop: spacing.lg,
    backgroundColor: colors.primary,
    borderRadius: borderRadius.lg,
    paddingVertical: spacing.sm,
  },

  // Info Section
  infoSection: {
    marginTop: spacing.xl,
    padding: spacing.lg,
    backgroundColor: '#f8f9fa',
    borderRadius: borderRadius.md,
    borderLeftWidth: 4,
    borderLeftColor: '#1976D2',
  },
  infoTitle: {
    ...typography.subtitle1,
    fontWeight: 'bold',
    color: colors.textPrimary,
    marginBottom: spacing.sm,
  },
  infoText: {
    ...typography.body2,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },

  // Mobile Header
  mobileHeaderContainer: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
    display: isDesktop ? "none" : "flex",
  },
  mobileHeader: {
    paddingTop: spacing.xxxxl,
    paddingBottom: spacing.lg,
    paddingHorizontal: spacing.lg,
    position: "relative",
  },
  mobileHeaderContent: {
    alignItems: "center",
    marginTop: spacing.xl,
  },
  mobileHeaderTitle: {
    ...typography.h2,
    color: colors.textOnPrimary,
    fontWeight: "bold",
    textAlign: "center",
  },
  mobileHeaderSubtitle: {
    ...typography.body1,
    color: colors.textOnPrimary,
    opacity: 0.9,
    textAlign: "center",
    marginTop: spacing.xs,
  },
});

export default ClerkOnboardingScreen;
