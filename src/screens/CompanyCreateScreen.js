import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import { Text, TextInput, Button, Card, Surface, RadioButton } from 'react-native-paper';
import { useUser } from '@clerk/clerk-expo';
import DatabaseService from '../services/database';
import { colors, spacing, borderRadius, commonStyles } from '../styles/theme';

export default function CompanyCreateScreen({ navigation }) {
  const { user } = useUser();
  const [loading, setLoading] = useState(false);
  const [companyName, setCompanyName] = useState('');
  const [companyRegistration, setCompanyRegistration] = useState('');
  const [capacity, setCapacity] = useState('');
  const [capacityType, setCapacityType] = useState('people');

  const onCreateCompany = async () => {
    if (!companyName.trim()) {
      Alert.alert('Error', 'Please enter a company name');
      return;
    }

    if (!capacity.trim() || isNaN(capacity) || parseInt(capacity) <= 0) {
      Alert.alert('Error', 'Please enter a valid capacity number');
      return;
    }

    setLoading(true);
    try {
      console.log('🔄 Creating company with:', {
        name: companyName,
        registration: companyRegistration,
        capacity: parseInt(capacity),
        capacityType,
        userId: user.id
      });

      // Generate account number
      const accountNumber = await DatabaseService.generateAccountNumber();

      // Create the company
      const companyId = await DatabaseService.createCompany({
        accountNumber,
        name: companyName.trim(),
        registration: companyRegistration.trim() || null,
        capacity: parseInt(capacity),
        currentOccupancy: 1, // Start with 1 (the creator)
        capacityType: capacityType
      });

      console.log('✅ Company created with ID:', companyId);

      // Add the user as admin to the company
      await DatabaseService.addUserToCompany(user.id, companyId, {
        isAdmin: true,
        permissions: [],
        isActive: true
      });

      console.log('✅ User added as admin to company');

      Alert.alert(
        'Success!',
        'Your company has been created successfully.',
        [
          {
            text: 'Continue',
            onPress: () => {
              // Navigate back to home - the app should now detect the user has a company
              navigation.navigate('Home');
            }
          }
        ]
      );

    } catch (error) {
      console.error('❌ Error creating company:', error);
      Alert.alert(
        'Error',
        error.message || 'Failed to create company. Please try again.'
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={commonStyles.container}>
      <ScrollView 
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
      >
        <Surface style={styles.headerCard} elevation={4}>
          <Text variant="headlineMedium" style={styles.title}>
            Create Your Company
          </Text>
          <Text variant="bodyMedium" style={styles.subtitle}>
            Set up your company to start managing benchmarks and budgets
          </Text>
        </Surface>

        <Card style={styles.formCard}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Company Information
            </Text>

            <TextInput
              label="Company Name *"
              value={companyName}
              onChangeText={setCompanyName}
              mode="outlined"
              style={styles.input}
              placeholder="Enter your company name"
            />

            <TextInput
              label="Company Registration (Optional)"
              value={companyRegistration}
              onChangeText={setCompanyRegistration}
              mode="outlined"
              style={styles.input}
              placeholder="e.g., 12345678"
            />

            <TextInput
              label="Capacity *"
              value={capacity}
              onChangeText={setCapacity}
              mode="outlined"
              style={styles.input}
              keyboardType="numeric"
              placeholder="Enter capacity number"
            />

            <Text variant="titleSmall" style={styles.radioTitle}>
              Capacity Type
            </Text>
            <RadioButton.Group 
              onValueChange={setCapacityType} 
              value={capacityType}
            >
              <View style={styles.radioOption}>
                <RadioButton value="people" />
                <Text variant="bodyMedium">People</Text>
              </View>
              <View style={styles.radioOption}>
                <RadioButton value="desks" />
                <Text variant="bodyMedium">Desks</Text>
              </View>
              <View style={styles.radioOption}>
                <RadioButton value="rooms" />
                <Text variant="bodyMedium">Rooms</Text>
              </View>
            </RadioButton.Group>

            <Button
              mode="contained"
              onPress={onCreateCompany}
              loading={loading}
              disabled={loading || !companyName.trim() || !capacity.trim()}
              style={styles.createButton}
              contentStyle={styles.createButtonContent}
            >
              Create Company
            </Button>
          </Card.Content>
        </Card>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  scrollContainer: {
    padding: spacing.md,
    paddingBottom: spacing.xl,
  },
  headerCard: {
    padding: spacing.lg,
    marginBottom: spacing.md,
    borderRadius: borderRadius.lg,
    backgroundColor: colors.surface,
  },
  title: {
    color: colors.primary,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  subtitle: {
    color: colors.onSurfaceVariant,
    textAlign: 'center',
  },
  formCard: {
    borderRadius: borderRadius.lg,
  },
  sectionTitle: {
    color: colors.primary,
    marginBottom: spacing.md,
    fontWeight: 'bold',
  },
  input: {
    marginBottom: spacing.md,
  },
  radioTitle: {
    color: colors.onSurface,
    marginBottom: spacing.sm,
    marginTop: spacing.sm,
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  createButton: {
    marginTop: spacing.lg,
    borderRadius: borderRadius.md,
  },
  createButtonContent: {
    paddingVertical: spacing.sm,
  },
});
