import React, { useState } from "react";
import { View, StyleSheet, ScrollView, Alert, Dimensions, Platform, TouchableOpacity } from "react-native";
import {
  Text,
  TextInput,
  Button,
  Card,
  ActivityIndicator,
  SegmentedButtons,
  Divider,
  Switch,
  Dialog,
  Portal,
  IconButton,
} from "react-native-paper";
import { LinearGradient } from "expo-linear-gradient";
import { Ionicons } from "@expo/vector-icons";
import SubscriptionModal from "../components/SubscriptionModal";
import { useAuth } from "../contexts/AuthContext";

import {
  colors,
  spacing,
  borderRadius,
  commonStyles,
  typography,
} from "../styles/theme";

const RegisterScreen = ({ navigation, route }) => {
  const selectedPlanFromRoute = route?.params?.selectedPlan;
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [name, setName] = useState("");
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Company data (for first user)
  const [companyName, setCompanyName] = useState("");
  const [companyRegistration, setCompanyRegistration] = useState("");
  const [capacity, setCapacity] = useState("");
  const [currentOccupancy, setCurrentOccupancy] = useState("");
  const [capacityType, setCapacityType] = useState("beds");
  const [createNewCompany, setCreateNewCompany] = useState(false);

  // Dialog state
  const [errorDialogVisible, setErrorDialogVisible] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [errorTitle, setErrorTitle] = useState("Error");
  const [successDialogVisible, setSuccessDialogVisible] = useState(false);

  // Subscription modal state
  const [subscriptionModalVisible, setSubscriptionModalVisible] = useState(false);
  const [subscriptionData, setSubscriptionData] = useState(null);



  const { register, isFirstUser } = useAuth();

  // Show company fields if it's the first user OR user chooses to create new company
  const showCompanyFields = isFirstUser || createNewCompany;

  const showError = (title, message) => {
    setErrorTitle(title);
    setErrorMessage(message);
    setErrorDialogVisible(true);
  };

  const validateForm = () => {
    if (!email.trim() || !password.trim() || !name.trim()) {
      showError("Error", "Please fill in all required fields");
      return false;
    }

    if (password !== confirmPassword) {
      showError("Error", "Passwords do not match");
      return false;
    }

    if (password.length < 6) {
      showError("Error", "Password must be at least 6 characters long");
      return false;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      showError("Error", "Please enter a valid email address");
      return false;
    }

    if (showCompanyFields) {
      if (!companyName.trim() || !capacity.trim()) {
        showError("Error", "Please fill in all company details");
        return false;
      }

      if (isNaN(capacity) || parseInt(capacity) <= 0) {
        showError("Error", "Please enter a valid capacity number");
        return false;
      }

      if (
        currentOccupancy &&
        (isNaN(currentOccupancy) || parseInt(currentOccupancy) < 0)
      ) {
        showError("Error", "Please enter a valid occupancy number");
        return false;
      }

      if (currentOccupancy && parseInt(currentOccupancy) > parseInt(capacity)) {
        showError("Error", "Current occupancy cannot exceed capacity");
        return false;
      }
    }

    return true;
  };

  const handleRegister = async () => {
    console.log("Starting registration process...");

    if (!validateForm()) {
      console.log("Form validation failed");
      return;
    }

    console.log("Form validation passed");

    // If creating a new company, require subscription first
    if (showCompanyFields) {
      console.log("Creating new company - subscription required");
      setSubscriptionModalVisible(true);
      return;
    }

    // For existing company users, proceed with normal registration
    await proceedWithRegistration();
  };





  const navigateToLogin = () => {
    navigation.navigate("Login");
  };

  const verifyCompanyRegistration = async (registrationNumber) => {
    if (!registrationNumber || registrationNumber.trim().length < 6) {
      setCompanyVerified(false);
      setCompanyVerificationError("");
      setVerifiedCompanyData(null);
      return;
    }

    setVerifyingCompany(true);
    setCompanyVerificationError("");

    try {
      console.log('🔍 Verifying company registration:', registrationNumber);

      const companyData = await CompaniesHouseService.verifyCompany(registrationNumber);

      if (companyData) {
        console.log('✅ Company found:', companyData.name);

        // Auto-populate company name
        setCompanyName(companyData.name);

        // Set verification state
        setCompanyVerified(true);
        setVerifiedCompanyData(companyData);

        // Auto-suggest capacity type based on business type
        const suggestedCapacityType = CompaniesHouseService.getSuggestedCapacityType(companyData.sicCodes);
        setCapacityType(suggestedCapacityType);

        // Validate if company is suitable for platform
        const validation = CompaniesHouseService.validateCompanyForPlatform(companyData);
        if (!validation.isValid) {
          setCompanyVerificationError(`Warning: ${validation.warnings.join(', ')}`);
        }

      } else {
        console.log('❌ Company not found');
        setCompanyVerified(false);
        setVerifiedCompanyData(null);
        setCompanyVerificationError("Company not found in Companies House register");
      }

    } catch (error) {
      console.error('Error verifying company:', error);
      setCompanyVerified(false);
      setVerifiedCompanyData(null);
      setCompanyVerificationError(error.message || "Failed to verify company registration");
    } finally {
      setVerifyingCompany(false);
    }
  };

  const handleCompanyRegistrationChange = (value) => {
    setCompanyRegistration(value);

    // Reset verification state when user changes the input
    if (value !== companyRegistration) {
      setCompanyVerified(false);
      setCompanyVerificationError("");
      setVerifiedCompanyData(null);
    }

    // Auto-verify when user stops typing (debounced)
    if (verificationTimeout) {
      clearTimeout(verificationTimeout);
    }

    const timeout = setTimeout(() => {
      verifyCompanyRegistration(value);
    }, 1000); // 1 second delay

    setVerificationTimeout(timeout);
  };

  // Add timeout state for debouncing
  const [verificationTimeout, setVerificationTimeout] = useState(null);

  const handleSubscriptionComplete = async (subscriptionInfo) => {
    console.log("=== SUBSCRIPTION COMPLETED ===");
    console.log("Subscription info received:", subscriptionInfo);

    try {
      console.log("Setting subscription data...");
      setSubscriptionData(subscriptionInfo);

      console.log("Closing subscription modal...");
      setSubscriptionModalVisible(false);

      console.log("Proceeding with registration with subscription...");
      await proceedWithRegistration(subscriptionInfo);
    } catch (error) {
      console.error("Error in handleSubscriptionComplete:", error);
      showError("Registration Failed", "Failed to complete registration: " + error.message);
    }
  };

  const proceedWithRegistration = async (subscriptionInfo = null) => {
    console.log("=== PROCEEDING WITH REGISTRATION ===");
    console.log("Subscription info:", subscriptionInfo);

    setLoading(true);

    const userData = {
      email: email.trim(),
      password,
      name: name.trim(),
    };

    if (showCompanyFields) {
      // Subscription is required for company creation
      if (!subscriptionInfo) {
        console.error("No subscription info provided for company creation");
        setLoading(false);
        showError("Subscription Required", "A subscription is required to create a new company account.");
        return;
      }

      userData.companyData = {
        name: companyName.trim(),
        registration: companyRegistration.trim(),
        capacity: parseInt(capacity),
        currentOccupancy: currentOccupancy ? parseInt(currentOccupancy) : 0,
        capacityType,
      };

      userData.subscriptionData = subscriptionInfo;
      console.log("Company data with subscription added:", { ...userData, password: "[HIDDEN]" });
    }

    try {
      console.log("Calling register function...");
      const result = await register(userData);
      console.log("Register result:", result);

      setLoading(false);

      if (!result.success) {
        console.error("Registration failed:", result.error);
        showError("Registration Failed", result.error);
      } else {
        console.log("Registration successful!");
        setSuccessDialogVisible(true);
      }
    } catch (error) {
      console.error("Registration error:", error);
      setLoading(false);
      showError("Registration Failed", "An unexpected error occurred: " + error.message);
    }
  };

  const handleGoBack = () => {
    navigation.goBack();
  };

  return (
    <View style={commonStyles.container}>
      {/* Split Layout - Blue section on left, content on right */}
      <View style={styles.mainContent}>
        {/* Left Side - Blue Gradient Section */}
        <View style={styles.leftSide}>
          <LinearGradient
            colors={colors.gradientPrimary}
            style={styles.leftSideGradient}
          >
            <View style={styles.leftSideContent}>
              {/* Logo and App Name */}
              <View style={styles.leftSideHeader}>
                <Text style={styles.leftSideTitle}>CostCompass</Text>
                <Text style={styles.leftSideSubtitle}>Create Account</Text>
              </View>

              {/* Welcome Message */}
              <View style={styles.leftSideInfo}>
                <Text style={styles.leftSideWelcome}>Welcome!</Text>
                <Text style={styles.leftSideDescription}>
                  Join thousands of care homes managing their costs effectively with CostCompass.
                </Text>
                <Text style={styles.leftSideFeature}>
                  ✓ 30-day free trial{'\n'}
                  ✓ Real-time cost tracking{'\n'}
                  ✓ Advanced reporting{'\n'}
                  ✓ Benchmark comparisons
                </Text>
              </View>
            </View>
          </LinearGradient>
        </View>

        {/* Right Side - Registration Form */}
        <View style={styles.rightSide}>
          <ScrollView
            contentContainerStyle={styles.rightSideScrollContainer}
            showsVerticalScrollIndicator={true}
          >
        <Card style={styles.formCard}>
          <Card.Content style={styles.cardContent}>
            {/* Back Button */}
            <View style={styles.backButtonContainer}>
              <TouchableOpacity
                style={styles.backButton}
                onPress={handleGoBack}
                disabled={loading}
              >
                <Ionicons
                  name="arrow-back"
                  size={24}
                  color={colors.primary}
                />
                <Text style={styles.backButtonText}>Back</Text>
              </TouchableOpacity>
            </View>

            <Text variant="headlineSmall" style={commonStyles.sectionTitle}>
              Personal Information
            </Text>

            <TextInput
              label="Full Name *"
              value={name}
              onChangeText={setName}
              mode="outlined"
              style={styles.input}
              disabled={loading}
            />

            <TextInput
              label="Email *"
              value={email}
              onChangeText={setEmail}
              mode="outlined"
              keyboardType="email-address"
              autoCapitalize="none"
              style={styles.input}
              disabled={loading}
            />

            <TextInput
              label="Password *"
              value={password}
              onChangeText={setPassword}
              mode="outlined"
              secureTextEntry={!showPassword}
              right={
                <TextInput.Icon
                  icon={showPassword ? "eye-off" : "eye"}
                  onPress={() => setShowPassword(!showPassword)}
                />
              }
              style={styles.input}
              disabled={loading}
            />

            <TextInput
              label="Confirm Password *"
              value={confirmPassword}
              onChangeText={setConfirmPassword}
              mode="outlined"
              secureTextEntry={!showConfirmPassword}
              right={
                <TextInput.Icon
                  icon={showConfirmPassword ? "eye-off" : "eye"}
                  onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                />
              }
              style={styles.input}
              disabled={loading}
            />

            {!isFirstUser && (
              <>
                <Divider style={styles.divider} />

                <View style={styles.companyToggle}>
                  <View style={styles.toggleContent}>
                    <View style={styles.toggleTextContainer}>
                      <Text variant="bodyLarge" style={styles.toggleLabel}>
                        Create New Company
                      </Text>
                      <Text variant="bodySmall" style={styles.toggleDescription}>
                        Check this if you want to create a new care home/company
                      </Text>
                    </View>
                    <Switch
                      value={createNewCompany}
                      onValueChange={setCreateNewCompany}
                      disabled={loading}
                    />
                  </View>
                </View>
              </>
            )}

            {showCompanyFields && (
              <>
                <Divider style={styles.divider} />

                <Text variant="headlineSmall" style={commonStyles.sectionTitle}>
                  Company Information
                </Text>

                <TextInput
                  label="Company Name *"
                  value={companyName}
                  onChangeText={setCompanyName}
                  mode="outlined"
                  style={styles.input}
                  disabled={loading}
                />

                <TextInput
                  label="Company Registration"
                  value={companyRegistration}
                  onChangeText={setCompanyRegistration}
                  mode="outlined"
                  style={styles.input}
                  disabled={loading}
                />
                <Text variant="bodySmall" style={styles.helperText}>
                  If your company is already registered in the system, you'll be
                  asked to contact the administrator to be added to the existing
                  account.
                </Text>

                <Text variant="bodySmall" style={styles.subscriptionNotice}>
                  ⚠️ A paid subscription is required to create a new company account. You will need to enter your payment details during registration.
                </Text>

                <Text variant="bodyMedium" style={styles.label}>
                  Capacity Type *
                </Text>
                <SegmentedButtons
                  value={capacityType}
                  onValueChange={setCapacityType}
                  buttons={[
                    { value: "beds", label: "Beds" },
                    { value: "rooms", label: "Rooms" },
                    { value: "persons", label: "Persons" },
                  ]}
                  style={styles.segmentedButtons}
                />

                <TextInput
                  label={`Total ${capacityType.charAt(0).toUpperCase() + capacityType.slice(1)} *`}
                  value={capacity}
                  onChangeText={setCapacity}
                  mode="outlined"
                  keyboardType="numeric"
                  style={styles.input}
                  disabled={loading}
                />

                <TextInput
                  label={`Current Occupancy`}
                  value={currentOccupancy}
                  onChangeText={setCurrentOccupancy}
                  mode="outlined"
                  keyboardType="numeric"
                  style={styles.input}
                  disabled={loading}
                />
              </>
            )}

            <Button
              mode="contained"
              onPress={handleRegister}
              style={styles.createAccountButton}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator color="white" />
              ) : showCompanyFields ? (
                "Subscribe & Create Account"
              ) : (
                "Create Account"
              )}
            </Button>

            <View style={styles.loginSection}>
              <Text variant="bodyMedium" style={styles.loginText}>
                Already have an account?
              </Text>
              <Button mode="text" onPress={navigateToLogin} disabled={loading}>
                Sign In
              </Button>
            </View>
          </Card.Content>
        </Card>

            {/* Error Dialog */}
            <Portal>
              <Dialog
                visible={errorDialogVisible}
                onDismiss={() => setErrorDialogVisible(false)}
                style={commonStyles.compactDialog}
              >
                <Dialog.Title style={commonStyles.compactDialogTitle}>{errorTitle}</Dialog.Title>
                <Dialog.Content style={commonStyles.compactDialogContent}>
                  <Text variant="bodyMedium" style={commonStyles.compactDialogText}>{errorMessage}</Text>
                </Dialog.Content>
                <Dialog.Actions style={commonStyles.compactDialogActions}>
                  <Button
                    onPress={() => setErrorDialogVisible(false)}
                    mode="contained"
                    style={commonStyles.compactDialogButton}
                    compact
                  >
                    OK
                  </Button>
                </Dialog.Actions>
              </Dialog>
            </Portal>

            {/* Success Dialog */}
            <Portal>
              <Dialog
                visible={successDialogVisible}
                onDismiss={() => setSuccessDialogVisible(false)}
                style={commonStyles.compactDialog}
              >
                <Dialog.Title style={commonStyles.compactDialogTitle}>Registration Successful!</Dialog.Title>
                <Dialog.Content style={commonStyles.compactDialogContent}>
                  <Text variant="bodyMedium" style={commonStyles.compactDialogText}>
                    {showCompanyFields
                      ? "Your account has been created successfully with your subscription. Welcome to CostCompass!"
                      : "Your account has been created successfully. You now have a 30-day free trial. Please choose a subscription plan to continue."
                    }
                  </Text>
                </Dialog.Content>
                <Dialog.Actions style={commonStyles.compactDialogActions}>
                  <Button
                    onPress={() => {
                      setSuccessDialogVisible(false);
                      navigation.navigate(showCompanyFields && subscriptionData ? "HomeMain" : "Subscription",
                        selectedPlanFromRoute ? { selectedPlan: selectedPlanFromRoute } : undefined);
                    }}
                    mode="contained"
                    style={commonStyles.compactDialogButton}
                    compact
                  >
                    {showCompanyFields && subscriptionData ? "Continue to Dashboard" : "Choose Plan"}
                  </Button>
                </Dialog.Actions>
              </Dialog>
            </Portal>

            {/* Subscription Modal */}
            <SubscriptionModal
              visible={subscriptionModalVisible}
              onDismiss={() => setSubscriptionModalVisible(false)}
              onSubscriptionComplete={handleSubscriptionComplete}
              companyName={companyName}
              initialSelectedPlan={selectedPlanFromRoute}
            />


          </ScrollView>
        </View>
      </View>

      {/* Mobile Header - Only visible on mobile */}
      <View style={styles.mobileHeaderContainer}>
        <LinearGradient
          colors={colors.gradientPrimary}
          style={styles.mobileHeader}
        >
          {/* Mobile Back Button */}
          <TouchableOpacity
            style={styles.mobileBackButton}
            onPress={handleGoBack}
            disabled={loading}
          >
            <Ionicons
              name="arrow-back"
              size={24}
              color={colors.textOnPrimary}
            />
          </TouchableOpacity>

          <View style={styles.mobileHeaderContent}>
            <Text style={styles.mobileHeaderTitle}>CostCompass</Text>
            <Text style={styles.mobileHeaderSubtitle}>Create Account</Text>
          </View>
        </LinearGradient>
      </View>
    </View>
  );
};

const { width } = Dimensions.get("window");
const isDesktop = Platform.OS === "web" && width > 768;

const styles = StyleSheet.create({
  // Main Layout
  mainContent: {
    flex: 1,
    flexDirection: isDesktop ? "row" : "column",
  },

  // Left Side - Blue Section
  leftSide: {
    width: isDesktop ? "40%" : "100%",
    minHeight: isDesktop ? "100%" : 200,
    display: isDesktop ? "flex" : "none", // Hide on mobile
  },
  leftSideGradient: {
    flex: 1,
    padding: spacing.lg,
    paddingTop: spacing.md,
    justifyContent: "flex-start",
  },
  leftSideContent: {
    flex: 1,
    justifyContent: "space-between",
    paddingVertical: spacing.xl,
  },
  leftSideHeader: {
    marginBottom: spacing.xl,
  },
  leftSideTitle: {
    fontSize: 48,
    fontWeight: "800",
    color: colors.textOnPrimary,
    marginBottom: spacing.sm,
    textAlign: "left",
  },
  leftSideSubtitle: {
    fontSize: 24,
    fontWeight: "600",
    color: colors.textOnPrimary,
    opacity: 0.9,
  },
  leftSideInfo: {
    flex: 1,
    justifyContent: "center",
  },
  leftSideWelcome: {
    fontSize: 32,
    fontWeight: "700",
    color: colors.textOnPrimary,
    marginBottom: spacing.md,
  },
  leftSideDescription: {
    fontSize: 18,
    color: colors.textOnPrimary,
    opacity: 0.9,
    lineHeight: 26,
    marginBottom: spacing.xl,
  },
  leftSideFeature: {
    fontSize: 16,
    color: colors.textOnPrimary,
    opacity: 0.9,
    lineHeight: 24,
  },

  // Right Side - Form Content
  rightSide: {
    flex: 1,
    width: isDesktop ? "60%" : "100%",
    backgroundColor: colors.background,
    marginTop: isDesktop ? 0 : 180, // Add top margin on mobile for header
  },
  rightSideScrollContainer: {
    flexGrow: 1,
    paddingHorizontal: spacing.xl,
    paddingTop: spacing.lg,
    paddingBottom: spacing.xxxl,
  },

  // Mobile Header
  mobileHeaderContainer: {
    display: isDesktop ? "none" : "flex", // Only show on mobile
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1,
  },
  mobileHeader: {
    paddingTop: spacing.xxxxl,
    paddingBottom: spacing.xl,
    paddingHorizontal: spacing.xl,
    borderBottomLeftRadius: borderRadius.xxl,
    borderBottomRightRadius: borderRadius.xxl,
  },
  mobileHeaderContent: {
    alignItems: "center",
  },
  mobileHeaderTitle: {
    fontSize: 32,
    fontWeight: "800",
    color: colors.textOnPrimary,
    marginBottom: spacing.xs,
  },
  mobileHeaderSubtitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.textOnPrimary,
    opacity: 0.9,
  },
  mobileBackButton: {
    position: "absolute",
    top: spacing.xxxxl + spacing.sm,
    left: spacing.lg,
    zIndex: 2,
    padding: spacing.sm,
    borderRadius: borderRadius.md,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
  },

  // Form Styles
  formCard: {
    margin: 0,
    marginBottom: spacing.lg,
    backgroundColor: colors.surface,
    borderRadius: borderRadius.xl,
    elevation: 2,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    width: "100%",
  },
  cardContent: {
    padding: spacing.xl,
    alignItems: "stretch",
    width: "100%",
  },
  input: {
    marginBottom: spacing.md,
    backgroundColor: colors.surface,
    width: "100%",
  },
  createAccountButton: {
    marginTop: spacing.lg,
    marginBottom: spacing.md,
    paddingVertical: spacing.md,
    borderRadius: borderRadius.lg,
    backgroundColor: colors.primary,
    width: "100%",
    alignSelf: "stretch",
  },
  label: {
    marginBottom: spacing.sm,
    color: colors.textSecondary,
    fontWeight: typography.fontWeights.medium,
  },
  segmentedButtons: {
    marginBottom: spacing.md,
    width: "100%",
  },
  divider: {
    marginVertical: spacing.lg,
    width: "100%",
  },
  loginSection: {
    alignItems: "center",
    marginTop: spacing.md,
    width: "100%",
  },
  loginText: {
    color: colors.textSecondary,
    marginBottom: spacing.xs,
    textAlign: "center",
  },
  companyToggle: {
    backgroundColor: colors.surfaceVariant,
    padding: spacing.md,
    borderRadius: borderRadius.sm,
    marginBottom: spacing.md,
    width: "100%",
  },
  toggleContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    width: "100%",
  },
  toggleTextContainer: {
    flex: 1,
    marginRight: spacing.md,
  },
  toggleLabel: {
    fontWeight: typography.fontWeights.bold,
    marginBottom: spacing.xs,
    color: colors.text,
  },
  toggleDescription: {
    color: colors.textSecondary,
    lineHeight: 18,
  },
  helperText: {
    color: colors.textSecondary,
    fontSize: typography.fontSizes.xs,
    marginTop: -spacing.sm,
    marginBottom: spacing.md,
    fontStyle: "italic",
    width: "100%",
  },

  subscriptionNotice: {
    color: colors.warning,
    fontSize: typography.fontSizes.xs,
    marginTop: spacing.sm,
    marginBottom: spacing.md,
    fontWeight: typography.fontWeights.medium,
    backgroundColor: colors.warningLight + '20',
    padding: spacing.sm,
    borderRadius: borderRadius.sm,
    borderLeftWidth: 3,
    borderLeftColor: colors.warning,
    width: "100%",
  },

  // Back Button Styles
  backButtonContainer: {
    width: "100%",
    marginBottom: spacing.lg,
  },
  backButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderRadius: borderRadius.md,
    backgroundColor: colors.surfaceVariant,
    alignSelf: "flex-start",
  },
  backButtonText: {
    marginLeft: spacing.sm,
    color: colors.primary,
    fontSize: typography.fontSizes.md,
    fontWeight: typography.fontWeights.medium,
  },


});

export default RegisterScreen;
