import React, { useState } from "react";
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
  Dimensions,
  Platform,
  Image,
} from "react-native";
import {
  Text,
  TextInput,
  Button,
  Card,
  ActivityIndicator,
  Surface,
  Chip,
} from "react-native-paper";
import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from 'expo-linear-gradient';
import { colors, spacing, borderRadius, commonStyles, typography, elevation } from '../styles/theme';
import { useAuth } from "../contexts/AuthContext";

const { width } = Dimensions.get("window");

// Simple gradient fallback
const GradientView = ({ colors, style, children, ...props }) => (
  <View style={[style, { backgroundColor: colors[0] }]} {...props}>
    {children}
  </View>
);

const LoginScreen = ({ navigation }) => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const { login, isFirstUser } = useAuth();

  const handleLogin = async () => {
    if (!email.trim() || !password.trim()) {
      Alert.alert("Error", "Please fill in all fields");
      return;
    }

    setLoading(true);
    const result = await login(email.trim(), password);
    setLoading(false);

    if (!result.success) {
      Alert.alert("Login Failed", result.error);
    }
  };

  const navigateToRegister = () => {
    navigation.navigate("ClerkSignUp");
  };

  const navigateToLanding = () => {
    navigation.navigate("Landing");
  };

  const fillDemoCredentials = () => {
    setEmail("<EMAIL>");
    setPassword("admin123");
  };

  return (
    <View style={styles.container}>
      {/* Soldo-style Split Layout */}
      <View style={styles.mainContent}>
        {/* Left Side - Branding (hidden on mobile) */}
        {Platform.OS === 'web' && width > 768 && (
          <View style={styles.leftSide}>
            <View style={styles.brandingContainer}>
              <View style={styles.logoSection}>
                <Image
                  source={require("../../assets/logo.png")}
                  style={styles.logo}
                  resizeMode="contain"
                />
                <Text style={styles.brandTitle}>CostCompass</Text>
              </View>
              <Text style={styles.brandSubtitle}>
                Smart cost management for modern businesses
              </Text>
              <Text style={styles.brandDescription}>
                Take control of your operational expenses with real-time tracking,
                intelligent benchmarking, and automated budget management designed
                for healthcare and hospitality industries.
              </Text>

              {/* Feature highlights */}
              <View style={styles.features}>
                <View style={styles.feature}>
                  <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
                  <Text style={styles.featureText}>Real-time cost tracking</Text>
                </View>
                <View style={styles.feature}>
                  <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
                  <Text style={styles.featureText}>Intelligent benchmarking</Text>
                </View>
                <View style={styles.feature}>
                  <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
                  <Text style={styles.featureText}>Automated budget alerts</Text>
                </View>
              </View>
            </View>
          </View>
        )}

        {/* Right Side - Login Form */}
        <View style={styles.rightSide}>
          <ScrollView
            contentContainerStyle={styles.formScrollContainer}
            showsVerticalScrollIndicator={false}
          >
            {/* Mobile Logo (only shown on mobile) */}
            {(Platform.OS !== 'web' || width <= 768) && (
              <View style={styles.mobileHeader}>
                <Image
                  source={require("../../assets/logo.png")}
                  style={styles.mobileLogo}
                  resizeMode="contain"
                />
                <Text style={styles.mobileTitle}>CostCompass</Text>
              </View>
            )}

            {/* Login Form */}
            <View style={styles.loginForm}>
              <View style={styles.formHeader}>
                <Text style={styles.formTitle}>Welcome back</Text>
                <Text style={styles.formSubtitle}>
                  Sign in to your account to continue
                </Text>
              </View>

              <View style={styles.formFields}>
                <TextInput
                  label="Email address"
                  value={email}
                  onChangeText={setEmail}
                  mode="outlined"
                  keyboardType="email-address"
                  autoCapitalize="none"
                  style={styles.input}
                  disabled={loading}
                  outlineStyle={styles.inputOutline}
                  contentStyle={styles.inputContent}
                />

                <TextInput
                  label="Password"
                  value={password}
                  onChangeText={setPassword}
                  mode="outlined"
                  secureTextEntry={!showPassword}
                  right={
                    <TextInput.Icon
                      icon={showPassword ? "eye-off" : "eye"}
                      onPress={() => setShowPassword(!showPassword)}
                    />
                  }
                  style={styles.input}
                  disabled={loading}
                  outlineStyle={styles.inputOutline}
                  contentStyle={styles.inputContent}
                />

                <Button
                  mode="contained"
                  onPress={handleLogin}
                  style={styles.loginButton}
                  disabled={loading}
                  buttonColor="#1976D2"
                  contentStyle={styles.buttonContent}
                >
                  {loading ? (
                    <ActivityIndicator color="white" size="small" />
                  ) : (
                    "Sign in"
                  )}
                </Button>
              </View>

              {/* Demo Account Info */}
              <Surface style={styles.demoInfo} elevation={1}>
                <View style={styles.demoHeader}>
                  <Ionicons
                    name="information-circle"
                    size={16}
                    color="#1976D2"
                  />
                  <Text style={styles.demoTitle}>
                    Try Demo Account
                  </Text>
                </View>
                <Text style={styles.demoText}>
                  Email: <EMAIL> | Password: admin123
                </Text>
                <Button
                  mode="text"
                  compact
                  onPress={fillDemoCredentials}
                  style={styles.fillDemoButton}
                  textColor="#1976D2"
                >
                  Fill Demo Credentials
                </Button>
              </Surface>

              {/* Register Link */}
              <View style={styles.registerSection}>
                <Text style={styles.registerText}>
                  Don't have an account?{' '}
                  <Text
                    style={styles.registerLink}
                    onPress={navigateToRegister}
                  >
                    Sign up
                  </Text>
                </Text>
              </View>

              {/* Back to Home */}
              <Button
                mode="text"
                onPress={navigateToLanding}
                disabled={loading}
                textColor="#666"
                style={styles.backButton}
              >
                ← Back to Home
              </Button>
            </View>
          </ScrollView>
        </View>
      </View>

      {/* First User Welcome - Mobile Only */}
      {isFirstUser && (Platform.OS !== 'web' || width <= 768) && (
        <View style={styles.welcomeCard}>
          <View style={styles.welcomeHeader}>
            <Ionicons name="rocket" size={24} color="#4CAF50" />
            <Text style={styles.welcomeTitle}>
              Welcome to CostCompass!
            </Text>
          </View>
          <Text style={styles.welcomeText}>
            You're setting up the first account. Create your admin account
            to get started with cost management.
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  mainContent: {
    flex: 1,
    flexDirection: Platform.OS === 'web' && width > 768 ? 'row' : 'column',
  },
  // Left Side - Branding (Desktop only) - Smaller width
  leftSide: {
    flex: 0.6, // Reduced from 1 to 0.6 for smaller width
    backgroundColor: '#1976D2',
    padding: 40, // Reduced padding
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 350, // Minimum width to ensure content fits
    maxWidth: 450, // Maximum width to prevent it from getting too wide
  },
  brandingContainer: {
    maxWidth: 320, // Reduced from 400 to 320
    alignItems: 'center',
  },
  logoSection: {
    alignItems: 'center',
    marginBottom: 30, // Reduced from 40
  },
  logo: {
    width: 80, // Larger logo for better integration
    height: 80, // Larger logo for better integration
    marginBottom: 20, // Increased spacing
    // Remove tintColor to show original logo colors
  },
  brandTitle: {
    fontSize: 28, // Reduced from 32
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
  },
  brandSubtitle: {
    fontSize: 18, // Reduced from 20
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    marginBottom: 20, // Reduced from 24
    fontWeight: '500',
  },
  brandDescription: {
    fontSize: 14, // Reduced from 16
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    lineHeight: 20, // Reduced from 24
    marginBottom: 30, // Reduced from 40
  },
  features: {
    alignSelf: 'stretch',
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12, // Reduced from 16
  },
  featureText: {
    fontSize: 14, // Reduced from 16
    color: 'rgba(255, 255, 255, 0.9)',
    marginLeft: 10, // Reduced from 12
    fontWeight: '500',
  },
  // Right Side - Login Form - Larger width
  rightSide: {
    flex: 1.4, // Increased from 1 to 1.4 for more space
    justifyContent: 'center',
    alignItems: 'center',
    padding: Platform.OS === 'web' ? 60 : 24,
    backgroundColor: '#f8f9fa',
  },
  formScrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    width: '100%',
    maxWidth: 400,
  },
  mobileHeader: {
    alignItems: 'center',
    marginBottom: 40,
  },
  mobileLogo: {
    width: 80,
    height: 80,
    marginBottom: 20,
  },
  mobileTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1976D2',
    textAlign: 'center',
  },
  loginForm: {
    width: '100%',
  },
  formHeader: {
    marginBottom: 32,
  },
  formTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 8,
    textAlign: Platform.OS === 'web' ? 'left' : 'center',
  },
  formSubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: Platform.OS === 'web' ? 'left' : 'center',
  },
  formFields: {
    marginBottom: 24,
  },
  input: {
    backgroundColor: 'white',
    marginBottom: 16,
    borderRadius: 8,
  },
  inputContent: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  inputOutline: {
    borderColor: '#e1e5e9',
    borderWidth: 1,
    borderRadius: 8,
  },
  loginButton: {
    backgroundColor: '#1976D2',
    borderRadius: 8,
    marginTop: 8,
    elevation: 0,
    shadowOpacity: 0,
  },
  buttonContent: {
    paddingVertical: 14,
    justifyContent: 'center',
  },
  demoInfo: {
    backgroundColor: '#f8f9fa',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e1e5e9',
    marginTop: 24,
  },
  demoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  demoTitle: {
    color: '#1976D2',
    fontWeight: '600',
    fontSize: 14,
    marginLeft: 8,
  },
  demoText: {
    color: '#666',
    fontSize: 14,
    marginBottom: 4,
  },
  fillDemoButton: {
    marginTop: 8,
  },
  registerSection: {
    marginTop: 32,
    alignItems: 'center',
  },
  registerText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  registerLink: {
    color: '#1976D2',
    fontWeight: '600',
  },
  backButton: {
    marginTop: 16,
  },
  welcomeCard: {
    backgroundColor: '#e8f5e8',
    padding: 20,
    margin: 24,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#c8e6c9',
  },
  welcomeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  welcomeTitle: {
    color: '#2e7d32',
    fontWeight: '600',
    fontSize: 18,
    marginLeft: 8,
  },
  welcomeText: {
    color: '#2e7d32',
    fontSize: 14,
    lineHeight: 20,
  },
});

export default LoginScreen;
