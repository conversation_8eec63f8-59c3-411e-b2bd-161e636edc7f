import React, { useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import {
  Text,
  Card,
  Button,
  ActivityIndicator,
  Chip,
  Divider,
  IconButton
} from 'react-native-paper';
import { useAuth } from '../contexts/AuthContext';
import DatabaseService from '../services/database';
import AsyncStorage from '@react-native-async-storage/async-storage';

const AccountSelectionScreen = () => {
  const { userAccounts, logout, selectAccount } = useAuth();
  const [loading, setLoading] = useState(false);

  const handleAccountSelect = async (account) => {
    try {
      setLoading(true);
      const result = await selectAccount(account);

      if (!result.success) {
        console.error('Error selecting account:', result.error);
        // Show error message to user
      }
    } catch (error) {
      console.error('Error selecting account:', error);
      // Show error message to user
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    await logout();
  };

  const getAccountDisplayName = (account) => {
    if (account.account_name) {
      return account.account_name;
    }
    
    // Generate display name based on company or role
    if (account.company_id) {
      return `${account.name} (${account.is_admin ? 'Admin' : 'User'})`;
    }
    
    return account.name;
  };

  const getAccountDescription = (account) => {
    const parts = [];
    
    if (account.is_admin) {
      parts.push('Administrator');
    } else {
      parts.push('User');
    }
    
    if (account.last_login) {
      const lastLogin = new Date(account.last_login).toLocaleDateString();
      parts.push(`Last login: ${lastLogin}`);
    }
    
    return parts.join(' • ');
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
        <Text variant="bodyLarge" style={styles.loadingText}>
          Switching account...
        </Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <View style={styles.header}>
        <Text variant="headlineLarge" style={styles.title}>
          CostCompass
        </Text>
        <Text variant="bodyLarge" style={styles.subtitle}>
          Select Account
        </Text>
        <Text variant="bodyMedium" style={styles.description}>
          You have multiple accounts with this email address
        </Text>
      </View>

      <Card style={styles.card}>
        <Card.Content>
          <View style={styles.cardHeader}>
            <Text variant="titleLarge" style={styles.cardTitle}>
              Your Accounts
            </Text>
            <IconButton
              icon="logout"
              size={24}
              onPress={handleLogout}
              iconColor="#f44336"
            />
          </View>
          
          <Text variant="bodyMedium" style={styles.cardDescription}>
            Select the account you want to use:
          </Text>

          <Divider style={styles.divider} />

          {userAccounts && userAccounts.length > 0 ? (
            userAccounts.map((account) => (
              <Card key={account.id} style={styles.accountCard}>
                <Card.Content>
                  <View style={styles.accountHeader}>
                    <View style={styles.accountInfo}>
                      <Text variant="titleMedium" style={styles.accountName}>
                        {getAccountDisplayName(account)}
                      </Text>
                      <Text variant="bodySmall" style={styles.accountDescription}>
                        {getAccountDescription(account)}
                      </Text>
                      <Text variant="bodySmall" style={styles.accountEmail}>
                        {account.email}
                      </Text>
                    </View>
                    <View style={styles.accountBadges}>
                      {account.is_admin && (
                        <Chip icon="shield-account" style={styles.adminChip}>
                          Admin
                        </Chip>
                      )}
                      {account.is_active ? (
                        <Chip icon="check-circle" style={styles.activeChip}>
                          Active
                        </Chip>
                      ) : (
                        <Chip icon="pause-circle" style={styles.inactiveChip}>
                          Inactive
                        </Chip>
                      )}
                    </View>
                  </View>
                  
                  <Button
                    mode="contained"
                    onPress={() => handleAccountSelect(account)}
                    style={styles.selectButton}
                    icon="arrow-right"
                    contentStyle={styles.selectButtonContent}
                    disabled={!account.is_active || loading}
                  >
                    Select Account
                  </Button>
                </Card.Content>
              </Card>
            ))
          ) : (
            <View style={styles.noAccountsContainer}>
              <Text variant="bodyLarge" style={styles.noAccountsText}>
                No accounts found
              </Text>
              <Text variant="bodyMedium" style={styles.noAccountsSubtext}>
                Please contact your administrator.
              </Text>
              <Button
                mode="outlined"
                onPress={handleLogout}
                style={styles.logoutButton}
                icon="logout"
              >
                Logout
              </Button>
            </View>
          )}
        </Card.Content>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  contentContainer: {
    padding: 20,
    minHeight: '100vh',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 16,
    color: '#666',
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
  },
  title: {
    color: '#2196F3',
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    color: '#666',
    marginBottom: 8,
  },
  description: {
    color: '#333',
    textAlign: 'center',
  },
  card: {
    marginBottom: 20,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  cardTitle: {
    color: '#333',
    fontWeight: 'bold',
  },
  cardDescription: {
    color: '#666',
    marginBottom: 16,
  },
  divider: {
    marginBottom: 20,
  },
  noAccountsContainer: {
    alignItems: 'center',
    padding: 20,
  },
  noAccountsText: {
    color: '#333',
    marginBottom: 8,
  },
  noAccountsSubtext: {
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
  },
  logoutButton: {
    marginTop: 10,
  },
  accountCard: {
    marginBottom: 16,
    backgroundColor: '#fff',
  },
  accountHeader: {
    marginBottom: 16,
  },
  accountInfo: {
    marginBottom: 12,
  },
  accountName: {
    color: '#333',
    fontWeight: 'bold',
    marginBottom: 4,
  },
  accountDescription: {
    color: '#666',
    marginBottom: 2,
  },
  accountEmail: {
    color: '#666',
    fontSize: 12,
  },
  accountBadges: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  adminChip: {
    backgroundColor: '#e8f5e8',
  },
  activeChip: {
    backgroundColor: '#e8f5e8',
  },
  inactiveChip: {
    backgroundColor: '#ffebee',
  },
  selectButton: {
    marginTop: 8,
  },
  selectButtonContent: {
    flexDirection: 'row-reverse',
  },
});

export default AccountSelectionScreen;
