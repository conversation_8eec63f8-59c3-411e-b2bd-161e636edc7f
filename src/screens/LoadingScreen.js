import React from "react";
import { View, StyleSheet } from "react-native";
import { ActivityIndicator, Text } from "react-native-paper";

const LoadingScreen = () => {
  return (
    <View style={styles.container}>
      <Text variant="headlineMedium" style={styles.title}>
        CostCompass
      </Text>
      <ActivityIndicator size="large" style={styles.loader} />
      <Text variant="bodyMedium" style={styles.subtitle}>
        Loading your dashboard...
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#f5f5f5",
  },
  title: {
    marginBottom: 30,
    color: "#2196F3",
    fontWeight: "bold",
  },
  loader: {
    marginBottom: 20,
  },
  subtitle: {
    color: "#666",
  },
});

export default LoadingScreen;
