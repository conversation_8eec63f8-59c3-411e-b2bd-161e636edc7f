import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import {
  Text,
  Card,
  Button,
  ActivityIndicator,
  List,
  Chip,
  Divider,
  IconButton,
  Surface
} from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { colors, spacing, borderRadius, commonStyles, typography, elevation } from '../styles/theme';
import { useClerkAuth } from '../contexts/ClerkAuthContext';
import DatabaseService from '../services/database';

const CompanySelectionScreen = ({ navigation }) => {
  const { user, selectCompany, logout } = useClerkAuth();
  const [companies, setCompanies] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadUserCompanies();
  }, []);

  const loadUserCompanies = async () => {
    try {
      setLoading(true);
      const userCompanies = await DatabaseService.getUserCompanies(user.id);
      setCompanies(userCompanies);
    } catch (error) {
      console.error('Error loading user companies:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCompanySelect = async (company) => {
    try {
      setLoading(true);
      await selectCompany(company);
      // Navigation will be handled by AuthContext
    } catch (error) {
      console.error('Error selecting company:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    await logout();
  };

  if (loading) {
    return (
      <View style={commonStyles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text variant="bodyLarge" style={styles.loadingText}>
            Loading your companies...
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={commonStyles.container}>
      {/* Soldo-style Header */}
      <LinearGradient
        colors={colors.gradientPrimary}
        style={commonStyles.headerGradient}
      >
        <View style={commonStyles.headerContent}>
          <View style={styles.headerIconContainer}>
            <Ionicons
              name="business"
              size={32}
              color={colors.textOnPrimary}
            />
          </View>
          <Text variant="headlineLarge" style={commonStyles.headerTitle}>
            Select Company
          </Text>
          <Text variant="bodyLarge" style={commonStyles.headerSubtitle}>
            Welcome, {user?.name}! Choose your company to continue.
          </Text>
        </View>
      </LinearGradient>

      <ScrollView
        contentContainerStyle={commonStyles.scrollContainer}
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >

        <Card style={commonStyles.cardLarge}>
        <Card.Content>
          <View style={styles.cardHeader}>
            <Text variant="titleLarge" style={styles.cardTitle}>
              Your Companies
            </Text>
            <IconButton
              icon="logout"
              size={24}
              onPress={handleLogout}
              iconColor="#f44336"
            />
          </View>
          
          <Text variant="bodyMedium" style={styles.description}>
            You have access to {companies.length} {companies.length === 1 ? 'company' : 'companies'}. 
            Select one to continue.
          </Text>

          <Divider style={styles.divider} />

          {companies.length === 0 ? (
            <View style={styles.noCompaniesContainer}>
              <Text variant="bodyLarge" style={styles.noCompaniesText}>
                No companies found
              </Text>
              <Text variant="bodyMedium" style={styles.noCompaniesSubtext}>
                Contact your administrator to be added to a company.
              </Text>
              <Button
                mode="outlined"
                onPress={handleLogout}
                style={styles.logoutButton}
                icon="logout"
              >
                Logout
              </Button>
            </View>
          ) : (
            companies.map((company) => (
              <Surface key={company.id} style={styles.companyCard} elevation={elevation.sm}>
                <Card.Content>
                  <View style={styles.companyHeader}>
                    <View style={styles.companyInfo}>
                      <Text variant="titleMedium" style={styles.companyName}>
                        {company.name}
                      </Text>
                      {company.registration && (
                        <Text variant="bodySmall" style={styles.companyRegistration}>
                          Registration: {company.registration}
                        </Text>
                      )}
                      <Text variant="bodySmall" style={styles.companyCapacity}>
                        Capacity: {company.current_occupancy}/{company.capacity} {company.capacity_type}
                      </Text>
                    </View>
                    <View style={styles.companyBadges}>
                      {company.user_role.is_admin && (
                        <Chip icon="shield-account" style={styles.adminChip}>
                          Admin
                        </Chip>
                      )}
                      <Chip icon="calendar" style={styles.joinedChip}>
                        Joined {new Date(company.user_role.joined_at).toLocaleDateString()}
                      </Chip>
                    </View>
                  </View>
                  
                  <Button
                    mode="contained"
                    onPress={() => handleCompanySelect(company)}
                    style={styles.selectButton}
                    icon="arrow-right"
                    contentStyle={styles.selectButtonContent}
                  >
                    Select Company
                  </Button>
                </Card.Content>
              </Surface>
            ))
          )}
        </Card.Content>
        </Card>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  headerIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
    elevation: elevation.md,
  },
  scrollView: {
    marginTop: -spacing.lg,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: spacing.lg,
    color: colors.textSecondary,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  cardTitle: {
    color: colors.text,
    fontWeight: typography.fontWeights.bold,
    fontSize: typography.fontSizes.xl,
  },
  description: {
    color: colors.textSecondary,
    marginBottom: spacing.lg,
    fontSize: typography.fontSizes.md,
  },
  divider: {
    marginBottom: spacing.xl,
  },
  noCompaniesContainer: {
    alignItems: 'center',
    padding: spacing.xl,
  },
  noCompaniesText: {
    color: colors.text,
    marginBottom: spacing.sm,
    fontSize: typography.fontSizes.lg,
  },
  noCompaniesSubtext: {
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.xl,
    fontSize: typography.fontSizes.md,
  },
  logoutButton: {
    marginTop: spacing.md,
    borderRadius: borderRadius.lg,
  },
  companyCard: {
    marginBottom: spacing.lg,
    backgroundColor: colors.surface,
    borderRadius: borderRadius.lg,
    padding: spacing.lg,
  },
  companyHeader: {
    marginBottom: spacing.lg,
  },
  companyInfo: {
    marginBottom: spacing.lg,
  },
  companyName: {
    color: colors.text,
    fontWeight: typography.fontWeights.bold,
    marginBottom: spacing.xs,
    fontSize: typography.fontSizes.lg,
  },
  companyRegistration: {
    color: colors.textSecondary,
    marginBottom: spacing.xs,
    fontSize: typography.fontSizes.sm,
  },
  companyCapacity: {
    color: colors.textSecondary,
    fontSize: typography.fontSizes.sm,
  },
  companyBadges: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  adminChip: {
    backgroundColor: colors.success + '20',
    borderColor: colors.success + '40',
  },
  joinedChip: {
    backgroundColor: colors.primary + '20',
    borderColor: colors.primary + '40',
  },
  selectButton: {
    marginTop: spacing.sm,
    borderRadius: borderRadius.lg,
  },
  selectButtonContent: {
    flexDirection: 'row-reverse',
    paddingVertical: spacing.md,
  },
});

export default CompanySelectionScreen;
