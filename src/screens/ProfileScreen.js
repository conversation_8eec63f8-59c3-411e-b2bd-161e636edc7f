import React, { useState, useEffect } from "react";
import { View, StyleSheet, ScrollView, Alert, Dimensions, Platform, Image } from "react-native";
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import {
  Text,
  Card,
  Button,
  List,
  Divider,
  Chip,
  Dialog,
  Portal,
  TextInput,
  RadioButton,
  ActivityIndicator,
  Surface,
  Avatar,
} from "react-native-paper";
import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from 'expo-linear-gradient';
import { colors, spacing, borderRadius, commonStyles, typography, elevation } from '../styles/theme';
import { useAuth } from "../contexts/AuthContext";
import DatabaseService from "../services/database";

const { width } = Dimensions.get("window");

// Simple gradient fallback
const GradientView = ({ colors, style, children, ...props }) => (
  <View style={[style, { backgroundColor: colors[0] }]} {...props}>
    {children}
  </View>
);

const ProfileScreen = () => {
  const { user, company, userCompanies, logout, login, updateCompany, selectCompany, changePassword } = useAuth();
  const insets = useSafeAreaInsets();
  
  const [logoutDialogVisible, setLogoutDialogVisible] = useState(false);
  const [companySwitchDialogVisible, setCompanySwitchDialogVisible] = useState(false);
  const [accountSwitchDialogVisible, setAccountSwitchDialogVisible] = useState(false);
  const [userAccounts, setUserAccounts] = useState([]);
  
  // Password change state
  const [passwordDialogVisible, setPasswordDialogVisible] = useState(false);
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmNewPassword, setConfirmNewPassword] = useState('');
  const [passwordLoading, setPasswordLoading] = useState(false);
  
  // Company editing state
  const [editCompanyDialogVisible, setEditCompanyDialogVisible] = useState(false);
  const [companyLoading, setCompanyLoading] = useState(false);
  const [companyName, setCompanyName] = useState('');
  const [registration, setRegistration] = useState('');
  const [capacity, setCapacity] = useState('');
  const [currentOccupancy, setCurrentOccupancy] = useState('');
  const [capacityType, setCapacityType] = useState('beds');

  // Initialize company form data when company changes
  useEffect(() => {
    if (company) {
      setCompanyName(company.name || '');
      setRegistration(company.registration || '');
      setCapacity(company.capacity?.toString() || '');
      setCurrentOccupancy(company.current_occupancy?.toString() || '');
      setCapacityType(company.capacity_type || 'beds');
    }
  }, [company]);

  const handleLogout = () => {
    setLogoutDialogVisible(true);
  };

  const confirmLogout = async () => {
    setLogoutDialogVisible(false);
    await logout();
  };

  const handleEditCompany = () => {
    if (!user?.is_admin) {
      Alert.alert('Access Denied', 'Only administrators can edit company details');
      return;
    }
    setEditCompanyDialogVisible(true);
  };

  const validateCompanyForm = async () => {
    if (!companyName.trim()) {
      Alert.alert('Error', 'Please enter a company name');
      return false;
    }
    
    if (!capacity.trim()) {
      Alert.alert('Error', 'Please enter the capacity');
      return false;
    }
    
    const capacityNum = parseInt(capacity);
    if (isNaN(capacityNum) || capacityNum <= 0) {
      Alert.alert('Error', 'Please enter a valid capacity (positive number)');
      return false;
    }
    
    const occupancyNum = parseInt(currentOccupancy) || 0;
    if (occupancyNum < 0) {
      Alert.alert('Error', 'Current occupancy cannot be negative');
      return false;
    }
    
    if (occupancyNum > capacityNum) {
      Alert.alert('Error', 'Current occupancy cannot exceed capacity');
      return false;
    }
    
    return true;
  };

  const handleSaveCompany = async () => {
    const isValid = await validateCompanyForm();
    if (!isValid) return;
    
    setCompanyLoading(true);
    try {
      const updateData = {
        name: companyName.trim(),
        registration: registration.trim(),
        capacity: parseInt(capacity),
        currentOccupancy: parseInt(currentOccupancy) || 0,
        capacityType
      };
      
      const result = await updateCompany(updateData);
      if (result.success) {
        setEditCompanyDialogVisible(false);
        Alert.alert('Success', 'Company details updated successfully!');
      } else {
        Alert.alert('Error', result.error || 'Failed to update company details');
      }
    } catch (error) {
      console.error('Error updating company:', error);
      Alert.alert('Error', 'Failed to update company details. Please try again.');
    } finally {
      setCompanyLoading(false);
    }
  };

  const handleSwitchCompany = () => {
    if (userCompanies && userCompanies.length > 1) {
      setCompanySwitchDialogVisible(true);
    }
  };

  const handleCompanySelect = async (selectedCompany) => {
    try {
      setCompanySwitchDialogVisible(false);
      await selectCompany(selectedCompany);
    } catch (error) {
      console.error('Error switching company:', error);
      Alert.alert('Error', 'Failed to switch company. Please try again.');
    }
  };

  const handleSwitchAccount = async () => {
    if (!user?.email) return;
    
    try {
      const accounts = await DatabaseService.getUserAccountsByEmail(user.email);
      const activeAccounts = accounts.filter(acc => acc.is_active && acc.id !== user.id);
      
      if (activeAccounts.length > 0) {
        setUserAccounts(activeAccounts);
        setAccountSwitchDialogVisible(true);
      } else {
        Alert.alert('No Other Accounts', 'You don\'t have any other active accounts with this email address.');
      }
    } catch (error) {
      console.error('Error loading user accounts:', error);
      Alert.alert('Error', 'Failed to load accounts. Please try again.');
    }
  };

  const handleAccountSelect = async (selectedAccount) => {
    try {
      setAccountSwitchDialogVisible(false);
      // This will trigger account selection flow
      await logout(); // The user will need to login again, which will show account selection if multiple accounts exist
    } catch (error) {
      console.error('Error switching account:', error);
      Alert.alert('Error', 'Failed to switch account. Please try again.');
    }
  };

  const handleChangePassword = () => {
    setPasswordDialogVisible(true);
  };

  const validatePasswordForm = () => {
    if (!currentPassword.trim()) {
      Alert.alert('Error', 'Please enter your current password');
      return false;
    }
    
    if (!newPassword.trim()) {
      Alert.alert('Error', 'Please enter a new password');
      return false;
    }
    
    if (newPassword.length < 6) {
      Alert.alert('Error', 'New password must be at least 6 characters long');
      return false;
    }
    
    if (newPassword !== confirmNewPassword) {
      Alert.alert('Error', 'New passwords do not match');
      return false;
    }
    
    if (currentPassword === newPassword) {
      Alert.alert('Error', 'New password must be different from current password');
      return false;
    }
    
    return true;
  };

  const handleSavePassword = async () => {
    if (!validatePasswordForm()) return;
    
    setPasswordLoading(true);
    try {
      const result = await changePassword(currentPassword, newPassword);
      if (result.success) {
        setPasswordDialogVisible(false);
        setCurrentPassword('');
        setNewPassword('');
        setConfirmNewPassword('');
        Alert.alert('Success', 'Password changed successfully!');
      } else {
        Alert.alert('Error', result.error || 'Failed to change password');
      }
    } catch (error) {
      console.error('Error changing password:', error);
      Alert.alert('Error', 'Failed to change password. Please try again.');
    } finally {
      setPasswordLoading(false);
    }
  };

  const handleCancelPasswordChange = () => {
    setPasswordDialogVisible(false);
    setCurrentPassword('');
    setNewPassword('');
    setConfirmNewPassword('');
  };

  return (
    <View style={commonStyles.container}>
      {/* Split Layout - Blue section on left, content on right */}
      <View style={styles.mainContent}>
        {/* Left Side - Blue Header Section (Desktop only) */}
        {Platform.OS === 'web' && width > 600 && (
          <View style={styles.leftSide}>
            <LinearGradient
              colors={colors.gradientPrimary}
              style={styles.leftSideGradient}
            >
              <View style={styles.leftSideContent}>
                {/* App Logo and Name */}
                <View style={styles.logoSection}>
                  <View style={styles.appLogoContainer}>
                    <Image
                      source={require("../../assets/logo.png")}
                      style={styles.appLogo}
                      resizeMode="contain"
                    />
                  </View>
                  <Text style={styles.appNameBig}>CostCompass</Text>
                </View>

                {/* Page Header */}
                <View style={styles.pageHeader}>
                  <View style={styles.headerIconContainer}>
                    <Ionicons
                      name="person-circle"
                      size={32}
                      color={colors.textOnPrimary}
                    />
                  </View>
                  <Text style={styles.leftSideTitle}>
                    Profile & Settings
                  </Text>
                  <Text style={styles.leftSideSubtitle}>
                    Manage your account and company information
                  </Text>
                </View>

                {/* User Info */}
                <View style={styles.leftSideInfo}>
                  <View style={styles.leftSideInfoItem}>
                    <Ionicons name="person" size={20} color={colors.textOnPrimary} />
                    <Text style={styles.leftSideInfoText}>
                      {user?.name}
                    </Text>
                  </View>
                  <View style={styles.leftSideInfoItem}>
                    <Ionicons name="mail" size={20} color={colors.textOnPrimary} />
                    <Text style={styles.leftSideInfoText}>
                      {user?.email}
                    </Text>
                  </View>
                  {user?.is_admin && (
                    <View style={styles.leftSideInfoItem}>
                      <Ionicons name="shield-checkmark" size={20} color={colors.textOnPrimary} />
                      <Text style={styles.leftSideInfoText}>
                        Administrator
                      </Text>
                    </View>
                  )}
                  {company && (
                    <View style={styles.leftSideInfoItem}>
                      <Ionicons name="business" size={20} color={colors.textOnPrimary} />
                      <Text style={styles.leftSideInfoText}>
                        {company.name}
                      </Text>
                    </View>
                  )}
                </View>

                {/* Logout Button under company name */}
                <View style={styles.leftSideLogoutUnderCompany}>
                  <Button
                    mode="contained"
                    onPress={handleLogout}
                    icon="logout"
                    style={styles.logoutButtonLeft}
                    buttonColor="rgba(244, 67, 54, 0.9)"
                    textColor={colors.textOnPrimary}
                  >
                    Logout
                  </Button>
                </View>
              </View>
            </LinearGradient>
          </View>
        )}

        {/* Right Side - Main Content */}
        <View style={styles.rightSide}>
          {/* Mobile Header (only shown on mobile) */}
          {(Platform.OS !== 'web' || width <= 600) && (
            <LinearGradient
              colors={colors.gradientPrimary}
              style={styles.mobileHeader}
            >
              <View style={styles.mobileHeaderContent}>
                <View style={styles.headerIconContainer}>
                  <Ionicons
                    name="person-circle"
                    size={32}
                    color={colors.textOnPrimary}
                  />
                </View>
                <Text style={styles.mobileHeaderTitle}>
                  Profile & Settings
                </Text>
                <Text style={styles.mobileHeaderSubtitle}>
                  Manage your account and company information
                </Text>

                {/* Mobile Logout Button */}
                <View style={styles.mobileLogout}>
                  <Button
                    mode="contained"
                    onPress={handleLogout}
                    icon="logout"
                    style={styles.logoutButtonMobile}
                    buttonColor="rgba(244, 67, 54, 0.9)"
                    textColor={colors.textOnPrimary}
                  >
                    Logout
                  </Button>
                </View>
              </View>
            </LinearGradient>
          )}

          <ScrollView
            contentContainerStyle={styles.rightSideScrollContainer}
            style={styles.rightSideScrollView}
            showsVerticalScrollIndicator={false}
          >
        {/* Top Right Action Buttons */}
        <View style={styles.topRightActions}>
          <Button
            mode="contained"
            onPress={handleChangePassword}
            compact
            style={styles.topRightButton}
            buttonColor={colors.primary}
            children="Change Password"
          />
          <Button
            mode="contained"
            onPress={handleSwitchAccount}
            icon="swap-horizontal"
            compact
            style={styles.topRightButton}
            buttonColor={colors.success}
          >
            Switch Account
          </Button>
        </View>

        {/* Profile Card - Soldo-inspired */}
        <Card style={commonStyles.cardLarge}>
          <Card.Content style={styles.profileCardContent}>
            <View style={styles.profileHeader}>
              <Surface style={styles.avatarContainer} elevation={elevation.md}>
                <Avatar.Text
                  size={80}
                  label={user?.name?.charAt(0)?.toUpperCase() || 'U'}
                  style={styles.avatar}
                  labelStyle={styles.avatarLabel}
                />
              </Surface>
              <View style={styles.userInfo}>
                <Text variant="headlineSmall" style={styles.userName}>
                  {user?.name}
                </Text>
                <Text variant="bodyMedium" style={styles.userEmail}>
                  {user?.email}
                </Text>
                <View style={styles.userBadges}>
                  {user?.is_admin && (
                    <Chip
                      icon="shield-account"
                      style={styles.adminChip}
                      textStyle={styles.adminChipText}
                    >
                      Administrator
                    </Chip>
                  )}
                  <Chip
                    icon="account-check"
                    style={styles.statusChip}
                    textStyle={styles.statusChipText}
                  >
                    Active
                  </Chip>
                </View>
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* Switch Company Card - Only show if user has multiple companies */}
        {userCompanies && userCompanies.length > 1 && (
          <Card style={commonStyles.cardLarge}>
            <Card.Content>
              <View style={styles.sectionHeader}>
                <View style={styles.sectionTitleContainer}>
                  <Ionicons name="business" size={24} color={colors.accent} />
                  <Text variant="titleLarge" style={styles.sectionTitle}>
                    Company Access
                  </Text>
                </View>
                <Button
                  mode="contained"
                  onPress={handleSwitchCompany}
                  icon="swap-horizontal"
                  buttonColor={colors.accent}
                >
                  Switch Company
                </Button>
              </View>
              <Text variant="bodyMedium" style={styles.switchDescription}>
                You have access to multiple companies. Click the button above to switch between them.
              </Text>
            </Card.Content>
          </Card>
        )}

        {/* Account Information Card - Soldo-inspired */}
        <Card style={commonStyles.cardLarge}>
          <Card.Content>
            <View style={styles.sectionHeader}>
              <View style={styles.sectionTitleContainer}>
                <Ionicons name="person-circle" size={24} color="#1976D2" />
                <Text variant="titleLarge" style={styles.sectionTitle}>
                  Account Information
                </Text>
              </View>
            </View>
            
            <View style={styles.infoGrid}>
              <Surface style={styles.infoItem} elevation={1}>
                <View style={styles.infoItemContent}>
                  <Ionicons name="person" size={20} color="#666" />
                  <View style={styles.infoItemText}>
                    <Text variant="bodySmall" style={styles.infoLabel}>Full Name</Text>
                    <Text variant="bodyLarge" style={styles.infoValue}>{user?.name}</Text>
                  </View>
                </View>
              </Surface>
              
              <Surface style={styles.infoItem} elevation={1}>
                <View style={styles.infoItemContent}>
                  <Ionicons name="mail" size={20} color="#666" />
                  <View style={styles.infoItemText}>
                    <Text variant="bodySmall" style={styles.infoLabel}>Email Address</Text>
                    <Text variant="bodyLarge" style={styles.infoValue}>{user?.email}</Text>
                  </View>
                </View>
              </Surface>
              
              <Surface style={styles.infoItem} elevation={1}>
                <View style={styles.infoItemContent}>
                  <Ionicons name="shield-checkmark" size={20} color="#666" />
                  <View style={styles.infoItemText}>
                    <Text variant="bodySmall" style={styles.infoLabel}>Role</Text>
                    <Text variant="bodyLarge" style={styles.infoValue}>
                      {user?.is_admin ? 'Administrator' : 'User'}
                    </Text>
                  </View>
                </View>
              </Surface>
              
              <Surface style={styles.infoItem} elevation={1}>
                <View style={styles.infoItemContent}>
                  <Ionicons name="calendar" size={20} color="#666" />
                  <View style={styles.infoItemText}>
                    <Text variant="bodySmall" style={styles.infoLabel}>Member Since</Text>
                    <Text variant="bodyLarge" style={styles.infoValue}>
                      {user?.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}
                    </Text>
                  </View>
                </View>
              </Surface>
            </View>
          </Card.Content>
        </Card>

        {/* Company Information Card - Soldo-inspired */}
        {company && (
          <Card style={commonStyles.cardLarge}>
            <Card.Content>
              <View style={styles.sectionHeader}>
                <View style={styles.sectionTitleContainer}>
                  <Ionicons name="business" size={24} color="#2196F3" />
                  <Text variant="titleLarge" style={styles.sectionTitle}>
                    Company Information
                  </Text>
                </View>
                <View style={styles.headerButtons}>
                  {user?.is_admin && (
                    <Button 
                      mode="contained" 
                      compact 
                      onPress={handleEditCompany}
                      icon="pencil"
                      buttonColor="#2196F3"
                    >
                      Edit
                    </Button>
                  )}
                </View>
              </View>
              
              <View style={styles.companyOverview}>
                <Surface style={styles.companyCard} elevation={2}>
                  <View style={styles.companyHeader}>
                    <Surface style={styles.companyIcon} elevation={1}>
                      <Ionicons name="business" size={32} color="#2196F3" />
                    </Surface>
                    <View style={styles.companyInfo}>
                      <Text variant="headlineSmall" style={styles.companyName}>
                        {company.name}
                      </Text>
                      <Text variant="bodyMedium" style={styles.companyAccount}>
                        Account: {company.account_number}
                      </Text>
                    </View>
                  </View>
                </Surface>
              </View>
              
              <View style={styles.companyStats}>
                <Surface style={styles.statCard} elevation={1}>
                  <View style={styles.statContent}>
                    <Ionicons name="home" size={24} color="#2196F3" />
                    <Text variant="headlineMedium" style={styles.statNumber}>
                      {company.capacity}
                    </Text>
                    <Text variant="bodySmall" style={styles.statLabel}>
                      Total {company.capacity_type}
                    </Text>
                  </View>
                </Surface>
                
                <Surface style={styles.statCard} elevation={1}>
                  <View style={styles.statContent}>
                    <Ionicons name="people" size={24} color="#42A5F5" />
                    <Text variant="headlineMedium" style={styles.statNumber}>
                      {company.current_occupancy}
                    </Text>
                    <Text variant="bodySmall" style={styles.statLabel}>
                      Current Occupancy
                    </Text>
                  </View>
                </Surface>
                
                <Surface style={styles.statCard} elevation={1}>
                  <View style={styles.statContent}>
                    <Ionicons name="analytics" size={24} color="#03A9F4" />
                    <Text variant="headlineMedium" style={styles.statNumber}>
                      {Math.round((company.current_occupancy / company.capacity) * 100)}%
                    </Text>
                    <Text variant="bodySmall" style={styles.statLabel}>
                      Occupancy Rate
                    </Text>
                  </View>
                </Surface>
              </View>
              
              {company.registration && (
                <Surface style={styles.registrationInfo} elevation={1}>
                  <View style={styles.registrationContent}>
                    <Ionicons name="document-text" size={20} color="#666" />
                    <View style={styles.registrationText}>
                      <Text variant="bodySmall" style={styles.infoLabel}>Registration Number</Text>
                      <Text variant="bodyLarge" style={styles.infoValue}>{company.registration}</Text>
                    </View>
                  </View>
                </Surface>
              )}
            </Card.Content>
          </Card>
        )}

        {/* App Info Card */}
        <Card style={commonStyles.cardLarge}>
          <Card.Content>
            <Text variant="titleLarge" style={styles.sectionTitle}>
              About CostCompass
            </Text>
            <Divider style={styles.divider} />
            <List.Item
              title="Version"
              description="1.0.0"
              left={(props) => <List.Icon {...props} icon="information" />}
            />
            <List.Item
              title="Support"
              description="Contact your administrator"
              left={(props) => <List.Icon {...props} icon="help-circle" />}
            />
          </Card.Content>
        </Card>


          </ScrollView>
        </View>
      </View>

      {/* Logout Confirmation Dialog */}
      <Portal>
          <Dialog
            visible={logoutDialogVisible}
            onDismiss={() => setLogoutDialogVisible(false)}
            style={styles.logoutDialog}
          >
            <Dialog.Title>Logout</Dialog.Title>
            <Dialog.Content>
              <Text variant="bodyMedium">Are you sure you want to logout?</Text>
            </Dialog.Content>
            <Dialog.Actions>
              <Button onPress={() => setLogoutDialogVisible(false)}>Cancel</Button>
              <Button onPress={confirmLogout} buttonColor="#f44336">Logout</Button>
            </Dialog.Actions>
          </Dialog>
        </Portal>

        {/* Edit Company Dialog */}
        <Portal>
          <Dialog 
            visible={editCompanyDialogVisible} 
            onDismiss={() => setEditCompanyDialogVisible(false)}
            style={styles.companyDialog}
          >
            <Dialog.Title>Edit Company Details</Dialog.Title>
            <Dialog.Content>
              <TextInput
                label="Company Name *"
                value={companyName}
                onChangeText={setCompanyName}
                mode="outlined"
                style={styles.dialogInput}
                disabled={companyLoading}
              />
              <TextInput
                label="Registration Number"
                value={registration}
                onChangeText={setRegistration}
                mode="outlined"
                style={styles.dialogInput}
                disabled={companyLoading}
              />
              <TextInput
                label="Capacity *"
                value={capacity}
                onChangeText={setCapacity}
                mode="outlined"
                keyboardType="numeric"
                style={styles.dialogInput}
                disabled={companyLoading}
              />
              <TextInput
                label="Current Occupancy"
                value={currentOccupancy}
                onChangeText={setCurrentOccupancy}
                mode="outlined"
                keyboardType="numeric"
                style={styles.dialogInput}
                disabled={companyLoading}
              />
              
              <Text variant="bodyMedium" style={styles.capacityTypeLabel}>
                Capacity Type *
              </Text>
              <View style={styles.radioGroup}>
                <View style={styles.radioOption}>
                  <RadioButton
                    value="beds"
                    status={capacityType === 'beds' ? 'checked' : 'unchecked'}
                    onPress={() => setCapacityType('beds')}
                    disabled={companyLoading}
                  />
                  <Text variant="bodyMedium">Beds</Text>
                </View>
                <View style={styles.radioOption}>
                  <RadioButton
                    value="rooms"
                    status={capacityType === 'rooms' ? 'checked' : 'unchecked'}
                    onPress={() => setCapacityType('rooms')}
                    disabled={companyLoading}
                  />
                  <Text variant="bodyMedium">Rooms</Text>
                </View>
                <View style={styles.radioOption}>
                  <RadioButton
                    value="persons"
                    status={capacityType === 'persons' ? 'checked' : 'unchecked'}
                    onPress={() => setCapacityType('persons')}
                    disabled={companyLoading}
                  />
                  <Text variant="bodyMedium">Persons</Text>
                </View>
              </View>
            </Dialog.Content>
            <Dialog.Actions>
              <Button 
                onPress={() => setEditCompanyDialogVisible(false)}
                disabled={companyLoading}
              >
                Cancel
              </Button>
              <Button 
                onPress={handleSaveCompany}
                disabled={companyLoading}
              >
                {companyLoading ? <ActivityIndicator size="small" /> : 'Save'}
              </Button>
            </Dialog.Actions>
          </Dialog>
        </Portal>

        {/* Company Switch Dialog */}
        <Portal>
          <Dialog 
            visible={companySwitchDialogVisible} 
            onDismiss={() => setCompanySwitchDialogVisible(false)}
            style={styles.companySwitchDialog}
          >
            <Dialog.Title>Switch Company</Dialog.Title>
            <Dialog.Content>
              <Text variant="bodyMedium" style={styles.switchDescription}>
                Select a company to switch to:
              </Text>
              {userCompanies && userCompanies.map((comp) => (
                <Card 
                  key={comp.id} 
                  style={[
                    styles.companyOption,
                    comp.id === company?.id && styles.currentCompanyOption
                  ]}
                  onPress={() => comp.id !== company?.id && handleCompanySelect(comp)}
                >
                  <Card.Content style={styles.companyOptionContent}>
                    <View style={styles.companyOptionInfo}>
                      <Text variant="titleMedium" style={styles.companyOptionName}>
                        {comp.name}
                      </Text>
                      {comp.registration && (
                        <Text variant="bodySmall" style={styles.companyOptionRegistration}>
                          {comp.registration}
                        </Text>
                      )}
                      <View style={styles.companyOptionBadges}>
                        {comp.user_role.is_admin && (
                          <Text variant="bodySmall" style={styles.adminBadge}>
                            Admin
                          </Text>
                        )}
                        {comp.id === company?.id && (
                          <Text variant="bodySmall" style={styles.currentBadge}>
                            Current
                          </Text>
                        )}
                      </View>
                    </View>
                  </Card.Content>
                </Card>
              ))}
            </Dialog.Content>
            <Dialog.Actions>
              <Button onPress={() => setCompanySwitchDialogVisible(false)}>
                Cancel
              </Button>
            </Dialog.Actions>
          </Dialog>
        </Portal>

        {/* Account Switch Dialog */}
        <Portal>
          <Dialog 
            visible={accountSwitchDialogVisible} 
            onDismiss={() => setAccountSwitchDialogVisible(false)}
            style={styles.accountSwitchDialog}
          >
            <Dialog.Title>Switch Account</Dialog.Title>
            <Dialog.Content>
              <Text variant="bodyMedium" style={styles.switchDescription}>
                Select an account to switch to:
              </Text>
              {userAccounts.map((account) => (
                <Card 
                  key={account.id} 
                  style={styles.accountOption}
                  onPress={() => handleAccountSelect(account)}
                >
                  <Card.Content style={styles.accountOptionContent}>
                    <View style={styles.accountOptionInfo}>
                      <Text variant="titleMedium" style={styles.accountOptionName}>
                        {account.name}
                      </Text>
                      <Text variant="bodySmall" style={styles.accountOptionEmail}>
                        {account.email}
                      </Text>
                      <View style={styles.accountOptionBadges}>
                        {account.is_admin && (
                          <Text variant="bodySmall" style={styles.adminBadge}>
                            Admin
                          </Text>
                        )}
                        {account.last_login && (
                          <Text variant="bodySmall" style={styles.lastLoginBadge}>
                            Last login: {new Date(account.last_login).toLocaleDateString()}
                          </Text>
                        )}
                      </View>
                    </View>
                  </Card.Content>
                </Card>
              ))}
            </Dialog.Content>
            <Dialog.Actions>
              <Button onPress={() => setAccountSwitchDialogVisible(false)}>
                Cancel
              </Button>
            </Dialog.Actions>
          </Dialog>
        </Portal>

        {/* Password Change Dialog */}
        <Portal>
          <Dialog 
            visible={passwordDialogVisible} 
            onDismiss={handleCancelPasswordChange}
            style={styles.passwordDialog}
          >
            <Dialog.Title>Change Password</Dialog.Title>
            <Dialog.Content>
              <Text variant="bodyMedium" style={styles.passwordDescription}>
                Enter your current password and choose a new password.
              </Text>
              <TextInput
                label="Current Password"
                value={currentPassword}
                onChangeText={setCurrentPassword}
                mode="outlined"
                secureTextEntry
                style={styles.passwordInput}
                disabled={passwordLoading}
                autoCapitalize="none"
              />
              <TextInput
                label="New Password"
                value={newPassword}
                onChangeText={setNewPassword}
                mode="outlined"
                secureTextEntry
                style={styles.passwordInput}
                disabled={passwordLoading}
                autoCapitalize="none"
              />
              <TextInput
                label="Confirm New Password"
                value={confirmNewPassword}
                onChangeText={setConfirmNewPassword}
                mode="outlined"
                secureTextEntry
                style={styles.passwordInput}
                disabled={passwordLoading}
                autoCapitalize="none"
              />
              <Text variant="bodySmall" style={styles.passwordHint}>
                Password must be at least 6 characters long and different from your current password.
              </Text>
            </Dialog.Content>
            <Dialog.Actions>
              <Button 
                onPress={handleCancelPasswordChange}
                disabled={passwordLoading}
              >
                Cancel
              </Button>
              <Button 
                onPress={handleSavePassword}
                disabled={passwordLoading}
                loading={passwordLoading}
                mode="contained"
              >
                Change Password
              </Button>
            </Dialog.Actions>
          </Dialog>
        </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  // Split Layout Styles
  mainContent: {
    flex: 1,
    flexDirection: Platform.OS === 'web' && width > 600 ? 'row' : 'column',
  },
  // Left Side - Blue Section (Desktop only)
  leftSide: {
    flex: 0.4,
    minWidth: 350,
    maxWidth: 450,
  },
  leftSideGradient: {
    flex: 1,
    padding: spacing.lg,
    paddingTop: spacing.md,
    paddingBottom: spacing.xxxl, // Add bottom padding to move logout button up
    justifyContent: 'flex-start',
  },
  leftSideContent: {
    alignItems: 'flex-start',
    maxWidth: 350,
    alignSelf: 'center',
    width: '100%',
    flex: 1,
    justifyContent: 'space-between',
  },
  // Logo Section
  logoSection: {
    alignItems: 'flex-start',
    alignSelf: 'flex-start',
    marginBottom: spacing.xl,
    paddingBottom: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.2)',
    width: '100%',
  },
  appLogoContainer: {
    width: '100%', // Full width of the blue sidebar content area
    height: 120, // Taller to accommodate larger logo
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.lg,
    marginTop: spacing.sm,
    // Remove artificial shadows and borders for more natural integration
  },
  appLogo: {
    width: 280, // Almost full width of the blue sidebar content
    height: 100, // Proportional height
    // Remove tintColor to show original logo colors
  },
  appNameBig: {
    color: colors.textOnPrimary,
    fontSize: 32,
    fontWeight: typography.fontWeights.bold,
    textAlign: 'left',
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
    letterSpacing: 0.5,
  },
  // Page Header Section
  pageHeader: {
    alignItems: 'flex-start',
    marginBottom: spacing.lg,
    width: '100%',
  },
  leftSideTitle: {
    color: colors.textOnPrimary,
    fontSize: 24,
    fontWeight: typography.fontWeights.bold,
    textAlign: 'left',
    marginBottom: spacing.sm,
    textShadowColor: 'rgba(0,0,0,0.1)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  leftSideSubtitle: {
    color: colors.textOnPrimary,
    fontSize: 16,
    textAlign: 'left',
    opacity: 0.95,
    marginBottom: spacing.lg,
    textShadowColor: 'rgba(0,0,0,0.1)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },

  leftSideInfo: {
    marginTop: spacing.lg,
    alignSelf: 'stretch',
  },
  leftSideInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    padding: spacing.md,
    borderRadius: borderRadius.lg,
  },
  leftSideInfoText: {
    color: colors.textOnPrimary,
    fontSize: 14,
    fontWeight: '500',
    marginLeft: spacing.sm,
    flex: 1,
  },
  // Logout Button in Blue Section (old position - not used)
  leftSideLogout: {
    marginTop: 'auto',
    paddingTop: spacing.xl,
    width: '100%',
  },
  // Logout Button under company name
  leftSideLogoutUnderCompany: {
    marginTop: spacing.lg,
    width: '100%',
  },
  logoutButtonLeft: {
    borderRadius: borderRadius.md,
    elevation: elevation.sm,
    shadowColor: 'rgba(0, 0, 0, 0.3)',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  // Mobile Logout Button
  mobileLogout: {
    marginTop: spacing.lg,
    width: '100%',
    alignItems: 'center',
  },
  logoutButtonMobile: {
    borderRadius: borderRadius.md,
    elevation: elevation.sm,
    shadowColor: 'rgba(0, 0, 0, 0.3)',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  // Right Side - Main Content
  rightSide: {
    flex: 1.6,
    backgroundColor: colors.background,
  },
  rightSideScrollContainer: {
    padding: spacing.lg,
    paddingBottom: spacing.xxxl,
  },
  rightSideScrollView: {
    flex: 1,
  },
  // Top Right Action Buttons
  topRightActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: spacing.md,
    marginBottom: spacing.lg,
    paddingHorizontal: spacing.sm,
  },
  topRightButton: {
    borderRadius: borderRadius.md,
    elevation: elevation.sm,
    shadowColor: 'rgba(0, 0, 0, 0.1)',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  // Mobile Header
  mobileHeader: {
    paddingTop: spacing.xxxxl,
    paddingBottom: spacing.xxxl, // Increase bottom padding to move logout button up
    paddingHorizontal: spacing.xl,
    borderBottomLeftRadius: borderRadius.xxl,
    borderBottomRightRadius: borderRadius.xxl,
  },
  mobileHeaderContent: {
    alignItems: 'center',
  },
  mobileHeaderTitle: {
    color: colors.textOnPrimary,
    fontSize: 24,
    fontWeight: typography.fontWeights.bold,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  mobileHeaderSubtitle: {
    color: colors.textOnPrimary,
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.95,
  },

  headerIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
    elevation: elevation.sm,
    shadowColor: 'rgba(0, 0, 0, 0.3)',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },

  // Profile Card Styles
  profileCardContent: {
    padding: spacing.xxxl,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xl,
  },
  avatarContainer: {
    borderRadius: 40,
    backgroundColor: colors.surface,
  },
  avatar: {
    backgroundColor: colors.primary + '20',
  },
  avatarLabel: {
    color: colors.primary,
    fontWeight: typography.fontWeights.bold,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    color: colors.text,
    fontWeight: typography.fontWeights.bold,
    marginBottom: spacing.xs,
    fontSize: typography.fontSizes.xl,
  },
  userEmail: {
    color: colors.textSecondary,
    marginBottom: spacing.md,
    fontSize: typography.fontSizes.md,
  },
  userBadges: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  adminChip: {
    backgroundColor: colors.success + '20',
    borderColor: colors.success + '40',
  },
  adminChipText: {
    color: colors.success,
    fontWeight: typography.fontWeights.medium,
  },
  statusChip: {
    backgroundColor: colors.primary + '20',
    borderColor: colors.primary + '40',
  },
  statusChipText: {
    color: colors.primary,
    fontWeight: typography.fontWeights.medium,
  },


  // Section Styles
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  sectionTitle: {
    color: colors.text,
    fontWeight: typography.fontWeights.bold,
    fontSize: typography.fontSizes.xl,
  },
  headerButtons: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  // Info Grid Styles
  infoGrid: {
    gap: spacing.lg,
    marginTop: spacing.lg,
  },
  infoItem: {
    borderRadius: borderRadius.lg,
    backgroundColor: colors.surface,
  },
  infoItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.xl,
    gap: spacing.lg,
  },
  infoItemText: {
    flex: 1,
  },
  infoLabel: {
    color: colors.textSecondary,
    marginBottom: spacing.xs,
    fontSize: typography.fontSizes.sm,
  },
  infoValue: {
    color: colors.text,
    fontWeight: typography.fontWeights.medium,
    fontSize: typography.fontSizes.md,
  },
  // Company Styles
  companyOverview: {
    marginBottom: spacing.lg,
    marginTop: spacing.lg,
  },
  companyCard: {
    borderRadius: borderRadius.lg,
    backgroundColor: colors.surface,
  },
  companyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.xl,
    gap: spacing.lg,
  },
  companyIcon: {
    borderRadius: 20,
    padding: spacing.md,
    backgroundColor: colors.success + '20',
  },
  companyInfo: {
    flex: 1,
  },
  companyName: {
    fontWeight: typography.fontWeights.bold,
    marginBottom: spacing.xs,
    fontSize: typography.fontSizes.lg,
    color: colors.text,
  },
  companyAccount: {
    color: colors.textSecondary,
    fontSize: typography.fontSizes.sm,
  },
  companyStats: {
    flexDirection: 'row',
    gap: spacing.md,
    marginBottom: spacing.lg,
    marginTop: spacing.lg,
  },
  statCard: {
    flex: 1,
    borderRadius: borderRadius.lg,
    backgroundColor: colors.surface,
  },
  statContent: {
    alignItems: 'center',
    padding: spacing.xl,
    gap: spacing.sm,
  },
  statNumber: {
    fontWeight: typography.fontWeights.bold,
    color: colors.text,
    fontSize: typography.fontSizes.lg,
  },
  statLabel: {
    color: colors.textSecondary,
    textAlign: 'center',
    fontSize: typography.fontSizes.sm,
  },
  registrationInfo: {
    borderRadius: borderRadius.lg,
    backgroundColor: colors.surface,
  },
  registrationContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.xl,
    gap: spacing.lg,
  },
  registrationText: {
    flex: 1,
  },
  divider: {
    marginBottom: 8,
  },
  companyDialog: {
    maxHeight: '80%',
  },
  logoutDialog: {
    maxWidth: 400,
    alignSelf: 'center',
  },
  dialogInput: {
    marginBottom: 16,
  },
  capacityTypeLabel: {
    fontWeight: 'bold',
    marginBottom: 8,
    marginTop: 8,
  },
  radioGroup: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  companySwitchDialog: {
    maxHeight: '70%',
  },
  switchDescription: {
    marginBottom: 16,
  },
  companyOption: {
    marginBottom: 8,
    backgroundColor: '#f8f9fa',
  },
  currentCompanyOption: {
    backgroundColor: '#e3f2fd',
    borderColor: '#2196F3',
    borderWidth: 1,
  },
  companyOptionContent: {
    paddingVertical: 12,
  },
  companyOptionInfo: {
    flex: 1,
  },
  companyOptionName: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  companyOptionRegistration: {
    color: '#666',
    marginBottom: 8,
  },
  companyOptionBadges: {
    flexDirection: 'row',
    gap: 8,
  },
  adminBadge: {
    backgroundColor: '#e3f2fd',
    color: '#1976d2',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    fontSize: 12,
  },
  currentBadge: {
    backgroundColor: '#e3f2fd',
    color: '#1976d2',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    fontSize: 12,
  },
  accountSwitchDialog: {
    maxHeight: '70%',
  },
  accountOption: {
    marginBottom: 8,
    backgroundColor: '#f8f9fa',
  },
  accountOptionContent: {
    paddingVertical: 12,
  },
  accountOptionInfo: {
    flex: 1,
  },
  accountOptionName: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  accountOptionEmail: {
    color: '#666',
    marginBottom: 8,
  },
  accountOptionBadges: {
    flexDirection: 'row',
    gap: 8,
    flexWrap: 'wrap',
  },
  lastLoginBadge: {
    backgroundColor: '#e3f2fd',
    color: '#1976d2',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    fontSize: 12,
  },
  passwordDialog: {
    maxWidth: 400,
    alignSelf: 'center',
  },
  passwordDescription: {
    marginBottom: 16,
    color: '#666',
  },
  passwordInput: {
    marginBottom: 16,
  },
  passwordHint: {
    color: '#666',
    fontStyle: 'italic',
    marginTop: 8,
  },
});

export default ProfileScreen;
