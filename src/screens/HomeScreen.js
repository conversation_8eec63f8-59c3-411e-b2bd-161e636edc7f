import React, { useState, useEffect, useCallback } from 'react';
import { View, StyleSheet, ScrollView, RefreshControl, Dimensions, TouchableOpacity, Animated, Platform, Image } from 'react-native';
import {
  Text,
  Card,
  Button,
  FAB,
  Chip,
  Divider,
  Surface,
  ProgressBar,
  Badge,
  IconButton,
  ActivityIndicator,
  Snackbar
} from 'react-native-paper';
import { useFocusEffect } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { colors, spacing, borderRadius, commonStyles, typography, elevation } from '../styles/theme';

import { useClerkAuth } from '../contexts/ClerkAuthContext';
import DatabaseService from '../services/database';
import OverdueNotificationService from '../services/overdueNotificationService';

const { width } = Dimensions.get('window');

// Simple gradient fallback
const GradientView = ({ colors, style, children, ...props }) => (
  <View style={[style, { backgroundColor: colors[0] }]} {...props}>
    {children}
  </View>
);

const HomeScreen = ({ navigation }) => {
  const { user, company } = useClerkAuth();
  const [benchmarks, setBenchmarks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);
  const [unreadNotifications, setUnreadNotifications] = useState(0);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [planLimits, setPlanLimits] = useState(null);
  const [dashboardStats, setDashboardStats] = useState({
    totalBenchmarks: 0,
    activeBenchmarks: 0,
    overBudget: 0,
    avgVariance: 0
  });
  const fadeAnim = useState(new Animated.Value(0))[0];

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();
  }, [fadeAnim]);

  const loadData = async () => {
    try {
      setError(null);
      if (company) {
        const benchmarkData = await DatabaseService.getBenchmarksByCompany(company.id);
        setBenchmarks(benchmarkData);

        // Calculate dashboard stats
        const stats = {
          totalBenchmarks: benchmarkData.length,
          activeBenchmarks: benchmarkData.filter(b => b.is_active !== false).length,
          overBudget: 0, // This would need actual spend data
          avgVariance: 0 // This would need actual spend data
        };
        setDashboardStats(stats);

        // Load unread notifications count
        const notifications = await DatabaseService.getNotificationsByCompany(company.id, true);
        setUnreadNotifications(notifications.length);

        // Load plan limits
        const benchmarkLimits = await DatabaseService.checkPlanLimits(company.id, 'benchmarks');
        setPlanLimits(benchmarkLimits);
      }
    } catch (error) {
      console.error('Error loading data:', error);
      setError('Failed to load data. Please try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      console.log('Home screen focused, loading data...');
      loadData();
    }, [company])
  );

  const onRefresh = () => {
    setRefreshing(true);
    loadData();
  };

  const calculateOccupancyPercentage = () => {
    if (!company || company.capacity === 0) return 0;
    return Math.round((company.current_occupancy / company.capacity) * 100);
  };

  const getOccupancyColor = (percentage) => {
    if (percentage >= 90) return colors.success;
    if (percentage >= 70) return colors.warning;
    return colors.error;
  };

  const navigateToBenchmarkDetail = (benchmark) => {
    navigation.navigate('BenchmarkDetail', { benchmarkId: benchmark.id });
  };

  const navigateToBenchmarkSetup = async () => {
    // Check plan limits before navigating
    if (planLimits && !planLimits.allowed) {
      const limitText = planLimits.limit === -1
        ? "unlimited benchmarks"
        : `${planLimits.limit} benchmarks`;
      Alert.alert(
        "Benchmark Limit Reached",
        `Your ${company?.subscription_plan || 'current'} plan allows ${limitText} and you currently have ${planLimits.current}. Please upgrade your plan to create more benchmarks.`,
        [
          { text: "Cancel", style: "cancel" },
          { text: "Upgrade Plan", onPress: () => navigation.navigate("Subscription") }
        ]
      );
      return;
    }
    navigation.navigate('BenchmarkSetup');
  };

  const testNotification = async () => {
    if (!company || benchmarks.length === 0) {
      Alert.alert("Test Error", "No company or benchmarks available for testing");
      return;
    }

    try {
      const testBenchmark = benchmarks[0];

      // Create a test notification
      await DatabaseService.createNotification({
        companyId: company.id,
        benchmarkId: testBenchmark.id,
        type: 'benchmark_exceeded',
        message: `Test: Benchmark "${testBenchmark.name}" exceeded by £50.00`,
        actualAmount: (testBenchmark.amount || 100) + 50,
        benchmarkAmount: testBenchmark.amount || 100,
        variance: 50
      });

      setSnackbarMessage('Test notification created and email sent!');
      setSnackbarVisible(true);

      // Reload notifications count
      const notifications = await DatabaseService.getNotificationsByCompany(company.id, true);
      setUnreadNotifications(notifications.length);

    } catch (error) {
      console.error("Error creating test notification:", error);
      Alert.alert("Test Error", "Failed to create test notification: " + error.message);
    }
  };

  const testOverdueNotification = async () => {
    if (!company) {
      Alert.alert("Test Error", "No company available for testing");
      return;
    }

    try {
      // Test overdue notification check for this company
      await OverdueNotificationService.checkCompanyOverdueSpends(company);

      setSnackbarMessage('Overdue notification check completed!');
      setSnackbarVisible(true);

      // Reload notifications count
      const notifications = await DatabaseService.getNotificationsByCompany(company.id, true);
      setUnreadNotifications(notifications.length);

    } catch (error) {
      console.error("Error testing overdue notification:", error);
      Alert.alert("Test Error", "Failed to test overdue notification: " + error.message);
    }
  };

  const navigateToOccupancyDetail = () => {
    navigation.navigate('OccupancyDetail');
  };

  const formatAmount = (amount, period, currency = 'USD', showOccupancy = true) => {
    const currencySymbols = {
      'USD': '$',
      'EUR': '€',
      'GBP': '£',
      'CAD': 'C$',
      'AUD': 'A$',
      'JPY': '¥',
      'CHF': 'CHF',
      'CNY': '¥',
      'INR': '₹'
    };
    const symbol = currencySymbols[currency] || '$';

    // Apply occupancy multiplier if available and requested
    const adjustedAmount = showOccupancy && company?.current_occupancy ?
      amount * company.current_occupancy : amount;

    return `${symbol}${adjustedAmount.toFixed(2)}/${period}`;
  };

  const getBenchmarkTypeColor = (type) => {
    return type === 'budget' ? colors.success : colors.primary;
  };

  const getBenchmarkTypeIcon = (type) => {
    return type === 'budget' ? 'wallet' : 'analytics';
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 17) return 'Good afternoon';
    return 'Good evening';
  };

  if (!company) {
    return (
      <View style={styles.container}>
        <GradientView
          colors={colors.gradientPrimary}
          style={styles.noCompanyGradient}
        >
          <Surface style={styles.noCompanyCard} elevation={8}>
            <Ionicons name="business-outline" size={64} color="#666" />
            <Text variant="headlineSmall" style={styles.noCompanyTitle}>
              No Company Associated
            </Text>
            <Text variant="bodyMedium" style={styles.noCompanyText}>
              Please contact your administrator to be added to a company or create a new account.
            </Text>
            <Button
              mode="contained"
              onPress={() => navigation.navigate('ClerkSignUp')}
              style={styles.noCompanyButton}
              buttonColor={colors.primary}
            >
              Create Company Account
            </Button>
          </Surface>
        </GradientView>
      </View>
    );
  }

  const occupancyPercentage = calculateOccupancyPercentage();

  return (
    <View style={commonStyles.container}>
      {/* Split Layout - Blue section on left, content on right */}
      <View style={styles.mainContent}>
        {/* Left Side - Blue Header Section (Desktop only) */}
        {Platform.OS === 'web' && width > 768 && (
          <View style={styles.leftSide}>
            <LinearGradient
              colors={colors.gradientPrimary}
              style={styles.leftSideGradient}
            >
              <View style={styles.leftSideContent}>
                {/* Logo at Top Left */}
                <View style={styles.logoSection}>
                  <View style={styles.appLogoContainer}>
                    <Image
                      source={require("../../assets/logo.png")}
                      style={styles.appLogo}
                      resizeMode="contain"
                    />
                  </View>
                  {/* App Name in Big Bold Words Under Logo */}
                  <Text style={styles.appNameBig}>CostCompass</Text>
                </View>

                {/* User Greeting */}
                <View style={styles.userGreeting}>
                  <Text variant="headlineLarge" style={styles.leftSideTitle}>
                    {getGreeting()}, {user?.name?.split(' ')[0]}!
                  </Text>
                  <Text variant="bodyMedium" style={styles.companyNameInGreeting}>
                    {company.name}
                  </Text>
                </View>
                <Text variant="bodyLarge" style={styles.leftSideSubtitle}>
                  Here's your cost management overview
                </Text>

                {/* Quick Stats in Left Panel */}
                <View style={styles.leftSideStats}>
                  <View style={styles.leftSideStat}>
                    <Ionicons name="business" size={20} color={colors.textOnPrimary} />
                    <Text style={styles.leftSideStatText}>
                      {company.capacity} {company.capacity_type}
                    </Text>
                  </View>
                  <View style={styles.leftSideStat}>
                    <Ionicons name="people" size={20} color={colors.textOnPrimary} />
                    <Text style={styles.leftSideStatText}>
                      {company.current_occupancy} occupied
                    </Text>
                  </View>
                  <View style={styles.leftSideStat}>
                    <Ionicons name="analytics" size={20} color={colors.textOnPrimary} />
                    <Text style={styles.leftSideStatText}>
                      {dashboardStats.totalBenchmarks} benchmarks
                    </Text>
                  </View>
                </View>
              </View>
            </LinearGradient>
          </View>
        )}

        {/* Right Side - Main Content */}
        <View style={styles.rightSide}>
          {/* Mobile Header (only shown on mobile) */}
          {(Platform.OS !== 'web' || width <= 768) && (
            <LinearGradient
              colors={colors.gradientPrimary}
              style={styles.mobileHeader}
            >
              <View style={styles.mobileHeaderContent}>
                <View style={styles.headerIconContainer}>
                  <Ionicons
                    name="home"
                    size={32}
                    color={colors.textOnPrimary}
                  />
                </View>
                <Text variant="headlineLarge" style={styles.mobileHeaderTitle}>
                  {getGreeting()}, {user?.name?.split(' ')[0]}!
                </Text>
                <Text variant="bodyLarge" style={styles.mobileHeaderSubtitle}>
                  {company.name}
                </Text>
              </View>
            </LinearGradient>
          )}

          <ScrollView
            contentContainerStyle={styles.rightSideScrollContainer}
            style={styles.rightSideScrollView}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
            }
            showsVerticalScrollIndicator={false}
          >



        {/* Occupancy Overview */}
        <TouchableOpacity onPress={navigateToOccupancyDetail} activeOpacity={0.7}>
          <Card style={commonStyles.cardLarge}>
            <Card.Content>
              <View style={styles.occupancyHeader}>
                <Text variant="titleLarge" style={styles.sectionTitle}>
                  Occupancy Overview
                </Text>
                <View style={styles.occupancyHeaderRight}>
                  <Chip
                    icon="trending-up"
                    style={[styles.occupancyChip, { backgroundColor: getOccupancyColor(occupancyPercentage) + '20' }]}
                    textStyle={{ color: getOccupancyColor(occupancyPercentage) }}
                  >
                    {occupancyPercentage}%
                  </Chip>
                  <Ionicons name="chevron-forward" size={20} color="#666" style={styles.chevronIcon} />
                </View>
              </View>

              <View style={styles.occupancyDetails}>
                <View style={styles.occupancyInfo}>
                  <Text variant="headlineMedium" style={[styles.occupancyPercentage, { color: getOccupancyColor(occupancyPercentage) }]}>
                    {occupancyPercentage}%
                  </Text>
                  <Text variant="bodyMedium" style={styles.occupancyText}>
                    {company.current_occupancy} of {company.capacity} {company.capacity_type} occupied
                  </Text>
                </View>
                <View style={styles.occupancyProgress}>
                  <ProgressBar
                    progress={occupancyPercentage / 100}
                    color={getOccupancyColor(occupancyPercentage)}
                    style={styles.progressBar}
                  />
                  <View style={styles.progressLabels}>
                    <Text variant="bodySmall" style={styles.progressLabel}>0</Text>
                    <Text variant="bodySmall" style={styles.progressLabel}>{company.capacity}</Text>
                  </View>
                </View>
              </View>

              <View style={styles.occupancyFooter}>
                <Text variant="bodySmall" style={styles.tapHint}>
                  Tap to manage occupancy
                </Text>
              </View>
            </Card.Content>
          </Card>
        </TouchableOpacity>

        {/* Benchmarks Section */}
        <Card style={commonStyles.cardLarge}>
          <Card.Content>
            <View style={styles.sectionHeader}>
              <View style={styles.sectionTitleContainer}>
                <Text variant="titleLarge" style={styles.sectionTitle}>
                  Cost Benchmarks
                </Text>
                {benchmarks.length > 0 && (
                  <Badge style={styles.benchmarkBadge}>{benchmarks.length}</Badge>
                )}
              </View>
              <View style={styles.headerButtons}>
                <View style={styles.notificationButton}>
                  <IconButton
                    icon="bell"
                    size={20}
                    onPress={() => navigation.navigate('Notifications')}
                  />
                  {unreadNotifications > 0 && (
                    <Badge style={styles.notificationBadge} size={16}>
                      {unreadNotifications > 99 ? '99+' : unreadNotifications}
                    </Badge>
                  )}
                </View>
                <IconButton
                  icon="refresh"
                  size={20}
                  onPress={onRefresh}
                  disabled={refreshing}
                />
              </View>
            </View>

            {loading ? (
              <ActivityIndicator size="large" color={colors.primary} style={styles.loadingIndicator} />
            ) : benchmarks.length === 0 ? (
              <View style={styles.emptyState}>
                <Surface style={styles.emptyStateIcon} elevation={2}>
                  <Ionicons name="analytics-outline" size={48} color={colors.primary} />
                </Surface>
                <Text variant="titleMedium" style={styles.emptyStateTitle}>
                  No Benchmarks Yet
                </Text>
                <Text variant="bodyMedium" style={styles.emptyStateText}>
                  Create your first benchmark to start tracking costs and managing budgets effectively.
                </Text>

                {/* Plan Limits Information */}
                {planLimits && (
                  <View style={styles.planLimitsContainer}>
                    <Text variant="bodySmall" style={styles.planLimitsText}>
                      Plan: {company?.subscription_plan || 'Trial'} • Benchmarks: {planLimits.current}/{planLimits.limit === -1 ? '∞' : planLimits.limit}
                    </Text>
                    {!planLimits.allowed && (
                      <Text variant="bodySmall" style={styles.planLimitsWarning}>
                        ⚠️ Benchmark limit reached
                      </Text>
                    )}
                  </View>
                )}

                {user?.is_admin && (
                  <>
                    <Button
                      mode="contained"
                      onPress={navigateToBenchmarkSetup}
                      style={styles.emptyStateButton}
                      icon="plus"
                      buttonColor={colors.primary}
                    >
                      Create First Benchmark
                    </Button>

                    {__DEV__ && (
                      <>
                        <Button
                          mode="outlined"
                          onPress={testNotification}
                          style={[styles.emptyStateButton, { marginTop: spacing.md }]}
                          icon="bell"
                        >
                          Test Exceeded Notification
                        </Button>
                        <Button
                          mode="outlined"
                          onPress={testOverdueNotification}
                          style={[styles.emptyStateButton, { marginTop: spacing.sm }]}
                          icon="time"
                        >
                          Test Overdue Notification
                        </Button>
                      </>
                    )}
                  </>
                )}
              </View>
            ) : (
              <View style={styles.benchmarksGrid}>
                {benchmarks.map((benchmark) => (
                  <TouchableOpacity
                    key={benchmark.id}
                    onPress={() => navigateToBenchmarkDetail(benchmark)}
                    activeOpacity={0.7}
                    style={styles.benchmarkSmallCard}
                  >
                    <Surface
                      style={styles.benchmarkSmallCardSurface}
                      elevation={2}
                    >
                      <View style={styles.benchmarkSmallContent}>
                        {/* Icon and Type */}
                        <View style={styles.benchmarkSmallHeader}>
                          <Surface
                            style={[
                              styles.benchmarkSmallIcon,
                              { backgroundColor: getBenchmarkTypeColor(benchmark.type) + '20' }
                            ]}
                            elevation={1}
                          >
                            <Ionicons
                              name={getBenchmarkTypeIcon(benchmark.type)}
                              size={16}
                              color={getBenchmarkTypeColor(benchmark.type)}
                            />
                          </Surface>
                          <Text style={[styles.benchmarkSmallType, { color: getBenchmarkTypeColor(benchmark.type) }]}>
                            {benchmark.type.toUpperCase()}
                          </Text>
                        </View>

                        {/* Name */}
                        <Text variant="titleSmall" style={styles.benchmarkSmallName} numberOfLines={2}>
                          {benchmark.name}
                        </Text>

                        {/* Amount */}
                        <Text variant="headlineSmall" style={[styles.benchmarkSmallAmount, { color: getBenchmarkTypeColor(benchmark.type) }]}>
                          {formatAmount(benchmark.amount, benchmark.period, benchmark.currency)}
                        </Text>

                        {/* Period */}
                        <Text variant="bodySmall" style={styles.benchmarkSmallPeriod}>
                          {benchmark.period} {benchmark.type}
                        </Text>
                      </View>
                    </Surface>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </Card.Content>
        </Card>


          </ScrollView>
        </View>
      </View>

      {user?.is_admin && (
        <FAB
          icon="plus"
          style={styles.fab}
          onPress={navigateToBenchmarkSetup}
          label={width > 600 ? "Add Benchmark" : undefined}
          extended={width > 600}
        />
      )}

      <Snackbar
        visible={!!error}
        onDismiss={() => setError(null)}
        action={{ label: 'Retry', onPress: loadData }}
        style={styles.errorSnackbar}
      >
        {error}
      </Snackbar>

      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
        style={styles.successSnackbar}
      >
        {snackbarMessage}
      </Snackbar>
    </View>
  );
};

const styles = StyleSheet.create({
  // Split Layout Styles
  mainContent: {
    flex: 1,
    flexDirection: Platform.OS === 'web' && width > 768 ? 'row' : 'column',
  },
  // Left Side - Blue Section (Desktop only)
  leftSide: {
    flex: 0.4, // Takes 40% of width
    minWidth: 350,
    maxWidth: 450,
  },
  leftSideGradient: {
    flex: 1,
    padding: spacing.lg, // Reduced from spacing.xl
    paddingTop: spacing.md, // Much smaller top padding
    justifyContent: 'flex-start', // Changed from 'center' to start from top
  },
  leftSideContent: {
    alignItems: 'flex-start', // Changed to flex-start for left alignment
    maxWidth: 350,
    alignSelf: 'center',
    width: '100%',
  },
  // Logo Section - Top Left with App Name Below
  logoSection: {
    alignItems: 'flex-start',
    alignSelf: 'flex-start',
    marginBottom: spacing.xl,
    paddingBottom: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.2)',
    width: '100%',
  },
  appLogoContainer: {
    width: '100%', // Full width of the blue sidebar content area
    height: 120, // Taller to accommodate larger logo
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.lg, // Bottom margin for vertical layout
    marginTop: spacing.sm,
    // Remove artificial shadows and borders for more natural integration
  },
  appLogo: {
    width: 280, // Almost full width of the blue sidebar content
    height: 100, // Proportional height
    // Remove tintColor to show original logo colors
  },
  appName: {
    color: colors.textOnPrimary,
    fontSize: 20,
    fontWeight: typography.fontWeights.bold,
    textAlign: 'left',
    textShadowColor: 'rgba(0,0,0,0.2)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  // Big Bold App Name Under Logo
  appNameBig: {
    color: colors.textOnPrimary,
    fontSize: 32, // Big bold text
    fontWeight: typography.fontWeights.bold,
    textAlign: 'left',
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
    letterSpacing: 0.5,
  },
  // User Greeting Section
  userGreeting: {
    alignItems: 'flex-start', // Left aligned
    marginBottom: spacing.lg,
    width: '100%',
  },
  companyNameInGreeting: {
    color: colors.textOnPrimary,
    fontSize: 14,
    opacity: 0.8,
    marginTop: spacing.xs,
    textShadowColor: 'rgba(0,0,0,0.1)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 1,
  },
  leftSideTitle: {
    color: colors.textOnPrimary,
    fontSize: 28,
    fontWeight: typography.fontWeights.bold,
    textAlign: 'left', // Changed to left align
    marginBottom: spacing.sm, // Reduced margin
    textShadowColor: 'rgba(0,0,0,0.1)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  leftSideSubtitle: {
    color: colors.textOnPrimary,
    fontSize: 16,
    textAlign: 'left', // Changed to left align
    opacity: 0.95,
    marginBottom: spacing.lg,
    textShadowColor: 'rgba(0,0,0,0.1)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  leftSideStats: {
    marginTop: spacing.xl,
    alignSelf: 'stretch',
  },
  leftSideStat: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    padding: spacing.md,
    borderRadius: borderRadius.lg,
  },
  leftSideStatText: {
    color: colors.textOnPrimary,
    fontSize: 14,
    fontWeight: '500',
    marginLeft: spacing.sm,
  },
  // Right Side - Main Content
  rightSide: {
    flex: 1.6, // Takes 60% of width
    backgroundColor: colors.background,
  },
  rightSideScrollContainer: {
    padding: spacing.lg,
    paddingBottom: spacing.xxxl,
  },
  rightSideScrollView: {
    flex: 1,
  },
  // Mobile Header (when left side is hidden)
  mobileHeader: {
    paddingTop: spacing.xxxxl,
    paddingBottom: spacing.xl,
    paddingHorizontal: spacing.xl,
    borderBottomLeftRadius: borderRadius.xxl,
    borderBottomRightRadius: borderRadius.xxl,
  },
  mobileHeaderContent: {
    alignItems: 'center',
  },
  mobileHeaderTitle: {
    color: colors.textOnPrimary,
    fontSize: 28,
    fontWeight: typography.fontWeights.bold,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  mobileHeaderSubtitle: {
    color: colors.textOnPrimary,
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.95,
  },
  headerIconContainer: {
    width: 50, // Reduced from 80
    height: 50, // Reduced from 80
    borderRadius: 25, // Reduced accordingly
    backgroundColor: 'rgba(255, 255, 255, 0.2)', // Semi-transparent for left panel
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.sm, // Reduced margin
    elevation: elevation.sm, // Reduced elevation
    shadowColor: 'rgba(0, 0, 0, 0.3)',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  scrollView: {
    marginTop: -spacing.lg,
  },
  companyBadge: {
    backgroundColor: colors.surface + '40',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.xl,
    marginTop: spacing.md,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  companyBadgeText: {
    color: colors.textOnPrimary,
    fontWeight: typography.fontWeights.bold,
    fontSize: typography.fontSizes.sm,
  },
  statsGrid: {
    flexDirection: 'row',
    paddingHorizontal: spacing.xl,
    marginTop: -spacing.xxl,
    gap: spacing.md,
    marginBottom: spacing.xl,
  },
  statCard: {
    flex: 1,
    backgroundColor: colors.surface,
    borderRadius: borderRadius.xl,
    elevation: elevation.md,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  statContent: {
    padding: spacing.xl,
    alignItems: 'center',
  },
  statHeader: {
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  statIcon: {
    width: 56,
    height: 56,
    borderRadius: borderRadius.round,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  statValue: {
    fontWeight: typography.fontWeights.bold,
    color: colors.text,
    fontSize: typography.fontSizes.xxl,
    lineHeight: typography.fontSizes.xxl * typography.lineHeights.tight,
  },
  statLabel: {
    color: colors.textSecondary,
    textAlign: 'center',
    fontSize: typography.fontSizes.md,
    lineHeight: typography.fontSizes.md * typography.lineHeights.normal,
  },
  occupancyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  occupancyHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  occupancyChip: {
    borderRadius: borderRadius.xl,
  },
  chevronIcon: {
    marginLeft: spacing.xs,
  },
  occupancyFooter: {
    marginTop: spacing.lg,
    paddingTop: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: colors.borderLight,
    alignItems: 'center',
  },
  tapHint: {
    color: colors.textSecondary,
    fontStyle: 'italic',
    fontSize: typography.fontSizes.sm,
  },
  occupancyDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.lg,
  },
  occupancyInfo: {
    flex: 1,
  },
  occupancyPercentage: {
    fontWeight: typography.fontWeights.bold,
    marginBottom: spacing.xs,
    fontSize: typography.fontSizes.lg,
    color: colors.text,
  },
  occupancyText: {
    color: colors.textSecondary,
    fontSize: typography.fontSizes.sm,
  },
  occupancyProgress: {
    flex: 2,
  },
  progressBar: {
    height: 8,
    borderRadius: borderRadius.xs,
  },
  progressLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: spacing.xs,
  },
  progressLabel: {
    color: colors.textSecondary,
    fontSize: typography.fontSizes.xs,
  },
  sectionTitle: {
    color: colors.text,
    fontWeight: typography.fontWeights.semibold,
    marginBottom: spacing.sm,
    fontSize: typography.fontSizes.xl,
    lineHeight: typography.fontSizes.xl * typography.lineHeights.tight,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  benchmarkBadge: {
    backgroundColor: colors.primary,
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  notificationButton: {
    position: 'relative',
  },
  notificationBadge: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: colors.error,
    color: colors.textOnPrimary,
    fontSize: 10,
    minWidth: 16,
    height: 16,
    borderRadius: 8,
    zIndex: 1,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: spacing.xxxl,
  },
  emptyStateIcon: {
    width: 80,
    height: 80,
    borderRadius: borderRadius.round,
    backgroundColor: colors.surfaceVariant,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
    elevation: elevation.xs,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  emptyStateTitle: {
    color: colors.text,
    marginBottom: spacing.sm,
    fontWeight: typography.fontWeights.bold,
    fontSize: typography.fontSizes.lg,
  },
  emptyStateText: {
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.xl,
    lineHeight: typography.fontSizes.md * typography.lineHeights.relaxed,
    fontSize: typography.fontSizes.md,
  },
  emptyStateButton: {
    borderRadius: borderRadius.lg,
  },
  planLimitsContainer: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.md,
    padding: spacing.sm,
    marginBottom: spacing.md,
    borderWidth: 1,
    borderColor: colors.outline,
  },
  planLimitsText: {
    textAlign: 'center',
    color: colors.onSurface,
    opacity: 0.8,
  },
  planLimitsWarning: {
    textAlign: 'center',
    color: colors.error,
    marginTop: spacing.xs,
    fontWeight: 'bold',
  },
  benchmarksList: {
    gap: spacing.md,
  },
  // Small Benchmark Cards Grid
  benchmarksGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.md,
    justifyContent: 'space-between',
  },
  benchmarkSmallCard: {
    width: '48%', // Two cards per row with gap
    minWidth: 150,
  },
  benchmarkSmallCardSurface: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.lg,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  benchmarkSmallContent: {
    padding: spacing.md,
    alignItems: 'flex-start',
  },
  benchmarkSmallHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
    width: '100%',
    justifyContent: 'space-between',
  },
  benchmarkSmallIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  benchmarkSmallType: {
    fontSize: 10,
    fontWeight: typography.fontWeights.bold,
    letterSpacing: 0.5,
  },
  benchmarkSmallName: {
    fontWeight: typography.fontWeights.semibold,
    color: colors.text,
    marginBottom: spacing.sm,
    lineHeight: 18,
  },
  benchmarkSmallAmount: {
    fontWeight: typography.fontWeights.bold,
    fontSize: 18,
    marginBottom: spacing.xs,
  },
  benchmarkSmallPeriod: {
    color: colors.textSecondary,
    fontSize: 12,
  },
  benchmarkCard: {
    borderRadius: borderRadius.lg,
    backgroundColor: colors.surface,
    elevation: elevation.sm,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 4,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  benchmarkCardInner: {
    backgroundColor: 'transparent',
    elevation: 0,
  },
  benchmarkContent: {
    padding: spacing.md,
  },
  benchmarkHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  benchmarkTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: spacing.md,
  },
  benchmarkIcon: {
    width: 36,
    height: 36,
    borderRadius: borderRadius.round,
    justifyContent: 'center',
    alignItems: 'center',
  },
  benchmarkTitleText: {
    flex: 1,
  },
  benchmarkName: {
    fontWeight: typography.fontWeights.bold,
    color: colors.text,
    marginBottom: spacing.xs,
    fontSize: typography.fontSizes.md,
  },
  benchmarkPeriod: {
    color: colors.textSecondary,
    textTransform: 'capitalize',
    fontSize: typography.fontSizes.sm,
  },
  benchmarkTypeChip: {
    borderRadius: borderRadius.lg,
  },
  benchmarkAmount: {
    marginBottom: spacing.md,
  },
  benchmarkAmountText: {
    fontWeight: typography.fontWeights.bold,
    marginBottom: spacing.xs,
    fontSize: typography.fontSizes.lg,
  },
  benchmarkDate: {
    color: colors.textSecondary,
    fontSize: typography.fontSizes.sm,
  },
  benchmarkActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  quickActions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.md,
  },
  quickAction: {
    flex: 1,
    minWidth: 100,
    borderRadius: borderRadius.lg,
    backgroundColor: colors.surfaceVariant,
    elevation: elevation.xs,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  quickActionContent: {
    padding: spacing.md,
  },
  quickActionInner: {
    alignItems: 'center',
    gap: spacing.sm,
  },
  quickActionText: {
    fontSize: typography.fontSizes.sm,
    color: colors.text,
    fontWeight: typography.fontWeights.medium,
  },
  fab: {
    position: 'absolute',
    margin: spacing.xl,
    right: 0,
    bottom: 0,
    backgroundColor: colors.primary,
    borderRadius: borderRadius.round,
    elevation: elevation.lg,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
  },
  noCompanyGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.xl,
  },
  noCompanyCard: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.xl,
    padding: spacing.xxl,
    alignItems: 'center',
    maxWidth: 400,
    width: '100%',
    elevation: elevation.md,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  noCompanyTitle: {
    textAlign: 'center',
    color: colors.text,
    marginTop: spacing.md,
    marginBottom: spacing.md,
    fontWeight: typography.fontWeights.bold,
    fontSize: typography.fontSizes.xl,
  },
  noCompanyText: {
    textAlign: 'center',
    color: colors.textSecondary,
    marginBottom: spacing.xl,
    lineHeight: typography.fontSizes.md * typography.lineHeights.relaxed,
    fontSize: typography.fontSizes.md,
  },
  noCompanyButton: {
    borderRadius: borderRadius.lg,
  },
  loadingIndicator: {
    marginVertical: spacing.xxxl,
  },
  errorSnackbar: {
    backgroundColor: colors.error,
  },
  successSnackbar: {
    backgroundColor: colors.success,
  },
});

export default HomeScreen;
