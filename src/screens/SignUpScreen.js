import React from 'react';
import { View, StyleSheet, Text, TouchableOpacity, Alert } from 'react-native';
import { useSignUp } from '@clerk/clerk-expo';
import { TextInput, Button } from 'react-native-paper';

export default function SignUpScreen({ navigation }) {
  const { signUp, setActive, isLoaded } = useSignUp();
  const [email, setEmail] = React.useState('');
  const [password, setPassword] = React.useState('');
  const [firstName, setFirstName] = React.useState('');
  const [lastName, setLastName] = React.useState('');
  const [verificationCode, setVerificationCode] = React.useState('');
  const [pendingVerification, setPendingVerification] = React.useState(false);
  const [loading, setLoading] = React.useState(false);

  const onSignUpPress = async () => {
    if (!isLoaded) return;

    setLoading(true);
    try {
      await signUp.create({
        emailAddress: email,
        password,
        firstName,
        lastName,
      });

      await signUp.prepareEmailAddressVerification({ strategy: 'email_code' });
      setPendingVerification(true);
      console.log('✅ Verification email sent');
    } catch (err) {
      console.error('❌ Sign up error:', err);
      alert(err.errors?.[0]?.message || 'Sign up failed');
    } finally {
      setLoading(false);
    }
  };

  const onVerifyPress = async () => {
    if (!isLoaded) return;

    setLoading(true);
    try {
      console.log('🔄 Attempting verification with code:', verificationCode);

      const completeSignUp = await signUp.attemptEmailAddressVerification({
        code: verificationCode,
      });

      console.log('✅ Verification result:', completeSignUp.status);

      if (completeSignUp.status === 'complete') {
        await setActive({ session: completeSignUp.createdSessionId });
        console.log('✅ Email verified and signed up successfully');
        // Navigation will happen automatically via AppNavigator
      } else {
        console.log('⚠️ Verification incomplete, status:', completeSignUp.status);
        alert('Verification incomplete. Please try again.');
      }
    } catch (err) {
      console.error('❌ Verification error:', err);
      alert(err.errors?.[0]?.message || 'Verification failed');
    } finally {
      setLoading(false);
    }
  };

  const onResendPress = async () => {
    if (!isLoaded) return;

    setLoading(true);
    try {
      console.log('🔄 Resending verification code...');
      await signUp.prepareEmailAddressVerification({ strategy: 'email_code' });
      alert('Verification code sent! Check your email.');
      console.log('✅ Verification code resent');
    } catch (err) {
      console.error('❌ Resend error:', err);
      alert(err.errors?.[0]?.message || 'Failed to resend code');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>
          {pendingVerification ? 'Verify Your Email' : 'Create Account'}
        </Text>
        <Text style={styles.subtitle}>
          {pendingVerification 
            ? 'Enter the verification code sent to your email'
            : 'Start your free trial with CostCompass'
          }
        </Text>
      </View>

      <View style={styles.form}>
        {!pendingVerification ? (
          <>
            <TextInput
              label="First Name"
              value={firstName}
              onChangeText={setFirstName}
              mode="outlined"
              style={styles.input}
            />

            <TextInput
              label="Last Name"
              value={lastName}
              onChangeText={setLastName}
              mode="outlined"
              style={styles.input}
            />

            <TextInput
              label="Email"
              value={email}
              onChangeText={setEmail}
              mode="outlined"
              keyboardType="email-address"
              autoCapitalize="none"
              style={styles.input}
            />

            <TextInput
              label="Password"
              value={password}
              onChangeText={setPassword}
              mode="outlined"
              secureTextEntry
              style={styles.input}
            />

            <Button
              mode="contained"
              onPress={onSignUpPress}
              loading={loading}
              disabled={loading || !email || !password || !firstName || !lastName}
              style={styles.signUpButton}
            >
              Create Account
            </Button>
          </>
        ) : (
          <>
            <TextInput
              label="Verification Code"
              value={verificationCode}
              onChangeText={setVerificationCode}
              mode="outlined"
              keyboardType="number-pad"
              style={styles.input}
            />

            <Button
              mode="contained"
              onPress={onVerifyPress}
              loading={loading}
              disabled={loading || !verificationCode}
              style={styles.signUpButton}
            >
              Verify Email
            </Button>

            <Button
              mode="outlined"
              onPress={onResendPress}
              loading={loading}
              disabled={loading}
              style={styles.resendButton}
            >
              Resend Code
            </Button>

            <TouchableOpacity
              onPress={() => setPendingVerification(false)}
              style={styles.goBackButton}
            >
              <Text style={styles.goBackText}>← Change Email Address</Text>
            </TouchableOpacity>
          </>
        )}

        <View style={styles.footer}>
          <Text style={styles.footerText}>Already have an account? </Text>
          <TouchableOpacity onPress={() => navigation.navigate('SignIn')}>
            <Text style={styles.linkText}>Sign in</Text>
          </TouchableOpacity>
        </View>

        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Text style={styles.backText}>← Back to landing page</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginTop: 60,
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  form: {
    flex: 1,
  },
  input: {
    marginBottom: 16,
  },
  signUpButton: {
    marginTop: 20,
    paddingVertical: 8,
  },
  resendButton: {
    marginTop: 12,
    paddingVertical: 8,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 30,
  },
  footerText: {
    color: '#666',
  },
  linkText: {
    color: '#2196F3',
    fontWeight: 'bold',
  },
  backText: {
    color: '#666',
    textAlign: 'center',
    marginTop: 20,
  },
  goBackButton: {
    marginTop: 20,
    alignItems: 'center',
  },
  goBackText: {
    color: '#666',
    fontSize: 14,
  },
});
