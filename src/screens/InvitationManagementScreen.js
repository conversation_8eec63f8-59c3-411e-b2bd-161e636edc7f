import React, { useState, useEffect } from "react";
import { View, StyleSheet, ScrollView, Alert, Platform, Dimensions, Image } from "react-native";
import {
  Text,
  Card,
  Button,
  TextInput,
  ActivityIndicator,
  List,
  Chip,
  Dialog,
  Portal,
  Divider,
  IconButton,
} from "react-native-paper";
import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from 'expo-linear-gradient';
import { useClerkAuth } from "../contexts/ClerkAuthContext";
import DatabaseService from "../services/database";
import {
  colors,
  spacing,
  borderRadius,
  commonStyles,
  typography,
  elevation,
} from "../styles/theme";

const { width } = Dimensions.get("window");

const InvitationManagementScreen = () => {
  const { user, company } = useClerkAuth();
  const [invitations, setInvitations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [inviteDialogVisible, setInviteDialogVisible] = useState(false);
  const [inviteEmail, setInviteEmail] = useState("");
  const [inviteLoading, setInviteLoading] = useState(false);

  useEffect(() => {
    loadInvitations();
  }, []);

  const loadInvitations = async () => {
    try {
      setLoading(true);
      const companyInvitations = await DatabaseService.getCompanyInvitations(
        company.id,
      );
      setInvitations(companyInvitations);
    } catch (error) {
      console.error("Error loading invitations:", error);
      Alert.alert("Error", "Failed to load invitations");
    } finally {
      setLoading(false);
    }
  };

  const handleSendInvitation = async () => {
    if (!inviteEmail.trim()) {
      Alert.alert("Error", "Please enter an email address");
      return;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(inviteEmail)) {
      Alert.alert("Error", "Please enter a valid email address");
      return;
    }

    try {
      setInviteLoading(true);

      // Check if user exists
      const existingUser = await DatabaseService.getUserByEmail(inviteEmail);
      if (!existingUser) {
        Alert.alert(
          "User Not Found",
          "No account found with this email address. The user must create an account first before they can be invited to join your company.",
        );
        return;
      }

      // Check if user is already in this company
      const userCompanies = await DatabaseService.getUserCompanies(
        existingUser.id,
      );
      const alreadyInCompany = userCompanies.some(
        (comp) => comp.id === company.id,
      );

      if (alreadyInCompany) {
        Alert.alert("Error", "This user is already a member of your company");
        return;
      }

      // Create invitation
      await DatabaseService.createInvitation({
        email: inviteEmail,
        companyId: company.id,
        invitedBy: user.id,
        permissions: [],
      });

      Alert.alert(
        "Invitation Sent",
        `An invitation has been sent to ${inviteEmail}. They will be able to access your company once they accept the invitation.`,
      );

      setInviteDialogVisible(false);
      setInviteEmail("");
      loadInvitations();
    } catch (error) {
      console.error("Error sending invitation:", error);
      Alert.alert("Error", "Failed to send invitation. Please try again.");
    } finally {
      setInviteLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "pending":
        return colors.warning;
      case "accepted":
        return colors.success;
      case "expired":
        return colors.error;
      default:
        return colors.textSecondary;
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case "pending":
        return "clock-outline";
      case "accepted":
        return "check-circle";
      case "expired":
        return "close-circle";
      default:
        return "help-circle";
    }
  };

  if (loading) {
    return (
      <View style={commonStyles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text variant="bodyLarge" style={styles.loadingText}>
            Loading invitations...
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={commonStyles.container}>
      {/* Split Layout - Blue section on left, content on right */}
      <View style={styles.mainContent}>
        {/* Left Side - Blue Header Section (Desktop only) */}
        {Platform.OS === 'web' && width > 600 && (
          <View style={styles.leftSide}>
            <LinearGradient
              colors={colors.gradientPrimary}
              style={styles.leftSideGradient}
            >
              <View style={styles.leftSideContent}>
                {/* App Logo and Name */}
                <View style={styles.logoSection}>
                  <View style={styles.appLogoContainer}>
                    <Image
                      source={require("../../assets/logo.png")}
                      style={styles.appLogo}
                      resizeMode="contain"
                    />
                  </View>
                  <Text style={styles.appNameBig}>CostCompass</Text>
                </View>

                {/* Page Header */}
                <View style={styles.pageHeader}>
                  <View style={styles.headerIconContainer}>
                    <Ionicons
                      name="mail"
                      size={32}
                      color={colors.textOnPrimary}
                    />
                  </View>
                  <Text style={styles.leftSideTitle}>
                    User Invitations
                  </Text>
                  <Text style={styles.leftSideSubtitle}>
                    Invite existing users to join {company?.name}
                  </Text>
                </View>

                {/* Company Info */}
                <View style={styles.leftSideInfo}>
                  <View style={styles.leftSideInfoItem}>
                    <Ionicons name="business" size={20} color={colors.textOnPrimary} />
                    <Text style={styles.leftSideInfoText}>
                      {company?.name}
                    </Text>
                  </View>
                  <View style={styles.leftSideInfoItem}>
                    <Ionicons name="people" size={20} color={colors.textOnPrimary} />
                    <Text style={styles.leftSideInfoText}>
                      {invitations.length} invitations sent
                    </Text>
                  </View>
                </View>
              </View>
            </LinearGradient>
          </View>
        )}

        {/* Right Side - Main Content */}
        <View style={styles.rightSide}>
          {/* Mobile Header (only shown on mobile) */}
          {(Platform.OS !== 'web' || width <= 600) && (
            <LinearGradient
              colors={colors.gradientPrimary}
              style={styles.mobileHeader}
            >
              <View style={styles.mobileHeaderContent}>
                <View style={styles.headerIconContainer}>
                  <Ionicons
                    name="mail"
                    size={32}
                    color={colors.textOnPrimary}
                  />
                </View>
                <Text style={styles.mobileHeaderTitle}>
                  User Invitations
                </Text>
                <Text style={styles.mobileHeaderSubtitle}>
                  Invite existing users to join {company?.name}
                </Text>
              </View>
            </LinearGradient>
          )}

          <ScrollView
            contentContainerStyle={styles.rightSideScrollContainer}
            style={styles.rightSideScrollView}
            showsVerticalScrollIndicator={false}
          >
        <Card style={commonStyles.cardLarge}>
          <Card.Content>
            <View style={styles.headerContent}>
              <Text variant="titleMedium" style={commonStyles.sectionTitle}>
                Recent Invitations
              </Text>
              <Button
                mode="contained"
                onPress={() => setInviteDialogVisible(true)}
                icon="plus"
                style={commonStyles.buttonPrimary}
              >
                Invite User
              </Button>
            </View>

            {invitations.length === 0 ? (
              <View style={commonStyles.emptyState}>
                <View style={commonStyles.emptyStateIcon}>
                  <Ionicons
                    name="mail-outline"
                    size={48}
                    color={colors.textLight}
                  />
                </View>
                <Text style={commonStyles.emptyStateTitle}>
                  No invitations sent yet
                </Text>
                <Text style={commonStyles.emptyStateText}>
                  Invite existing users to join your company
                </Text>
              </View>
            ) : (
              invitations.map((invitation) => (
                <View key={invitation.id} style={commonStyles.listItem}>
                  <View style={styles.invitationInfo}>
                    <Text variant="titleMedium" style={styles.invitationEmail}>
                      {invitation.email}
                    </Text>
                    <Text variant="bodySmall" style={styles.invitationDate}>
                      Sent{" "}
                      {new Date(invitation.created_at).toLocaleDateString()}
                    </Text>
                    {invitation.expires_at && (
                      <Text variant="bodySmall" style={styles.invitationExpiry}>
                        Expires{" "}
                        {new Date(invitation.expires_at).toLocaleDateString()}
                      </Text>
                    )}
                  </View>
                  <Chip
                    icon={getStatusIcon(invitation.status)}
                    style={[
                      styles.statusChip,
                      {
                        backgroundColor:
                          getStatusColor(invitation.status) + "20",
                      },
                    ]}
                    textStyle={{ color: getStatusColor(invitation.status) }}
                  >
                    {invitation.status.charAt(0).toUpperCase() +
                      invitation.status.slice(1)}
                  </Chip>
                </View>
              ))
            )}
          </Card.Content>
        </Card>

        <Card style={commonStyles.cardLarge}>
          <Card.Content>
            <Text variant="titleMedium" style={commonStyles.sectionTitle}>
              How Invitations Work
            </Text>
            <Text variant="bodyMedium" style={styles.infoText}>
              • Users must have an existing account before they can be invited
              {"\n"}• Invitations are valid for 7 days{"\n"}• Users will be
              added to your company once they accept{"\n"}• Invited users will
              have regular user permissions (not admin)
            </Text>
          </Card.Content>
        </Card>

        {/* Invite User Dialog */}
        <Portal>
          <Dialog
            visible={inviteDialogVisible}
            onDismiss={() => setInviteDialogVisible(false)}
          >
            <Dialog.Title>Invite User to Company</Dialog.Title>
            <Dialog.Content>
              <Text variant="bodyMedium" style={styles.dialogDescription}>
                Enter the email address of an existing user to invite them to
                join {company?.name}.
              </Text>
              <TextInput
                label="Email Address"
                value={inviteEmail}
                onChangeText={setInviteEmail}
                mode="outlined"
                keyboardType="email-address"
                autoCapitalize="none"
                style={commonStyles.input}
                disabled={inviteLoading}
              />
            </Dialog.Content>
            <Dialog.Actions>
              <Button
                onPress={() => setInviteDialogVisible(false)}
                disabled={inviteLoading}
              >
                Cancel
              </Button>
              <Button
                onPress={handleSendInvitation}
                disabled={inviteLoading}
                loading={inviteLoading}
              >
                Send Invitation
              </Button>
            </Dialog.Actions>
          </Dialog>
        </Portal>
          </ScrollView>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  // Split Layout Styles
  mainContent: {
    flex: 1,
    flexDirection: Platform.OS === 'web' && width > 600 ? 'row' : 'column',
  },
  // Left Side - Blue Section (Desktop only)
  leftSide: {
    flex: 0.4,
    minWidth: 350,
    maxWidth: 450,
  },
  leftSideGradient: {
    flex: 1,
    padding: spacing.lg,
    paddingTop: spacing.md,
    justifyContent: 'flex-start',
  },
  leftSideContent: {
    alignItems: 'flex-start',
    maxWidth: 350,
    alignSelf: 'center',
    width: '100%',
  },
  // Logo Section
  logoSection: {
    alignItems: 'flex-start',
    alignSelf: 'flex-start',
    marginBottom: spacing.xl,
    paddingBottom: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.2)',
    width: '100%',
  },
  appLogoContainer: {
    width: '100%', // Full width of the blue sidebar content area
    height: 120, // Taller to accommodate larger logo
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.lg,
    marginTop: spacing.sm,
    // Remove artificial shadows and borders for more natural integration
  },
  appLogo: {
    width: 280, // Almost full width of the blue sidebar content
    height: 100, // Proportional height
    // Remove tintColor to show original logo colors
  },
  appNameBig: {
    color: colors.textOnPrimary,
    fontSize: 32,
    fontWeight: typography.fontWeights.bold,
    textAlign: 'left',
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
    letterSpacing: 0.5,
  },
  // Page Header Section
  pageHeader: {
    alignItems: 'flex-start',
    marginBottom: spacing.lg,
    width: '100%',
  },
  leftSideTitle: {
    color: colors.textOnPrimary,
    fontSize: 24,
    fontWeight: typography.fontWeights.bold,
    textAlign: 'left',
    marginBottom: spacing.sm,
    textShadowColor: 'rgba(0,0,0,0.1)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  leftSideSubtitle: {
    color: colors.textOnPrimary,
    fontSize: 16,
    textAlign: 'left',
    opacity: 0.95,
    marginBottom: spacing.lg,
    textShadowColor: 'rgba(0,0,0,0.1)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  leftSideInfo: {
    marginTop: spacing.lg,
    alignSelf: 'stretch',
  },
  leftSideInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    padding: spacing.md,
    borderRadius: borderRadius.lg,
  },
  leftSideInfoText: {
    color: colors.textOnPrimary,
    fontSize: 14,
    fontWeight: '500',
    marginLeft: spacing.sm,
    flex: 1,
  },
  // Right Side - Main Content
  rightSide: {
    flex: 1.6,
    backgroundColor: colors.background,
  },
  rightSideScrollContainer: {
    padding: spacing.lg,
    paddingBottom: spacing.xxxl,
  },
  rightSideScrollView: {
    flex: 1,
  },
  // Mobile Header
  mobileHeader: {
    paddingTop: spacing.xxxxl,
    paddingBottom: spacing.xl,
    paddingHorizontal: spacing.xl,
    borderBottomLeftRadius: borderRadius.xxl,
    borderBottomRightRadius: borderRadius.xxl,
  },
  mobileHeaderContent: {
    alignItems: 'center',
  },
  mobileHeaderTitle: {
    color: colors.textOnPrimary,
    fontSize: 24,
    fontWeight: typography.fontWeights.bold,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  mobileHeaderSubtitle: {
    color: colors.textOnPrimary,
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.95,
  },
  scrollView: {
    marginTop: -spacing.lg,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: spacing.md,
    color: colors.textSecondary,
  },
  headerContent: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.md,
  },
  invitationInfo: {
    flex: 1,
  },
  invitationEmail: {
    fontWeight: typography.fontWeights.bold,
    color: colors.text,
  },
  invitationDate: {
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  invitationExpiry: {
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  statusChip: {
    marginLeft: spacing.sm,
  },
  infoText: {
    color: colors.textSecondary,
    lineHeight: 20,
  },
  dialogDescription: {
    marginBottom: spacing.md,
    color: colors.textSecondary,
  },
  headerIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
    elevation: elevation.sm,
    shadowColor: 'rgba(0, 0, 0, 0.3)',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
});

export default InvitationManagementScreen;
