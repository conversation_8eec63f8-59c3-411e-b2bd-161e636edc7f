import React, { useState, useEffect } from "react";
import { View, StyleSheet, ScrollView, Alert } from "react-native";
import {
  Text,
  Card,
  TextInput,
  Button,
  SegmentedButtons,
  ActivityIndicator,
  Divider,
  List,
} from "react-native-paper";
import { useAuth } from "../contexts/AuthContext";

const CompanyManagementScreen = () => {
  const { company, updateCompany } = useAuth();
  const [loading, setLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  // Form state
  const [companyName, setCompanyName] = useState("");
  const [registration, setRegistration] = useState("");
  const [capacity, setCapacity] = useState("");
  const [currentOccupancy, setCurrentOccupancy] = useState("");
  const [capacityType, setCapacityType] = useState("beds");

  useEffect(() => {
    if (company) {
      setCompanyName(company.name);
      setRegistration(company.registration || "");
      setCapacity(company.capacity.toString());
      setCurrentOccupancy(company.current_occupancy.toString());
      setCapacityType(company.capacity_type);
    }
  }, [company]);

  const validateForm = () => {
    if (!companyName.trim()) {
      Alert.alert("Error", "Please enter a company name");
      return false;
    }

    if (!capacity.trim()) {
      Alert.alert("Error", "Please enter the capacity");
      return false;
    }

    const numCapacity = parseInt(capacity);
    if (isNaN(numCapacity) || numCapacity <= 0) {
      Alert.alert("Error", "Please enter a valid capacity number");
      return false;
    }

    if (
      currentOccupancy &&
      (isNaN(parseInt(currentOccupancy)) || parseInt(currentOccupancy) < 0)
    ) {
      Alert.alert("Error", "Please enter a valid occupancy number");
      return false;
    }

    if (currentOccupancy && parseInt(currentOccupancy) > parseInt(capacity)) {
      Alert.alert("Error", "Current occupancy cannot exceed capacity");
      return false;
    }

    return true;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    setLoading(true);

    try {
      const updateData = {
        name: companyName.trim(),
        registration: registration.trim(),
        capacity: parseInt(capacity),
        currentOccupancy: parseInt(currentOccupancy) || 0,
        capacityType,
      };

      const result = await updateCompany(updateData);

      if (result.success) {
        setIsEditing(false);
        Alert.alert("Success", "Company details updated successfully!");
      } else {
        Alert.alert(
          "Error",
          result.error || "Failed to update company details",
        );
      }
    } catch (error) {
      console.error("Error updating company:", error);
      Alert.alert(
        "Error",
        "Failed to update company details. Please try again.",
      );
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    if (company) {
      setCompanyName(company.name);
      setRegistration(company.registration || "");
      setCapacity(company.capacity.toString());
      setCurrentOccupancy(company.current_occupancy.toString());
      setCapacityType(company.capacity_type);
    }
    setIsEditing(false);
  };

  const calculateOccupancyPercentage = () => {
    if (!company || company.capacity === 0) return 0;
    return Math.round((company.current_occupancy / company.capacity) * 100);
  };

  if (!company) {
    return (
      <View style={styles.container}>
        <Card style={styles.card}>
          <Card.Content>
            <Text variant="headlineSmall">No Company Data</Text>
            <Text variant="bodyMedium">No company information available.</Text>
          </Card.Content>
        </Card>
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.scrollContent}
    >
      {/* Company Overview Card */}
      <Card style={styles.card}>
        <Card.Content>
          <View style={styles.header}>
            <Text variant="headlineSmall" style={styles.title}>
              Company Information
            </Text>
            {!isEditing && (
              <Button
                mode="outlined"
                onPress={() => setIsEditing(true)}
                compact
              >
                Edit
              </Button>
            )}
          </View>

          <Divider style={styles.divider} />

          {!isEditing ? (
            <>
              <List.Item
                title="Company Name"
                description={company.name}
                left={(props) => <List.Icon {...props} icon="domain" />}
              />

              <List.Item
                title="Account Number"
                description={company.account_number}
                left={(props) => <List.Icon {...props} icon="identifier" />}
              />

              {company.registration && (
                <List.Item
                  title="Registration"
                  description={company.registration}
                  left={(props) => <List.Icon {...props} icon="certificate" />}
                />
              )}

              <List.Item
                title="Capacity"
                description={`${company.capacity} ${company.capacity_type}`}
                left={(props) => <List.Icon {...props} icon="home-group" />}
              />

              <List.Item
                title="Current Occupancy"
                description={`${company.current_occupancy} ${company.capacity_type} (${calculateOccupancyPercentage()}%)`}
                left={(props) => <List.Icon {...props} icon="account-group" />}
              />
            </>
          ) : (
            <>
              <TextInput
                label="Company Name *"
                value={companyName}
                onChangeText={setCompanyName}
                mode="outlined"
                style={styles.input}
                disabled={loading}
              />

              <TextInput
                label="Company Registration"
                value={registration}
                onChangeText={setRegistration}
                mode="outlined"
                style={styles.input}
                disabled={loading}
              />

              <Text variant="bodyMedium" style={styles.label}>
                Capacity Type *
              </Text>
              <SegmentedButtons
                value={capacityType}
                onValueChange={setCapacityType}
                buttons={[
                  { value: "beds", label: "Beds" },
                  { value: "rooms", label: "Rooms" },
                  { value: "persons", label: "Persons" },
                ]}
                style={styles.segmentedButtons}
              />

              <TextInput
                label={`Total ${capacityType.charAt(0).toUpperCase() + capacityType.slice(1)} *`}
                value={capacity}
                onChangeText={setCapacity}
                mode="outlined"
                keyboardType="numeric"
                style={styles.input}
                disabled={loading}
              />

              <TextInput
                label={`Current Occupancy`}
                value={currentOccupancy}
                onChangeText={setCurrentOccupancy}
                mode="outlined"
                keyboardType="numeric"
                style={styles.input}
                disabled={loading}
              />

              <View style={styles.actions}>
                <Button
                  mode="outlined"
                  onPress={handleCancel}
                  style={styles.cancelButton}
                  disabled={loading}
                >
                  Cancel
                </Button>
                <Button
                  mode="contained"
                  onPress={handleSave}
                  style={styles.saveButton}
                  disabled={loading}
                >
                  {loading ? (
                    <ActivityIndicator color="white" />
                  ) : (
                    "Save Changes"
                  )}
                </Button>
              </View>
            </>
          )}
        </Card.Content>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 20,
  },
  card: {
    margin: 16,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  title: {
    color: "#2196F3",
    fontWeight: "bold",
  },
  divider: {
    marginBottom: 16,
  },
  input: {
    marginBottom: 16,
  },
  label: {
    marginBottom: 8,
    color: "#666",
    fontWeight: "500",
  },
  segmentedButtons: {
    marginBottom: 16,
  },
  actions: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 16,
    gap: 12,
  },
  cancelButton: {
    flex: 1,
  },
  saveButton: {
    flex: 1,
  },
});

export default CompanyManagementScreen;
