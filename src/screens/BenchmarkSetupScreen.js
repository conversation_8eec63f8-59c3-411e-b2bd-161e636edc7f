import React, { useState } from "react";
import { View, StyleSheet, ScrollView, Alert, Dimensions, Platform, Image } from "react-native";
import {
  Text,
  Card,
  TextInput,
  Button,
  SegmentedButtons,
  ActivityIndicator,
  Divider,
  Menu,
  Surface,
  Dialog,
  Portal,
} from "react-native-paper";
import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { colors, spacing, borderRadius, commonStyles, typography, elevation } from '../styles/theme';
import { useClerkAuth } from "../contexts/ClerkAuthContext";
import DatabaseService from "../services/database";

const { width } = Dimensions.get("window");

const BenchmarkSetupScreen = ({ navigation }) => {
  const { company } = useClerkAuth();
  const [loading, setLoading] = useState(false);

  // Form state
  const [benchmarkName, setBenchmarkName] = useState("");
  const [amount, setAmount] = useState("");
  const [period, setPeriod] = useState("monthly");
  const [type, setType] = useState("benchmark");
  const [currency, setCurrency] = useState("USD");
  const [currencyMenuVisible, setCurrencyMenuVisible] = useState(false);

  // Dialog state
  const [errorDialogVisible, setErrorDialogVisible] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [successDialogVisible, setSuccessDialogVisible] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");

  const currencies = [
    { code: "USD", symbol: "$", name: "US Dollar" },
    { code: "EUR", symbol: "€", name: "Euro" },
    { code: "GBP", symbol: "£", name: "British Pound" },
    { code: "CAD", symbol: "C$", name: "Canadian Dollar" },
    { code: "AUD", symbol: "A$", name: "Australian Dollar" },
    { code: "JPY", symbol: "¥", name: "Japanese Yen" },
    { code: "CHF", symbol: "CHF", name: "Swiss Franc" },
    { code: "CNY", symbol: "¥", name: "Chinese Yuan" },
    { code: "INR", symbol: "₹", name: "Indian Rupee" },
  ];

  const selectedCurrency =
    currencies.find((c) => c.code === currency) || currencies[0];

  const showError = (message) => {
    setErrorMessage(message);
    setErrorDialogVisible(true);
  };

  const showSuccess = (message) => {
    setSuccessMessage(message);
    setSuccessDialogVisible(true);
  };

  const validateForm = () => {
    console.log("Validating form with values:", {
      benchmarkName: benchmarkName.trim(),
      amount: amount.trim(),
      period,
      type,
      currency,
    });

    if (!benchmarkName.trim()) {
      console.log("Validation failed: No benchmark name");
      showError("Please enter a benchmark name");
      return false;
    }

    if (!amount.trim()) {
      console.log("Validation failed: No amount");
      showError("Please enter an amount");
      return false;
    }

    const numAmount = parseFloat(amount);
    if (isNaN(numAmount) || numAmount <= 0) {
      console.log(
        "Validation failed: Invalid amount:",
        amount,
        "parsed as:",
        numAmount,
      );
      showError("Please enter a valid amount greater than 0");
      return false;
    }

    console.log("Form validation passed");
    return true;
  };

  const handleCreateBenchmark = async () => {
    console.log("handleCreateBenchmark called");
    console.log("Form values:", {
      benchmarkName,
      amount,
      period,
      type,
      currency,
    });

    if (!validateForm()) {
      console.log("Form validation failed");
      return;
    }

    if (!company) {
      console.log("No company found:", company);
      showError("No company associated with your account");
      return;
    }

    // Check plan limits before creating benchmark
    try {
      const limitCheck = await DatabaseService.checkPlanLimits(company.id, 'benchmarks');
      if (!limitCheck.allowed) {
        const limitText = limitCheck.limit === -1
          ? "unlimited benchmarks"
          : `${limitCheck.limit} benchmarks`;
        showError(`Benchmark limit reached. Your ${company.subscription_plan || 'current'} plan allows ${limitText} and you currently have ${limitCheck.current}. Please upgrade your plan to create more benchmarks.`);
        return;
      }
    } catch (error) {
      console.error('Error checking plan limits:', error);
      showError("Failed to check plan limits. Please try again.");
      return;
    }

    console.log("Starting benchmark creation...");
    setLoading(true);

    try {
      const benchmarkData = {
        companyId: company.id,
        name: benchmarkName.trim(),
        amount: parseFloat(amount),
        period,
        type,
        currency,
      };

      console.log("Creating benchmark with data:", benchmarkData);
      const benchmarkId = await DatabaseService.createBenchmark(benchmarkData);
      console.log("Benchmark created with ID:", benchmarkId);

      // Clear the form
      setBenchmarkName("");
      setAmount("");
      setPeriod("monthly");
      setType("benchmark");
      setCurrency("USD");

      showSuccess(`${type === "benchmark" ? "Benchmark" : "Budget"} "${benchmarkName}" created successfully!`);
    } catch (error) {
      console.error("Error creating benchmark:", error);
      showError(`Failed to create benchmark: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const calculateOtherPeriods = (baseAmount, basePeriod) => {
    if (!baseAmount || isNaN(baseAmount))
      return { daily: 0, weekly: 0, monthly: 0 };

    const amount = parseFloat(baseAmount);

    // Convert to daily first
    let dailyAmount;
    switch (basePeriod) {
      case "daily":
        dailyAmount = amount;
        break;
      case "weekly":
        dailyAmount = amount / 7;
        break;
      case "monthly":
        dailyAmount = amount / 30;
        break;
      default:
        dailyAmount = amount;
    }

    return {
      daily: dailyAmount.toFixed(2),
      weekly: (dailyAmount * 7).toFixed(2),
      monthly: (dailyAmount * 30).toFixed(2),
    };
  };

  const calculatedAmounts = calculateOtherPeriods(amount, period);

  return (
    <View style={commonStyles.container}>
      {/* Split Layout - Blue section on left, content on right */}
      <View style={styles.mainContent}>
        {/* Left Side - Blue Header Section (Desktop only) */}
        {Platform.OS === 'web' && width > 768 && (
          <View style={styles.leftSide}>
            <LinearGradient
              colors={colors.gradientPrimary}
              style={styles.leftSideGradient}
            >
              <View style={styles.leftSideContent}>
                {/* App Logo and Name */}
                <View style={styles.logoSection}>
                  <View style={styles.appLogoContainer}>
                    <Image
                      source={require("../../assets/logo.png")}
                      style={styles.appLogo}
                      resizeMode="contain"
                    />
                  </View>
                  <Text style={styles.appNameBig}>CostCompass</Text>
                </View>

                {/* Page Header */}
                <View style={styles.pageHeader}>
                  <View style={styles.headerIconContainer}>
                    <Ionicons
                      name={type === "benchmark" ? "analytics" : "wallet"}
                      size={32}
                      color={colors.textOnPrimary}
                    />
                  </View>
                  <Text variant="headlineLarge" style={styles.leftSideTitle}>
                    Create New {type === "benchmark" ? "Benchmark" : "Budget"}
                  </Text>
                  <Text variant="bodyLarge" style={styles.leftSideSubtitle}>
                    Set up cost tracking for your facility
                  </Text>
                </View>

                {/* Quick Info */}
                <View style={styles.leftSideInfo}>
                  <View style={styles.leftSideInfoItem}>
                    <Ionicons name="business" size={20} color={colors.textOnPrimary} />
                    <Text style={styles.leftSideInfoText}>
                      {company?.name || 'Your Company'}
                    </Text>
                  </View>
                  <View style={styles.leftSideInfoItem}>
                    <Ionicons name="people" size={20} color={colors.textOnPrimary} />
                    <Text style={styles.leftSideInfoText}>
                      {company?.current_occupancy || 0} occupied
                    </Text>
                  </View>
                </View>
              </View>
            </LinearGradient>
          </View>
        )}

        {/* Right Side - Main Content */}
        <View style={styles.rightSide}>
          {/* Mobile Header (only shown on mobile) */}
          {(Platform.OS !== 'web' || width <= 768) && (
            <LinearGradient
              colors={colors.gradientPrimary}
              style={styles.mobileHeader}
            >
              <View style={styles.mobileHeaderContent}>
                <View style={styles.headerIconContainer}>
                  <Ionicons
                    name={type === "benchmark" ? "analytics" : "wallet"}
                    size={32}
                    color={colors.textOnPrimary}
                  />
                </View>
                <Text variant="headlineLarge" style={styles.mobileHeaderTitle}>
                  Create New {type === "benchmark" ? "Benchmark" : "Budget"}
                </Text>
                <Text variant="bodyLarge" style={styles.mobileHeaderSubtitle}>
                  Set up cost tracking for your facility
                </Text>
              </View>
            </LinearGradient>
          )}

          <ScrollView
            contentContainerStyle={styles.rightSideScrollContainer}
            style={styles.rightSideScrollView}
            showsVerticalScrollIndicator={false}
          >
        <Card style={commonStyles.cardLarge}>
          <Card.Content>
            <View style={styles.sectionHeader}>
              <Ionicons name="settings" size={24} color="#1976D2" />
              <Text variant="titleLarge" style={styles.sectionTitle}>
                Configuration
              </Text>
            </View>

            <View style={styles.typeSection}>
              <Text variant="bodyMedium" style={styles.label}>
                Type *
              </Text>
              <View style={styles.typeCards}>
                {[
                  {
                    value: "benchmark",
                    label: "Benchmark",
                    icon: "analytics",
                    description: "Track performance against targets",
                  },
                  {
                    value: "budget",
                    label: "Budget",
                    icon: "wallet",
                    description: "Set spending limits and controls",
                  },
                ].map((option) => (
                  <Surface
                    key={option.value}
                    style={[
                      styles.typeCard,
                      type === option.value && styles.selectedTypeCard,
                    ]}
                    elevation={type === option.value ? 4 : 1}
                  >
                    <Button
                      mode="text"
                      onPress={() => setType(option.value)}
                      style={styles.typeButton}
                      contentStyle={styles.typeButtonContent}
                    >
                      <View style={styles.typeContent}>
                        <Ionicons
                          name={option.icon}
                          size={24}
                          color={type === option.value ? "#1976D2" : "#666"}
                        />
                        <Text
                          variant="titleMedium"
                          style={[
                            styles.typeLabel,
                            type === option.value && { color: "#1976D2" },
                          ]}
                        >
                          {option.label}
                        </Text>
                        <Text
                          variant="bodySmall"
                          style={[
                            styles.typeDescription,
                            type === option.value && { color: "#1565C0" },
                          ]}
                        >
                          {option.description}
                        </Text>
                      </View>
                    </Button>
                  </Surface>
                ))}
              </View>
            </View>

            <TextInput
              label={`${type === "benchmark" ? "Benchmark" : "Budget"} Name *`}
              value={benchmarkName}
              onChangeText={setBenchmarkName}
              mode="outlined"
              style={styles.input}
              disabled={loading}
              placeholder="e.g., Food & Beverage, Utilities, Staff Costs"
            />

            <Text variant="bodyMedium" style={styles.label}>
              Currency *
            </Text>
            <Menu
              visible={currencyMenuVisible}
              onDismiss={() => setCurrencyMenuVisible(false)}
              anchor={
                <Button
                  mode="outlined"
                  onPress={() => setCurrencyMenuVisible(true)}
                  style={styles.currencyButton}
                  contentStyle={styles.currencyButtonContent}
                  disabled={loading}
                >
                  {selectedCurrency.symbol} {selectedCurrency.code} -{" "}
                  {selectedCurrency.name}
                </Button>
              }
            >
              {currencies.map((curr) => (
                <Menu.Item
                  key={curr.code}
                  onPress={() => {
                    setCurrency(curr.code);
                    setCurrencyMenuVisible(false);
                  }}
                  title={`${curr.symbol} ${curr.code} - ${curr.name}`}
                />
              ))}
            </Menu>

            <TextInput
              label="Amount *"
              value={amount}
              onChangeText={setAmount}
              mode="outlined"
              keyboardType="numeric"
              style={styles.input}
              disabled={loading}
              placeholder="0.00"
              left={
                <TextInput.Icon
                  icon={() => (
                    <Text style={styles.currencyIcon}>
                      {selectedCurrency.symbol}
                    </Text>
                  )}
                />
              }
            />

            <Text variant="bodyMedium" style={styles.label}>
              Period *
            </Text>
            <SegmentedButtons
              value={period}
              onValueChange={setPeriod}
              buttons={[
                { value: "daily", label: "Daily" },
                { value: "weekly", label: "Weekly" },
                { value: "monthly", label: "Monthly" },
              ]}
              style={styles.segmentedButtons}
            />

            {amount && !isNaN(parseFloat(amount)) && (
              <Card style={styles.calculationCard} elevation={2}>
                <Card.Content>
                  <View style={styles.calculationHeader}>
                    <Ionicons name="calculator" size={20} color="#2196F3" />
                    <Text variant="titleMedium" style={styles.calculationTitle}>
                      Cost Breakdown
                    </Text>
                  </View>
                  <View style={styles.calculationGrid}>
                    <Surface style={styles.calculationItem} elevation={1}>
                      <View style={styles.calculationItemContent}>
                        <Ionicons name="calendar" size={16} color="#666" />
                        <Text
                          variant="bodySmall"
                          style={styles.calculationLabel}
                        >
                          Daily
                        </Text>
                        <Text
                          variant="titleMedium"
                          style={styles.calculationValue}
                        >
                          {selectedCurrency.symbol}
                          {calculatedAmounts.daily}
                        </Text>
                      </View>
                    </Surface>
                    <Surface style={styles.calculationItem} elevation={1}>
                      <View style={styles.calculationItemContent}>
                        <Ionicons
                          name="calendar-outline"
                          size={16}
                          color="#666"
                        />
                        <Text
                          variant="bodySmall"
                          style={styles.calculationLabel}
                        >
                          Weekly
                        </Text>
                        <Text
                          variant="titleMedium"
                          style={styles.calculationValue}
                        >
                          {selectedCurrency.symbol}
                          {calculatedAmounts.weekly}
                        </Text>
                      </View>
                    </Surface>
                    <Surface style={styles.calculationItem} elevation={1}>
                      <View style={styles.calculationItemContent}>
                        <Ionicons
                          name="calendar-sharp"
                          size={16}
                          color="#666"
                        />
                        <Text
                          variant="bodySmall"
                          style={styles.calculationLabel}
                        >
                          Monthly
                        </Text>
                        <Text
                          variant="titleMedium"
                          style={styles.calculationValue}
                        >
                          {selectedCurrency.symbol}
                          {calculatedAmounts.monthly}
                        </Text>
                      </View>
                    </Surface>
                  </View>

                  {company?.current_occupancy &&
                    company.current_occupancy > 1 && (
                      <>
                        <Divider style={styles.calculationDivider} />
                        <View style={styles.occupancySection}>
                          <View style={styles.occupancyHeader}>
                            <Ionicons name="people" size={20} color="#42A5F5" />
                            <Text
                              variant="titleMedium"
                              style={styles.occupancyTitle}
                            >
                              Total with Current Occupancy (
                              {company.current_occupancy})
                            </Text>
                          </View>
                          <View style={styles.occupancyGrid}>
                            <View style={styles.occupancyItem}>
                              <Text
                                variant="bodySmall"
                                style={styles.occupancyLabel}
                              >
                                Daily Total
                              </Text>
                              <Text
                                variant="titleMedium"
                                style={styles.occupancyValue}
                              >
                                {selectedCurrency.symbol}
                                {(
                                  calculatedAmounts.daily *
                                  company.current_occupancy
                                ).toFixed(2)}
                              </Text>
                            </View>
                            <View style={styles.occupancyItem}>
                              <Text
                                variant="bodySmall"
                                style={styles.occupancyLabel}
                              >
                                Weekly Total
                              </Text>
                              <Text
                                variant="titleMedium"
                                style={styles.occupancyValue}
                              >
                                {selectedCurrency.symbol}
                                {(
                                  calculatedAmounts.weekly *
                                  company.current_occupancy
                                ).toFixed(2)}
                              </Text>
                            </View>
                            <View style={styles.occupancyItem}>
                              <Text
                                variant="bodySmall"
                                style={styles.occupancyLabel}
                              >
                                Monthly Total
                              </Text>
                              <Text
                                variant="titleMedium"
                                style={styles.occupancyValue}
                              >
                                {selectedCurrency.symbol}
                                {(
                                  calculatedAmounts.monthly *
                                  company.current_occupancy
                                ).toFixed(2)}
                              </Text>
                            </View>
                          </View>
                        </View>
                      </>
                    )}
                </Card.Content>
              </Card>
            )}

            <Button
              mode="contained"
              onPress={() => {
                console.log("Create benchmark button clicked!");
                handleCreateBenchmark();
              }}
              style={styles.button}
              disabled={loading}
              buttonColor={colors.primary}
            >
              {loading ? (
                <ActivityIndicator color="white" />
              ) : (
                `Create ${type === "benchmark" ? "Benchmark" : "Budget"}`
              )}
            </Button>

            <Button
              mode="outlined"
              onPress={() => {
                console.log("Cancel button clicked");
                if (navigation && navigation.goBack) {
                  navigation.goBack();
                } else {
                  console.log("Navigation not available for cancel");
                }
              }}
              style={styles.cancelButton}
              disabled={loading}
            >
              Cancel
            </Button>
          </Card.Content>
        </Card>
          </ScrollView>
        </View>
      </View>

      {/* Compact Dialogs */}
      <Portal>
        <Dialog
          visible={errorDialogVisible}
          onDismiss={() => setErrorDialogVisible(false)}
          style={commonStyles.compactDialog}
        >
          <Dialog.Title style={commonStyles.compactDialogTitle}>Error</Dialog.Title>
          <Dialog.Content style={commonStyles.compactDialogContent}>
            <Text variant="bodyMedium" style={commonStyles.compactDialogText}>{errorMessage}</Text>
          </Dialog.Content>
          <Dialog.Actions style={commonStyles.compactDialogActions}>
            <Button
              onPress={() => setErrorDialogVisible(false)}
              mode="contained"
              style={commonStyles.compactDialogButton}
              compact
            >
              OK
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      <Portal>
        <Dialog
          visible={successDialogVisible}
          onDismiss={() => setSuccessDialogVisible(false)}
          style={commonStyles.compactDialog}
        >
          <Dialog.Title style={commonStyles.compactDialogTitle}>Success!</Dialog.Title>
          <Dialog.Content style={commonStyles.compactDialogContent}>
            <Text variant="bodyMedium" style={commonStyles.compactDialogText}>{successMessage}</Text>
          </Dialog.Content>
          <Dialog.Actions style={commonStyles.compactDialogActions}>
            <Button
              onPress={() => setSuccessDialogVisible(false)}
              mode="outlined"
              style={commonStyles.compactDialogButton}
              compact
            >
              Stay Here
            </Button>
            <Button
              onPress={() => {
                setSuccessDialogVisible(false);
                if (navigation && navigation.goBack) {
                  navigation.goBack();
                } else if (navigation && navigation.navigate) {
                  navigation.navigate("HomeMain");
                } else {
                  console.log("Navigation not available, reloading page");
                  window.location.reload();
                }
              }}
              mode="contained"
              style={commonStyles.compactDialogButton}
              compact
            >
              Go Back
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  // Split Layout Styles
  mainContent: {
    flex: 1,
    flexDirection: Platform.OS === 'web' && width > 768 ? 'row' : 'column',
  },
  // Left Side - Blue Section (Desktop only)
  leftSide: {
    flex: 0.4,
    minWidth: 350,
    maxWidth: 450,
  },
  leftSideGradient: {
    flex: 1,
    padding: spacing.lg,
    paddingTop: spacing.md,
    justifyContent: 'flex-start',
  },
  leftSideContent: {
    alignItems: 'flex-start',
    maxWidth: 350,
    alignSelf: 'center',
    width: '100%',
  },
  // Logo Section
  logoSection: {
    alignItems: 'flex-start',
    alignSelf: 'flex-start',
    marginBottom: spacing.xl,
    paddingBottom: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.2)',
    width: '100%',
  },
  appLogoContainer: {
    width: '100%', // Full width of the blue sidebar content area
    height: 120, // Taller to accommodate larger logo
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.lg,
    marginTop: spacing.sm,
    // Remove artificial shadows and borders for more natural integration
  },
  appLogo: {
    width: 280, // Almost full width of the blue sidebar content
    height: 100, // Proportional height
    // Remove tintColor to show original logo colors
  },
  appNameBig: {
    color: colors.textOnPrimary,
    fontSize: 32,
    fontWeight: typography.fontWeights.bold,
    textAlign: 'left',
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
    letterSpacing: 0.5,
  },
  // Page Header Section
  pageHeader: {
    alignItems: 'flex-start',
    marginBottom: spacing.lg,
    width: '100%',
  },
  leftSideTitle: {
    color: colors.textOnPrimary,
    fontSize: 24,
    fontWeight: typography.fontWeights.bold,
    textAlign: 'left',
    marginBottom: spacing.sm,
    textShadowColor: 'rgba(0,0,0,0.1)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  leftSideSubtitle: {
    color: colors.textOnPrimary,
    fontSize: 16,
    textAlign: 'left',
    opacity: 0.95,
    marginBottom: spacing.lg,
    textShadowColor: 'rgba(0,0,0,0.1)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  leftSideInfo: {
    marginTop: spacing.lg,
    alignSelf: 'stretch',
  },
  leftSideInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    padding: spacing.md,
    borderRadius: borderRadius.lg,
  },
  leftSideInfoText: {
    color: colors.textOnPrimary,
    fontSize: 14,
    fontWeight: '500',
    marginLeft: spacing.sm,
  },
  // Right Side - Main Content
  rightSide: {
    flex: 1.6,
    backgroundColor: colors.background,
  },
  rightSideScrollContainer: {
    padding: spacing.lg,
    paddingBottom: spacing.xxxl,
  },
  rightSideScrollView: {
    flex: 1,
  },
  // Mobile Header
  mobileHeader: {
    paddingTop: spacing.xxxxl,
    paddingBottom: spacing.xl,
    paddingHorizontal: spacing.xl,
    borderBottomLeftRadius: borderRadius.xxl,
    borderBottomRightRadius: borderRadius.xxl,
  },
  mobileHeaderContent: {
    alignItems: 'center',
  },
  mobileHeaderTitle: {
    color: colors.textOnPrimary,
    fontSize: 24,
    fontWeight: typography.fontWeights.bold,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  mobileHeaderSubtitle: {
    color: colors.textOnPrimary,
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.95,
  },
  headerIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
    elevation: elevation.sm,
    shadowColor: 'rgba(0, 0, 0, 0.3)',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  // Section Styles
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.xl,
  },
  sectionTitle: {
    marginLeft: spacing.sm,
    color: colors.primary,
    fontWeight: typography.fontWeights.bold,
    fontSize: typography.fontSizes.xl,
  },
  typeSection: {
    marginBottom: spacing.xl,
  },
  label: {
    marginBottom: spacing.sm,
    color: colors.textSecondary,
    fontWeight: typography.fontWeights.medium,
    fontSize: typography.fontSizes.md,
  },
  typeCards: {
    flexDirection: width > 600 ? "row" : "column",
    gap: spacing.lg,
  },
  typeCard: {
    flex: width > 600 ? 1 : undefined,
    borderRadius: borderRadius.lg,
    overflow: "hidden",
    backgroundColor: colors.surface,
  },
  selectedTypeCard: {
    backgroundColor: colors.primary + '10',
    borderWidth: 2,
    borderColor: colors.primary,
  },
  typeButton: {
    margin: 0,
    borderRadius: borderRadius.lg,
  },
  typeButtonContent: {
    paddingVertical: spacing.xl,
    paddingHorizontal: spacing.lg,
  },
  typeContent: {
    alignItems: "center",
    gap: spacing.sm,
  },
  typeLabel: {
    fontWeight: typography.fontWeights.bold,
    fontSize: typography.fontSizes.md,
  },
  typeDescription: {
    textAlign: "center",
    color: colors.textSecondary,
    fontSize: typography.fontSizes.sm,
  },
  input: {
    marginBottom: spacing.lg,
    backgroundColor: colors.surface,
  },
  currencyButton: {
    marginBottom: spacing.lg,
    justifyContent: "flex-start",
    borderRadius: borderRadius.lg,
  },
  currencyButtonContent: {
    justifyContent: "flex-start",
  },
  currencyIcon: {
    fontSize: typography.fontSizes.md,
    fontWeight: typography.fontWeights.bold,
    color: colors.textSecondary,
  },
  segmentedButtons: {
    marginBottom: spacing.lg,
  },
  calculationCard: {
    backgroundColor: colors.success + '15',
    marginBottom: spacing.lg,
    borderRadius: borderRadius.lg,
  },
  calculationHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.lg,
  },
  calculationTitle: {
    marginLeft: spacing.sm,
    color: colors.success,
    fontWeight: typography.fontWeights.bold,
    fontSize: typography.fontSizes.lg,
  },
  calculationGrid: {
    flexDirection: width > 600 ? "row" : "column",
    gap: spacing.lg,
  },
  calculationItem: {
    flex: width > 600 ? 1 : undefined,
    borderRadius: borderRadius.md,
    backgroundColor: colors.surface,
  },
  calculationItemContent: {
    padding: spacing.lg,
    alignItems: "center",
    gap: spacing.xs,
  },
  calculationLabel: {
    color: colors.textSecondary,
    textAlign: "center",
    fontSize: typography.fontSizes.sm,
  },
  calculationValue: {
    color: colors.success,
    fontWeight: typography.fontWeights.bold,
    textAlign: "center",
    fontSize: typography.fontSizes.lg,
  },
  calculationDivider: {
    marginVertical: spacing.lg,
  },
  occupancySection: {
    marginTop: spacing.sm,
  },
  occupancyHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.lg,
  },
  occupancyTitle: {
    marginLeft: spacing.sm,
    color: colors.accent,
    fontWeight: typography.fontWeights.bold,
    fontSize: typography.fontSizes.lg,
  },
  occupancyGrid: {
    flexDirection: width > 600 ? "row" : "column",
    gap: spacing.sm,
  },
  occupancyItem: {
    flex: width > 600 ? 1 : undefined,
    backgroundColor: colors.accent + '15',
    padding: spacing.lg,
    borderRadius: borderRadius.md,
    alignItems: "center",
  },
  occupancyLabel: {
    color: colors.textSecondary,
    textAlign: "center",
    marginBottom: spacing.xs,
    fontSize: typography.fontSizes.sm,
  },
  occupancyValue: {
    color: colors.accent,
    fontWeight: typography.fontWeights.bold,
    textAlign: "center",
    fontSize: typography.fontSizes.lg,
  },
  button: {
    marginTop: spacing.md,
    marginBottom: spacing.md,
    borderRadius: borderRadius.lg,
  },
  cancelButton: {
    marginBottom: spacing.md,
    borderRadius: borderRadius.lg,
  },
});

export default BenchmarkSetupScreen;
