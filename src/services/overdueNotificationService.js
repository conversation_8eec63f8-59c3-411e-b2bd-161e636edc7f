import { Platform } from 'react-native';
import DatabaseService from './database';
import EmailService from './emailService';

class OverdueNotificationService {
  constructor() {
    this.isWeb = Platform.OS === 'web';
    this.OVERDUE_DAYS = 7; // 7 days = 1 week
  }

  /**
   * Check for overdue spend updates and send notifications
   */
  async checkAndNotifyOverdueSpends() {
    console.log('🔍 Checking for overdue spend updates...');
    
    try {
      // Get all companies
      const companies = await DatabaseService.getAllCompanies();
      
      for (const company of companies) {
        await this.checkCompanyOverdueSpends(company);
      }
      
      console.log('✅ Overdue spend check completed');
    } catch (error) {
      console.error('❌ Error checking overdue spends:', error);
    }
  }

  /**
   * Check overdue spends for a specific company
   */
  async checkCompanyOverdueSpends(company) {
    try {
      console.log(`Checking overdue spends for company: ${company.name}`);
      
      // Get all active benchmarks for the company
      const benchmarks = await DatabaseService.getBenchmarksByCompany(company.id);
      const activeBenchmarks = benchmarks.filter(b => b.is_active !== false);
      
      if (activeBenchmarks.length === 0) {
        console.log(`No active benchmarks for company ${company.name}`);
        return;
      }

      // Check each benchmark for overdue updates
      const overdueBenchmarks = [];
      const currentDate = new Date();
      
      for (const benchmark of activeBenchmarks) {
        const isOverdue = await this.isBenchmarkOverdue(benchmark, currentDate);
        if (isOverdue) {
          overdueBenchmarks.push(benchmark);
        }
      }

      if (overdueBenchmarks.length > 0) {
        console.log(`Found ${overdueBenchmarks.length} overdue benchmarks for ${company.name}`);
        await this.sendOverdueNotifications(company, overdueBenchmarks);
      } else {
        console.log(`No overdue benchmarks for company ${company.name}`);
      }
      
    } catch (error) {
      console.error(`Error checking overdue spends for company ${company.name}:`, error);
    }
  }

  /**
   * Check if a benchmark has overdue spend updates
   */
  async isBenchmarkOverdue(benchmark, currentDate) {
    try {
      // Get the most recent actual spend entry for this benchmark
      const recentSpends = await DatabaseService.getActualSpendsByBenchmark(benchmark.id, 1); // Get 1 most recent
      
      if (recentSpends.length === 0) {
        // No spend entries at all - check if benchmark is older than overdue period
        const benchmarkDate = new Date(benchmark.created_at);
        const daysSinceCreation = Math.floor((currentDate - benchmarkDate) / (1000 * 60 * 60 * 24));
        return daysSinceCreation > this.OVERDUE_DAYS;
      }

      // Check if the most recent spend entry is older than overdue period
      const lastSpendDate = new Date(recentSpends[0].date);
      const daysSinceLastUpdate = Math.floor((currentDate - lastSpendDate) / (1000 * 60 * 60 * 24));
      
      return daysSinceLastUpdate > this.OVERDUE_DAYS;
      
    } catch (error) {
      console.error(`Error checking if benchmark ${benchmark.name} is overdue:`, error);
      return false;
    }
  }

  /**
   * Send overdue notifications to all company users
   */
  async sendOverdueNotifications(company, overdueBenchmarks) {
    try {
      console.log(`Sending overdue notifications for ${company.name}`);
      
      // Get all users for the company
      const users = await DatabaseService.getUsersByCompany(company.id);
      
      if (users.length === 0) {
        console.log(`No users found for company ${company.name}`);
        return;
      }

      // Create in-app notifications for each overdue benchmark
      for (const benchmark of overdueBenchmarks) {
        await this.createOverdueNotification(company, benchmark);
      }

      // Send email notification to all users
      await this.sendOverdueEmail(company, users, overdueBenchmarks);
      
      console.log(`✅ Overdue notifications sent to ${users.length} users for ${company.name}`);
      
    } catch (error) {
      console.error(`Error sending overdue notifications for ${company.name}:`, error);
    }
  }

  /**
   * Create in-app notification for overdue spend update
   */
  async createOverdueNotification(company, benchmark) {
    try {
      const message = `Spend update overdue: "${benchmark.name}" hasn't been updated for more than a week. Please update your actual spending.`;
      
      await DatabaseService.createNotification({
        companyId: company.id,
        benchmarkId: benchmark.id,
        type: 'spend_update_overdue',
        message: message,
        // Don't include actualAmount, benchmarkAmount, variance for overdue notifications
        // to prevent email sending (we'll send a custom email instead)
      });
      
      console.log(`Created overdue notification for benchmark: ${benchmark.name}`);
      
    } catch (error) {
      console.error(`Error creating overdue notification for benchmark ${benchmark.name}:`, error);
    }
  }

  /**
   * Send email notification for overdue spend updates
   */
  async sendOverdueEmail(company, users, overdueBenchmarks) {
    try {
      const emailData = {
        companyName: company.name,
        overdueBenchmarks: overdueBenchmarks.map(b => ({
          name: b.name,
          type: b.type,
          period: b.period,
          amount: b.amount,
          currency: b.currency || 'GBP'
        })),
        overdueCount: overdueBenchmarks.length,
        overdueDays: this.OVERDUE_DAYS
      };

      await EmailService.sendOverdueSpendNotification(users, emailData);
      
      console.log(`✅ Overdue email sent to ${users.length} users`);
      
    } catch (error) {
      console.error('Error sending overdue email:', error);
    }
  }

  /**
   * Get days since last update for a benchmark
   */
  async getDaysSinceLastUpdate(benchmarkId) {
    try {
      const recentSpends = await DatabaseService.getActualSpendsByBenchmark(benchmarkId, 1);
      
      if (recentSpends.length === 0) {
        return null; // No updates yet
      }

      const lastSpendDate = new Date(recentSpends[0].date);
      const currentDate = new Date();
      const daysSinceLastUpdate = Math.floor((currentDate - lastSpendDate) / (1000 * 60 * 60 * 24));
      
      return daysSinceLastUpdate;
      
    } catch (error) {
      console.error(`Error getting days since last update for benchmark ${benchmarkId}:`, error);
      return null;
    }
  }

  /**
   * Check if a specific benchmark is overdue (for UI indicators)
   */
  async isBenchmarkOverdueById(benchmarkId) {
    try {
      const benchmark = await DatabaseService.getBenchmarkById(benchmarkId);
      if (!benchmark) return false;
      
      const currentDate = new Date();
      return await this.isBenchmarkOverdue(benchmark, currentDate);
      
    } catch (error) {
      console.error(`Error checking if benchmark ${benchmarkId} is overdue:`, error);
      return false;
    }
  }

  /**
   * Get overdue benchmarks for a company (for UI display)
   */
  async getOverdueBenchmarks(companyId) {
    try {
      const benchmarks = await DatabaseService.getBenchmarksByCompany(companyId);
      const activeBenchmarks = benchmarks.filter(b => b.is_active !== false);
      
      const overdueBenchmarks = [];
      const currentDate = new Date();
      
      for (const benchmark of activeBenchmarks) {
        const isOverdue = await this.isBenchmarkOverdue(benchmark, currentDate);
        if (isOverdue) {
          const daysSinceLastUpdate = await this.getDaysSinceLastUpdate(benchmark.id);
          overdueBenchmarks.push({
            ...benchmark,
            daysSinceLastUpdate
          });
        }
      }
      
      return overdueBenchmarks;
      
    } catch (error) {
      console.error(`Error getting overdue benchmarks for company ${companyId}:`, error);
      return [];
    }
  }

  /**
   * Start periodic checking (for background service)
   */
  startPeriodicCheck(intervalHours = 24) {
    console.log(`🔄 Starting periodic overdue check every ${intervalHours} hours`);
    
    // Run initial check
    this.checkAndNotifyOverdueSpends();
    
    // Set up interval for periodic checks
    const intervalMs = intervalHours * 60 * 60 * 1000;
    return setInterval(() => {
      this.checkAndNotifyOverdueSpends();
    }, intervalMs);
  }

  /**
   * Stop periodic checking
   */
  stopPeriodicCheck(intervalId) {
    if (intervalId) {
      clearInterval(intervalId);
      console.log('🛑 Stopped periodic overdue check');
    }
  }
}

export default new OverdueNotificationService();
