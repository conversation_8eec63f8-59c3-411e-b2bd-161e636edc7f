import { Platform } from 'react-native';
import * as MailComposer from 'expo-mail-composer';

class EmailService {
  constructor() {
    this.isWeb = Platform.OS === 'web';
    this.emailQueue = []; // For web simulation
  }

  /**
   * Send email notification to users when spending exceeds benchmark/budget
   */
  async sendSpendingExceededNotification(users, notificationData) {
    const { benchmarkName, actualAmount, benchmarkAmount, variance, type, companyName, currency = 'GBP' } = notificationData;

    console.log('Sending spending exceeded notification to users:', users.length);

    const subject = `${type === 'budget' ? 'Budget' : 'Benchmark'} Exceeded Alert - ${benchmarkName}`;

    const body = this.generateSpendingExceededEmailBody({
      benchmarkName,
      actualAmount,
      benchmarkAmount,
      variance,
      type,
      companyName,
      currency
    });

    // Send email to each user
    for (const user of users) {
      try {
        await this.sendEmail(user.email, subject, body);
        console.log(`Email sent successfully to ${user.email}`);
      } catch (error) {
        console.error(`Failed to send email to ${user.email}:`, error);
      }
    }
  }

  /**
   * Send email notification for overdue spend updates
   */
  async sendOverdueSpendNotification(users, notificationData) {
    const { companyName, overdueBenchmarks, overdueCount, overdueDays } = notificationData;

    console.log('Sending overdue spend notification to users:', users.length);

    const subject = `Spend Update Reminder - ${overdueCount} ${overdueCount === 1 ? 'Item' : 'Items'} Overdue`;

    const body = this.generateOverdueSpendEmailBody({
      companyName,
      overdueBenchmarks,
      overdueCount,
      overdueDays
    });

    // Send email to each user
    for (const user of users) {
      try {
        await this.sendEmail(user.email, subject, body);
        console.log(`Overdue email sent successfully to ${user.email}`);
      } catch (error) {
        console.error(`Failed to send overdue email to ${user.email}:`, error);
      }
    }
  }

  /**
   * Generate HTML email body for overdue spend updates
   */
  generateOverdueSpendEmailBody({ companyName, overdueBenchmarks, overdueCount, overdueDays }) {
    const currencySymbol = this.getCurrencySymbol(overdueBenchmarks[0]?.currency || 'GBP');

    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spend Update Reminder</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #1976D2, #42A5F5); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: #fff; padding: 30px; border: 1px solid #e0e0e0; }
        .reminder-box { background: #e3f2fd; border: 1px solid #90caf9; border-radius: 6px; padding: 20px; margin: 20px 0; }
        .reminder-icon { font-size: 24px; margin-bottom: 10px; }
        .overdue-list { background: #f5f5f5; border-radius: 6px; padding: 15px; margin: 15px 0; }
        .overdue-item { padding: 10px 0; border-bottom: 1px solid #e0e0e0; }
        .overdue-item:last-child { border-bottom: none; }
        .benchmark-name { font-weight: bold; color: #1976D2; }
        .benchmark-details { font-size: 14px; color: #666; margin-top: 5px; }
        .footer { background: #f5f5f5; padding: 20px; text-align: center; border-radius: 0 0 8px 8px; font-size: 12px; color: #666; }
        .button { display: inline-block; background: #1976D2; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
        .urgent { color: #d32f2f; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⏰ Spend Update Reminder</h1>
            <p>CostCompass Notification</p>
        </div>

        <div class="content">
            <div class="reminder-box">
                <div class="reminder-icon">📊</div>
                <h2>Spend Updates Required for ${companyName}</h2>
                <p>You have <strong class="urgent">${overdueCount} ${overdueCount === 1 ? 'benchmark' : 'benchmarks'}</strong> that ${overdueCount === 1 ? 'hasn\'t' : 'haven\'t'} been updated for more than ${overdueDays} days.</p>
            </div>

            <h3>Overdue Items:</h3>
            <div class="overdue-list">
                ${overdueBenchmarks.map(benchmark => `
                    <div class="overdue-item">
                        <div class="benchmark-name">${benchmark.name}</div>
                        <div class="benchmark-details">
                            ${benchmark.type.charAt(0).toUpperCase() + benchmark.type.slice(1)} •
                            ${currencySymbol}${benchmark.amount.toFixed(2)} per ${benchmark.period}
                        </div>
                    </div>
                `).join('')}
            </div>

            <p><strong>Why Regular Updates Matter:</strong></p>
            <ul>
                <li>Accurate budget tracking and variance analysis</li>
                <li>Early detection of overspending</li>
                <li>Better financial planning and forecasting</li>
                <li>Compliance with company spending policies</li>
                <li>Real-time visibility for management decisions</li>
            </ul>

            <p><strong>Action Required:</strong></p>
            <ul>
                <li>Log into your CostCompass dashboard</li>
                <li>Update actual spending for the overdue items listed above</li>
                <li>Set up regular reminders to update spending weekly</li>
                <li>Contact your administrator if you need assistance</li>
            </ul>

            <p>
                <a href="#" class="button">Update Spending Now</a>
            </p>

            <p><em>Regular spend updates help maintain accurate financial tracking and ensure timely budget management.</em></p>
        </div>

        <div class="footer">
            <p>This is an automated reminder from CostCompass.</p>
            <p>You are receiving this because you have access to ${companyName}'s spending data.</p>
            <p>&copy; ${new Date().getFullYear()} CostCompass. All rights reserved.</p>
        </div>
    </div>
</body>
</html>`;
  }

  /**
   * Generate HTML email body for spending exceeded notification
   */
  generateSpendingExceededEmailBody({ benchmarkName, actualAmount, benchmarkAmount, variance, type, companyName, currency }) {
    const currencySymbol = this.getCurrencySymbol(currency);
    const typeLabel = type === 'budget' ? 'Budget' : 'Benchmark';
    
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${typeLabel} Exceeded Alert</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #1976D2, #42A5F5); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: #fff; padding: 30px; border: 1px solid #e0e0e0; }
        .alert-box { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 20px; margin: 20px 0; }
        .alert-icon { font-size: 24px; margin-bottom: 10px; }
        .amount { font-size: 24px; font-weight: bold; color: #1976D2; }
        .variance { font-size: 20px; font-weight: bold; color: #d32f2f; }
        .details-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .details-table th, .details-table td { padding: 12px; text-align: left; border-bottom: 1px solid #e0e0e0; }
        .details-table th { background: #f5f5f5; font-weight: bold; }
        .footer { background: #f5f5f5; padding: 20px; text-align: center; border-radius: 0 0 8px 8px; font-size: 12px; color: #666; }
        .button { display: inline-block; background: #1976D2; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚨 ${typeLabel} Exceeded Alert</h1>
            <p>CostCompass Notification</p>
        </div>
        
        <div class="content">
            <div class="alert-box">
                <div class="alert-icon">⚠️</div>
                <h2>Spending Alert for ${companyName}</h2>
                <p>The ${typeLabel.toLowerCase()} "<strong>${benchmarkName}</strong>" has been exceeded.</p>
            </div>
            
            <table class="details-table">
                <tr>
                    <th>${typeLabel} Name</th>
                    <td>${benchmarkName}</td>
                </tr>
                <tr>
                    <th>${typeLabel} Amount</th>
                    <td class="amount">${currencySymbol}${benchmarkAmount.toFixed(2)}</td>
                </tr>
                <tr>
                    <th>Actual Spend</th>
                    <td class="amount">${currencySymbol}${actualAmount.toFixed(2)}</td>
                </tr>
                <tr>
                    <th>Variance (Over ${typeLabel.toLowerCase()})</th>
                    <td class="variance">+${currencySymbol}${variance.toFixed(2)}</td>
                </tr>
                <tr>
                    <th>Percentage Over</th>
                    <td class="variance">+${((variance / benchmarkAmount) * 100).toFixed(1)}%</td>
                </tr>
            </table>
            
            <p><strong>Action Required:</strong></p>
            <ul>
                <li>Review the spending details in your CostCompass dashboard</li>
                <li>Investigate the cause of the overspend</li>
                <li>Consider adjusting future spending or updating the ${typeLabel.toLowerCase()} if appropriate</li>
                <li>Document any necessary corrective actions</li>
            </ul>
            
            <p>
                <a href="#" class="button">View in CostCompass Dashboard</a>
            </p>
        </div>
        
        <div class="footer">
            <p>This is an automated notification from CostCompass.</p>
            <p>If you have any questions, please contact your system administrator.</p>
            <p>&copy; ${new Date().getFullYear()} CostCompass. All rights reserved.</p>
        </div>
    </div>
</body>
</html>`;
  }

  /**
   * Send email using platform-appropriate method
   */
  async sendEmail(to, subject, body) {
    if (this.isWeb) {
      // For web, simulate email sending
      console.log('📧 [EMAIL SIMULATION] Sending email:');
      console.log('To:', to);
      console.log('Subject:', subject);
      console.log('Body preview:', body.substring(0, 200) + '...');
      
      // Store in queue for demonstration
      this.emailQueue.push({
        to,
        subject,
        body,
        sentAt: new Date().toISOString(),
        status: 'sent'
      });
      
      return { success: true, messageId: `sim_${Date.now()}` };
    } else {
      // For mobile, use MailComposer
      const isAvailable = await MailComposer.isAvailableAsync();
      
      if (!isAvailable) {
        throw new Error('Email is not available on this device');
      }
      
      const result = await MailComposer.composeAsync({
        recipients: [to],
        subject,
        body,
        isHtml: true,
      });
      
      return { success: result.status === 'sent', result };
    }
  }

  /**
   * Get currency symbol for display
   */
  getCurrencySymbol(currency) {
    const symbols = {
      'GBP': '£',
      'USD': '$',
      'EUR': '€',
      'CAD': 'C$',
      'AUD': 'A$',
      'JPY': '¥',
      'CHF': 'CHF',
      'CNY': '¥',
      'INR': '₹'
    };
    return symbols[currency] || currency;
  }

  /**
   * Get sent emails (for web simulation)
   */
  getSentEmails() {
    return this.emailQueue;
  }

  /**
   * Clear email queue (for testing)
   */
  clearEmailQueue() {
    this.emailQueue = [];
  }
}

export default new EmailService();
