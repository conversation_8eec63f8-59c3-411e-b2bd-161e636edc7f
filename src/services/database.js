import * as SQLite from 'expo-sqlite';
import { Platform } from 'react-native';

class DatabaseService {
  constructor() {
    this.db = null;
    this.isWeb = Platform.OS === 'web';
    this.mockData = {
      users: [],
      companies: [],
      benchmarks: [],
      actualSpends: [],
      notifications: [],
      occupancyHistory: [],
      subscriptions: []
    };
  }

  async init() {
    try {
      if (this.isWeb) {
        await this.createDummyData();
        return;
      }

      this.db = await SQLite.openDatabaseAsync('costcompass.db');
      await this.createTables();
      await this.createDummyDataIfEmpty();
      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Database initialization error:', error);
      throw error;
    }
  }

  async createTables() {
    const createTablesSQL = `
      -- Users table (Modified to allow multiple accounts per email)
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        email TEXT NOT NULL,
        password TEXT NOT NULL,
        name TEXT NOT NULL,
        is_admin INTEGER DEFAULT 0,
        company_id INTEGER,
        permissions TEXT DEFAULT '[]',
        is_active INTEGER DEFAULT 1,
        last_login DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        account_name TEXT,
        FOREIGN KEY (company_id) REFERENCES companies (id)
      );

      -- Companies table
      CREATE TABLE IF NOT EXISTS companies (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        account_number TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        registration TEXT,
        capacity INTEGER NOT NULL,
        current_occupancy INTEGER DEFAULT 0,
        capacity_type TEXT NOT NULL CHECK (capacity_type IN ('beds', 'rooms', 'persons')),
        subscription_plan TEXT DEFAULT 'trial' CHECK (subscription_plan IN ('trial', 'starter', 'professional', 'enterprise')),
        subscription_status TEXT DEFAULT 'trial' CHECK (subscription_status IN ('trial', 'active', 'expired', 'cancelled')),
        subscription_start_date DATETIME,
        subscription_end_date DATETIME,
        trial_end_date DATETIME,
        billing_cycle TEXT DEFAULT 'monthly' CHECK (billing_cycle IN ('monthly', 'annual')),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      -- Benchmarks table
      CREATE TABLE IF NOT EXISTS benchmarks (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        company_id INTEGER NOT NULL,
        name TEXT NOT NULL,
        amount REAL NOT NULL,
        period TEXT NOT NULL CHECK (period IN ('daily', 'weekly', 'monthly')),
        type TEXT NOT NULL CHECK (type IN ('benchmark', 'budget')),
        currency TEXT DEFAULT 'USD',
        is_active INTEGER DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (company_id) REFERENCES companies (id)
      );

      -- Actual spend records
      CREATE TABLE IF NOT EXISTS actual_spends (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        benchmark_id INTEGER NOT NULL,
        amount REAL NOT NULL,
        period_start DATE NOT NULL,
        period_end DATE NOT NULL,
        period_type TEXT NOT NULL CHECK (period_type IN ('daily', 'weekly', 'monthly')),
        benchmark_amount_at_time REAL NOT NULL,
        occupancy_at_time INTEGER DEFAULT 1,
        variance REAL,
        created_by INTEGER NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (benchmark_id) REFERENCES benchmarks (id),
        FOREIGN KEY (created_by) REFERENCES users (id)
      );

      -- User invitations table
      CREATE TABLE IF NOT EXISTS invitations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        email TEXT NOT NULL,
        company_id INTEGER NOT NULL,
        invited_by INTEGER NOT NULL,
        permissions TEXT DEFAULT '[]',
        token TEXT UNIQUE NOT NULL,
        status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'expired')),
        expires_at DATETIME NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (company_id) REFERENCES companies (id),
        FOREIGN KEY (invited_by) REFERENCES users (id)
      );

      -- Notifications table
      CREATE TABLE IF NOT EXISTS notifications (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        company_id INTEGER NOT NULL,
        benchmark_id INTEGER NOT NULL,
        type TEXT NOT NULL CHECK (type IN ('budget_exceeded', 'benchmark_exceeded')),
        message TEXT NOT NULL,
        is_read INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (company_id) REFERENCES companies (id),
        FOREIGN KEY (benchmark_id) REFERENCES benchmarks (id)
      );

      -- User-Company relationships table (many-to-many)
      CREATE TABLE IF NOT EXISTS user_companies (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        company_id INTEGER NOT NULL,
        is_admin INTEGER DEFAULT 0,
        permissions TEXT DEFAULT '[]',
        is_active INTEGER DEFAULT 1,
        joined_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
        FOREIGN KEY (company_id) REFERENCES companies (id) ON DELETE CASCADE,
        UNIQUE(user_id, company_id)
      );

      -- Occupancy history table
      CREATE TABLE IF NOT EXISTS occupancy_history (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        company_id INTEGER NOT NULL,
        old_occupancy INTEGER NOT NULL,
        new_occupancy INTEGER NOT NULL,
        change_reason TEXT,
        created_by INTEGER NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (company_id) REFERENCES companies (id),
        FOREIGN KEY (created_by) REFERENCES users (id)
      );
    `;

    await this.db.execAsync(createTablesSQL);

    // Run migrations for existing databases
    await this.runMigrations();
  }

  async runMigrations() {
    if (this.isWeb) {
      // For web, we need to add the benchmark_amount_at_time and created_by fields to existing mock data
      this.mockData.actualSpends.forEach(spend => {
        if (!spend.benchmark_amount_at_time) {
          // For existing data, use the current benchmark amount as fallback
          const benchmark = this.mockData.benchmarks.find(b => b.id === spend.benchmark_id);
          if (benchmark) {
            let benchmarkAmount = benchmark.amount;
            if (benchmark.period !== spend.period_type) {
              benchmarkAmount = this.convertPeriodAmount(benchmark.amount, benchmark.period, spend.period_type);
            }
            spend.benchmark_amount_at_time = benchmarkAmount;
          }
        }

        if (!spend.created_by) {
          // For existing data, use the first admin user as default
          const firstAdmin = this.mockData.users.find(u => u.is_admin);
          spend.created_by = firstAdmin ? firstAdmin.id : 1;
        }

        if (!spend.occupancy_at_time) {
          // For existing data, use the current company occupancy
          const benchmark = this.mockData.benchmarks.find(b => b.id === spend.benchmark_id);
          if (benchmark) {
            const company = this.mockData.companies.find(c => c.id === benchmark.company_id);
            spend.occupancy_at_time = company ? company.current_occupancy : 1;
          } else {
            spend.occupancy_at_time = 1;
          }
        }
      });
      return;
    }

    try {
      // Check if the benchmark_amount_at_time column exists
      const tableInfo = await this.db.getAllAsync("PRAGMA table_info(actual_spends)");
      const hasBenchmarkColumn = tableInfo.some(column => column.name === 'benchmark_amount_at_time');
      const hasCreatedByColumn = tableInfo.some(column => column.name === 'created_by');
      const hasOccupancyColumn = tableInfo.some(column => column.name === 'occupancy_at_time');

      if (!hasBenchmarkColumn) {
        console.log('Adding benchmark_amount_at_time column to actual_spends table');

        // Add the new column with a default value
        await this.db.runAsync('ALTER TABLE actual_spends ADD COLUMN benchmark_amount_at_time REAL DEFAULT 0');

        // Update existing records with the current benchmark amount
        const existingSpends = await this.db.getAllAsync(`
          SELECT as.id, as.benchmark_id, as.period_type, b.amount, b.period
          FROM actual_spends as
          JOIN benchmarks b ON as.benchmark_id = b.id
          WHERE as.benchmark_amount_at_time = 0
        `);

        for (const spend of existingSpends) {
          let benchmarkAmount = spend.amount;
          if (spend.period !== spend.period_type) {
            benchmarkAmount = this.convertPeriodAmount(spend.amount, spend.period, spend.period_type);
          }

          await this.db.runAsync(
            'UPDATE actual_spends SET benchmark_amount_at_time = ? WHERE id = ?',
            [benchmarkAmount, spend.id]
          );
        }

        console.log('Migration completed: Added benchmark_amount_at_time column');
      }

      if (!hasCreatedByColumn) {
        console.log('Adding created_by column to actual_spends table');

        // Add the new column with a default value (use first admin user)
        await this.db.runAsync('ALTER TABLE actual_spends ADD COLUMN created_by INTEGER DEFAULT 1');

        // Get the first admin user to use as default for existing records
        const firstAdmin = await this.db.getFirstAsync('SELECT id FROM users WHERE is_admin = 1 LIMIT 1');
        const defaultUserId = firstAdmin ? firstAdmin.id : 1;

        // Update existing records with the first admin user
        await this.db.runAsync(
          'UPDATE actual_spends SET created_by = ? WHERE created_by IS NULL OR created_by = 1',
          [defaultUserId]
        );

        console.log('Migration completed: Added created_by column');
      }

      if (!hasOccupancyColumn) {
        console.log('Adding occupancy_at_time column to actual_spends table');

        // Add the new column with a default value
        await this.db.runAsync('ALTER TABLE actual_spends ADD COLUMN occupancy_at_time INTEGER DEFAULT 1');

        // Update existing records with current company occupancy
        const existingSpends = await this.db.getAllAsync(`
          SELECT as.id, as.benchmark_id, b.company_id, c.current_occupancy
          FROM actual_spends as
          JOIN benchmarks b ON as.benchmark_id = b.id
          JOIN companies c ON b.company_id = c.id
          WHERE as.occupancy_at_time = 1
        `);

        for (const spend of existingSpends) {
          await this.db.runAsync(
            'UPDATE actual_spends SET occupancy_at_time = ? WHERE id = ?',
            [spend.current_occupancy || 1, spend.id]
          );
        }

        console.log('Migration completed: Added occupancy_at_time column');
      }
    } catch (error) {
      console.error('Migration error:', error);
    }
  }

  // Create dummy data for development
  async createDummyData() {
    if (this.isWeb) {
      // Create dummy company
      const company = {
        id: 1,
        account_number: '********',
        name: 'Demo Care Home',
        registration: 'REG123456',
        capacity: 50,
        current_occupancy: 42,
        capacity_type: 'beds',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      this.mockData.companies.push(company);

      // Create dummy admin user
      const user = {
        id: 1,
        email: '<EMAIL>',
        password: 'admin123',
        name: 'Demo Admin',
        is_admin: true,
        company_id: 1,
        permissions: [],
        is_active: true,
        last_login: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      this.mockData.users.push(user);

      // Create user-company relationship for the dummy user
      if (!this.mockData.userCompanies) {
        this.mockData.userCompanies = [];
      }

      const userCompanyRelation = {
        id: 1,
        user_id: 1,
        company_id: 1,
        is_admin: true,
        permissions: [],
        is_active: true,
        joined_at: new Date().toISOString()
      };
      this.mockData.userCompanies.push(userCompanyRelation);

      // Create some dummy benchmarks
      const benchmarks = [
        {
          id: 1,
          company_id: 1,
          name: 'Food & Beverage',
          amount: 25.50,
          period: 'daily',
          type: 'benchmark',
          currency: 'USD',
          is_active: 1,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          id: 2,
          company_id: 1,
          name: 'Utilities Budget',
          amount: 1200.00,
          period: 'monthly',
          type: 'budget',
          currency: 'USD',
          is_active: 1,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ];
      this.mockData.benchmarks.push(...benchmarks);

      // Create some dummy occupancy history
      const occupancyHistory = [
        {
          id: 1,
          company_id: 1,
          old_occupancy: 40,
          new_occupancy: 42,
          change_reason: 'New admission - Room 15',
          created_by: 1,
          created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString() // 2 days ago
        },
        {
          id: 2,
          company_id: 1,
          old_occupancy: 43,
          new_occupancy: 42,
          change_reason: 'Discharge - Room 8',
          created_by: 1,
          created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString() // 1 day ago
        }
      ];
      this.mockData.occupancyHistory.push(...occupancyHistory);

      // Create some dummy actual spend data
      const actualSpends = [
        {
          id: 1,
          benchmark_id: 1, // Food & Beverage
          amount: 1050.00,
          period_start: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 5 days ago
          period_end: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          period_type: 'daily',
          benchmark_amount_at_time: 1071.00, // 25.50 * 42 occupancy
          occupancy_at_time: 42,
          variance: -21.00,
          created_by: 1,
          created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          updated_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 2,
          benchmark_id: 1, // Food & Beverage
          amount: 1150.00,
          period_start: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 3 days ago
          period_end: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          period_type: 'daily',
          benchmark_amount_at_time: 1071.00, // 25.50 * 42 occupancy
          occupancy_at_time: 42,
          variance: 79.00,
          created_by: 1,
          created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
          updated_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 3,
          benchmark_id: 2, // Utilities Budget
          amount: 1180.00,
          period_start: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 1 day ago
          period_end: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          period_type: 'monthly',
          benchmark_amount_at_time: 50400.00, // 1200.00 * 42 occupancy
          occupancy_at_time: 42,
          variance: -49220.00,
          created_by: 1,
          created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
          updated_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
        }
      ];
      this.mockData.actualSpends.push(...actualSpends);

      console.log('Created dummy data for web development');
    }
  }

  async createDummyDataIfEmpty() {
    if (!this.isWeb) {
      // Check if we already have users
      const userCount = await this.db.getFirstAsync('SELECT COUNT(*) as count FROM users');

      if (userCount.count === 0) {
        console.log('Creating dummy data for mobile development');

        // Create dummy company
        const accountNumber = await this.generateAccountNumber();
        const companyId = await this.createCompany({
          accountNumber,
          name: 'Demo Care Home',
          registration: 'REG123456',
          capacity: 50,
          currentOccupancy: 42,
          capacityType: 'beds'
        });

        // Create dummy admin user
        const userId = await this.createUser({
          email: '<EMAIL>',
          password: 'admin123',
          name: 'Demo Admin',
          isAdmin: true,
          companyId,
          permissions: []
        });

        // Add user to company with admin role
        await this.addUserToCompany(userId, companyId, {
          isAdmin: true,
          permissions: [],
          isActive: true
        });

        // Create some dummy benchmarks
        await this.createBenchmark({
          companyId,
          name: 'Food & Beverage',
          amount: 25.50,
          period: 'daily',
          type: 'benchmark',
          currency: 'USD'
        });

        await this.createBenchmark({
          companyId,
          name: 'Utilities Budget',
          amount: 1200.00,
          period: 'monthly',
          type: 'budget',
          currency: 'USD'
        });

        console.log('Created dummy data for mobile development');
      }
    }
  }

  // User methods
  async createUser(userData) {
    const { email, password, name, isAdmin = false, companyId = null, permissions = [] } = userData;

    // Check plan limits before creating user (only if adding to existing company)
    if (companyId) {
      const limitCheck = await this.checkPlanLimits(companyId, 'users');
      if (!limitCheck.allowed) {
        throw new Error(`User limit reached. Your plan allows ${limitCheck.limit} users and you currently have ${limitCheck.current}.`);
      }
    }

    if (this.isWeb) {
      const user = {
        id: this.mockData.users.length + 1,
        email,
        password,
        name,
        is_admin: isAdmin,
        company_id: companyId,
        permissions,
        is_active: true,
        last_login: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      this.mockData.users.push(user);
      return user.id;
    }

    const result = await this.db.runAsync(
      'INSERT INTO users (email, password, name, is_admin, company_id, permissions, is_active) VALUES (?, ?, ?, ?, ?, ?, ?)',
      [email, password, name, isAdmin ? 1 : 0, companyId, JSON.stringify(permissions), 1]
    );

    return result.lastInsertRowId;
  }

  async getUserByEmail(email) {
    if (this.isWeb) {
      const user = this.mockData.users.find(u => u.email === email);
      if (user) {
        return {
          ...user,
          permissions: user.permissions || [],
          is_admin: Boolean(user.is_admin),
          is_active: user.is_active !== undefined ? Boolean(user.is_active) : true
        };
      }
      return null;
    }

    const result = await this.db.getFirstAsync(
      'SELECT * FROM users WHERE email = ?',
      [email]
    );

    if (result) {
      result.permissions = JSON.parse(result.permissions || '[]');
      result.is_admin = Boolean(result.is_admin);
      result.is_active = Boolean(result.is_active);
    }

    return result;
  }

  async getUserAccountsByEmail(email) {
    if (this.isWeb) {
      const users = this.mockData.users.filter(u => u.email === email);
      return users.map(user => ({
        ...user,
        permissions: user.permissions || [],
        is_admin: Boolean(user.is_admin),
        is_active: user.is_active !== undefined ? Boolean(user.is_active) : true
      }));
    }

    const users = await this.db.getAllAsync(
      'SELECT * FROM users WHERE email = ? ORDER BY last_login DESC',
      [email]
    );

    return users.map(user => ({
      ...user,
      permissions: JSON.parse(user.permissions || '[]'),
      is_admin: Boolean(user.is_admin),
      is_active: Boolean(user.is_active)
    }));
  }

  async getUserById(id) {
    if (this.isWeb) {
      const user = this.mockData.users.find(u => u.id === id);
      if (user) {
        return {
          ...user,
          permissions: user.permissions || [],
          is_admin: Boolean(user.is_admin),
          is_active: user.is_active !== undefined ? Boolean(user.is_active) : true
        };
      }
      return null;
    }

    const result = await this.db.getFirstAsync(
      'SELECT * FROM users WHERE id = ?',
      [id]
    );

    if (result) {
      result.permissions = JSON.parse(result.permissions || '[]');
      result.is_admin = Boolean(result.is_admin);
      result.is_active = Boolean(result.is_active);
    }

    return result;
  }

  async updateUser(id, userData) {
    const { name, isAdmin, permissions } = userData;

    if (this.isWeb) {
      const userIndex = this.mockData.users.findIndex(u => u.id === id);
      if (userIndex !== -1) {
        this.mockData.users[userIndex] = {
          ...this.mockData.users[userIndex],
          name,
          is_admin: isAdmin,
          permissions: permissions || [],
          updated_at: new Date().toISOString()
        };
      }
      return;
    }

    await this.db.runAsync(
      'UPDATE users SET name = ?, is_admin = ?, permissions = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [name, isAdmin ? 1 : 0, JSON.stringify(permissions || []), id]
    );
  }

  async deleteUser(id) {
    if (this.isWeb) {
      this.mockData.users = this.mockData.users.filter(u => u.id !== id);
      return;
    }

    await this.db.runAsync('DELETE FROM users WHERE id = ?', [id]);
  }

  async getUsersByCompany(companyId) {
    if (this.isWeb) {
      const companyUsers = this.mockData.users.filter(u => u.company_id === companyId);
      return companyUsers.map(user => ({
        ...user,
        permissions: user.permissions || [],
        is_admin: Boolean(user.is_admin),
        is_active: user.is_active !== undefined ? Boolean(user.is_active) : true
      }));
    }

    const result = await this.db.getAllAsync(
      'SELECT * FROM users WHERE company_id = ?',
      [companyId]
    );

    return result.map(user => ({
      ...user,
      permissions: JSON.parse(user.permissions || '[]'),
      is_admin: Boolean(user.is_admin),
      is_active: Boolean(user.is_active)
    }));
  }

  // Enhanced admin functions for user management
  async updateUserProfile(id, profileData) {
    const { name, email } = profileData;

    if (this.isWeb) {
      const userIndex = this.mockData.users.findIndex(u => u.id === id);
      if (userIndex !== -1) {
        this.mockData.users[userIndex] = {
          ...this.mockData.users[userIndex],
          name,
          email,
          updated_at: new Date().toISOString()
        };
      }
      return;
    }

    await this.db.runAsync(
      'UPDATE users SET name = ?, email = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [name, email, id]
    );
  }

  async resetUserPassword(id, newPassword) {
    if (this.isWeb) {
      const userIndex = this.mockData.users.findIndex(u => u.id === id);
      if (userIndex !== -1) {
        this.mockData.users[userIndex] = {
          ...this.mockData.users[userIndex],
          password: newPassword,
          updated_at: new Date().toISOString()
        };
      }
      return;
    }

    await this.db.runAsync(
      'UPDATE users SET password = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [newPassword, id]
    );
  }

  async changeUserPassword(userId, currentPassword, newPassword) {
    if (this.isWeb) {
      const userIndex = this.mockData.users.findIndex(u => u.id === userId);
      if (userIndex === -1) {
        throw new Error('User not found');
      }

      const user = this.mockData.users[userIndex];
      if (user.password !== currentPassword) {
        throw new Error('Current password is incorrect');
      }

      this.mockData.users[userIndex] = {
        ...user,
        password: newPassword,
        updated_at: new Date().toISOString()
      };
      return;
    }

    // First verify current password
    const user = await this.db.getFirstAsync(
      'SELECT password FROM users WHERE id = ?',
      [userId]
    );

    if (!user) {
      throw new Error('User not found');
    }

    if (user.password !== currentPassword) {
      throw new Error('Current password is incorrect');
    }

    // Update password
    await this.db.runAsync(
      'UPDATE users SET password = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [newPassword, userId]
    );
  }

  async toggleUserStatus(id, isActive) {
    if (this.isWeb) {
      const userIndex = this.mockData.users.findIndex(u => u.id === id);
      if (userIndex !== -1) {
        this.mockData.users[userIndex] = {
          ...this.mockData.users[userIndex],
          is_active: isActive,
          updated_at: new Date().toISOString()
        };
      }
      return;
    }

    await this.db.runAsync(
      'UPDATE users SET is_active = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [isActive ? 1 : 0, id]
    );
  }

  async updateLastLogin(id) {
    if (this.isWeb) {
      const userIndex = this.mockData.users.findIndex(u => u.id === id);
      if (userIndex !== -1) {
        this.mockData.users[userIndex] = {
          ...this.mockData.users[userIndex],
          last_login: new Date().toISOString()
        };
      }
      return;
    }

    await this.db.runAsync(
      'UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?',
      [id]
    );
  }

  async searchUsers(companyId, searchTerm) {
    if (this.isWeb) {
      const companyUsers = this.mockData.users.filter(u => u.company_id === companyId);
      if (!searchTerm) return companyUsers;

      const term = searchTerm.toLowerCase();
      return companyUsers.filter(user =>
        user.name.toLowerCase().includes(term) ||
        user.email.toLowerCase().includes(term)
      ).map(user => ({
        ...user,
        permissions: user.permissions || [],
        is_admin: Boolean(user.is_admin),
        is_active: user.is_active !== undefined ? Boolean(user.is_active) : true
      }));
    }

    const query = searchTerm
      ? 'SELECT * FROM users WHERE company_id = ? AND (name LIKE ? OR email LIKE ?)'
      : 'SELECT * FROM users WHERE company_id = ?';

    const params = searchTerm
      ? [companyId, `%${searchTerm}%`, `%${searchTerm}%`]
      : [companyId];

    const result = await this.db.getAllAsync(query, params);

    return result.map(user => ({
      ...user,
      permissions: JSON.parse(user.permissions || '[]'),
      is_admin: Boolean(user.is_admin),
      is_active: Boolean(user.is_active)
    }));
  }

  async bulkDeleteUsers(userIds) {
    if (this.isWeb) {
      this.mockData.users = this.mockData.users.filter(u => !userIds.includes(u.id));
      return;
    }

    const placeholders = userIds.map(() => '?').join(',');
    await this.db.runAsync(
      `DELETE FROM users WHERE id IN (${placeholders})`,
      userIds
    );
  }

  async bulkToggleAdmin(userIds, isAdmin) {
    if (this.isWeb) {
      userIds.forEach(id => {
        const userIndex = this.mockData.users.findIndex(u => u.id === id);
        if (userIndex !== -1) {
          this.mockData.users[userIndex] = {
            ...this.mockData.users[userIndex],
            is_admin: isAdmin,
            updated_at: new Date().toISOString()
          };
        }
      });
      return;
    }

    const placeholders = userIds.map(() => '?').join(',');
    await this.db.runAsync(
      `UPDATE users SET is_admin = ?, updated_at = CURRENT_TIMESTAMP WHERE id IN (${placeholders})`,
      [isAdmin ? 1 : 0, ...userIds]
    );
  }

  async getUserStats(companyId) {
    if (this.isWeb) {
      const companyUsers = this.mockData.users.filter(u => u.company_id === companyId);
      return {
        total: companyUsers.length,
        active: companyUsers.filter(u => u.is_active !== false).length,
        inactive: companyUsers.filter(u => u.is_active === false).length,
        admins: companyUsers.filter(u => u.is_admin).length
      };
    }

    const stats = await this.db.getFirstAsync(`
      SELECT
        COUNT(*) as total,
        SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active,
        SUM(CASE WHEN is_active = 0 THEN 1 ELSE 0 END) as inactive,
        SUM(CASE WHEN is_admin = 1 THEN 1 ELSE 0 END) as admins
      FROM users
      WHERE company_id = ?
    `, [companyId]);

    return stats;
  }

  // User-Company relationship methods
  async addUserToCompany(userId, companyId, options = {}) {
    const { isAdmin = false, permissions = [], isActive = true } = options;

    if (this.isWeb) {
      // For web, we'll simulate this with a new array in mockData
      if (!this.mockData.userCompanies) {
        this.mockData.userCompanies = [];
      }

      const relationship = {
        id: this.mockData.userCompanies.length + 1,
        user_id: userId,
        company_id: companyId,
        is_admin: isAdmin,
        permissions: permissions,
        is_active: isActive,
        joined_at: new Date().toISOString()
      };

      this.mockData.userCompanies.push(relationship);
      return relationship.id;
    }

    const result = await this.db.runAsync(
      'INSERT INTO user_companies (user_id, company_id, is_admin, permissions, is_active) VALUES (?, ?, ?, ?, ?)',
      [userId, companyId, isAdmin ? 1 : 0, JSON.stringify(permissions), isActive ? 1 : 0]
    );

    return result.lastInsertRowId;
  }

  async getUserCompanies(userId) {
    if (this.isWeb) {
      if (!this.mockData.userCompanies) {
        this.mockData.userCompanies = [];
      }

      const userCompanyRelations = this.mockData.userCompanies.filter(uc => uc.user_id === userId);
      const companies = [];

      for (const relation of userCompanyRelations) {
        const company = this.mockData.companies.find(c => c.id === relation.company_id);
        if (company) {
          companies.push({
            ...company,
            user_role: {
              is_admin: relation.is_admin,
              permissions: relation.permissions || [],
              is_active: relation.is_active,
              joined_at: relation.joined_at
            }
          });
        }
      }

      return companies;
    }

    const result = await this.db.getAllAsync(`
      SELECT c.*, uc.is_admin, uc.permissions, uc.is_active as user_is_active, uc.joined_at
      FROM companies c
      INNER JOIN user_companies uc ON c.id = uc.company_id
      WHERE uc.user_id = ? AND uc.is_active = 1
      ORDER BY c.name
    `, [userId]);

    return result.map(row => ({
      id: row.id,
      account_number: row.account_number,
      name: row.name,
      registration: row.registration,
      capacity: row.capacity,
      current_occupancy: row.current_occupancy,
      capacity_type: row.capacity_type,
      created_at: row.created_at,
      updated_at: row.updated_at,
      user_role: {
        is_admin: Boolean(row.is_admin),
        permissions: JSON.parse(row.permissions || '[]'),
        is_active: Boolean(row.user_is_active),
        joined_at: row.joined_at
      }
    }));
  }

  async updateUserCompanyRole(userId, companyId, options = {}) {
    const { isAdmin, permissions, isActive } = options;

    if (this.isWeb) {
      if (!this.mockData.userCompanies) {
        this.mockData.userCompanies = [];
      }

      const relationIndex = this.mockData.userCompanies.findIndex(
        uc => uc.user_id === userId && uc.company_id === companyId
      );

      if (relationIndex !== -1) {
        if (isAdmin !== undefined) this.mockData.userCompanies[relationIndex].is_admin = isAdmin;
        if (permissions !== undefined) this.mockData.userCompanies[relationIndex].permissions = permissions;
        if (isActive !== undefined) this.mockData.userCompanies[relationIndex].is_active = isActive;
      }
      return;
    }

    const updates = [];
    const values = [];

    if (isAdmin !== undefined) {
      updates.push('is_admin = ?');
      values.push(isAdmin ? 1 : 0);
    }
    if (permissions !== undefined) {
      updates.push('permissions = ?');
      values.push(JSON.stringify(permissions));
    }
    if (isActive !== undefined) {
      updates.push('is_active = ?');
      values.push(isActive ? 1 : 0);
    }

    if (updates.length > 0) {
      values.push(userId, companyId);
      await this.db.runAsync(
        `UPDATE user_companies SET ${updates.join(', ')} WHERE user_id = ? AND company_id = ?`,
        values
      );
    }
  }

  async removeUserFromCompany(userId, companyId) {
    if (this.isWeb) {
      if (!this.mockData.userCompanies) {
        this.mockData.userCompanies = [];
      }

      this.mockData.userCompanies = this.mockData.userCompanies.filter(
        uc => !(uc.user_id === userId && uc.company_id === companyId)
      );
      return;
    }

    await this.db.runAsync(
      'DELETE FROM user_companies WHERE user_id = ? AND company_id = ?',
      [userId, companyId]
    );
  }

  // Invitation methods
  async createInvitation(invitationData) {
    const { email, companyId, invitedBy, permissions = [] } = invitationData;

    // Generate unique token
    const token = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);

    // Set expiration (7 days from now)
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7);

    if (this.isWeb) {
      if (!this.mockData.invitations) {
        this.mockData.invitations = [];
      }

      const invitation = {
        id: this.mockData.invitations.length + 1,
        email,
        company_id: companyId,
        invited_by: invitedBy,
        permissions: permissions,
        token,
        status: 'pending',
        expires_at: expiresAt.toISOString(),
        created_at: new Date().toISOString()
      };

      this.mockData.invitations.push(invitation);
      return invitation.id;
    }

    const result = await this.db.runAsync(
      'INSERT INTO invitations (email, company_id, invited_by, permissions, token, expires_at) VALUES (?, ?, ?, ?, ?, ?)',
      [email, companyId, invitedBy, JSON.stringify(permissions), token, expiresAt.toISOString()]
    );

    return result.lastInsertRowId;
  }

  async getInvitationByToken(token) {
    if (this.isWeb) {
      if (!this.mockData.invitations) {
        this.mockData.invitations = [];
      }

      const invitation = this.mockData.invitations.find(inv => inv.token === token);
      if (!invitation) return null;

      // Check if expired
      if (new Date(invitation.expires_at) < new Date()) {
        invitation.status = 'expired';
        return invitation;
      }

      return invitation;
    }

    const invitation = await this.db.getFirstAsync(
      'SELECT * FROM invitations WHERE token = ?',
      [token]
    );

    if (!invitation) return null;

    // Check if expired
    if (new Date(invitation.expires_at) < new Date()) {
      await this.db.runAsync(
        'UPDATE invitations SET status = ? WHERE id = ?',
        ['expired', invitation.id]
      );
      return { ...invitation, status: 'expired' };
    }

    return {
      ...invitation,
      permissions: JSON.parse(invitation.permissions || '[]')
    };
  }

  async acceptInvitation(token) {
    const invitation = await this.getInvitationByToken(token);
    if (!invitation || invitation.status !== 'pending') {
      throw new Error('Invalid or expired invitation');
    }

    // Check if user exists
    const existingUser = await this.getUserByEmail(invitation.email);
    if (!existingUser) {
      throw new Error('User account not found. Please create an account first.');
    }

    // Add user to company
    await this.addUserToCompany(existingUser.id, invitation.company_id, {
      isAdmin: false, // Invitations are for regular users by default
      permissions: invitation.permissions,
      isActive: true
    });

    // Mark invitation as accepted
    if (this.isWeb) {
      const invitationIndex = this.mockData.invitations.findIndex(inv => inv.token === token);
      if (invitationIndex !== -1) {
        this.mockData.invitations[invitationIndex].status = 'accepted';
      }
    } else {
      await this.db.runAsync(
        'UPDATE invitations SET status = ? WHERE token = ?',
        ['accepted', token]
      );
    }

    return { success: true, companyId: invitation.company_id };
  }

  async getCompanyInvitations(companyId) {
    if (this.isWeb) {
      if (!this.mockData.invitations) {
        this.mockData.invitations = [];
      }

      return this.mockData.invitations.filter(inv => inv.company_id === companyId);
    }

    const invitations = await this.db.getAllAsync(
      'SELECT * FROM invitations WHERE company_id = ? ORDER BY created_at DESC',
      [companyId]
    );

    return invitations.map(inv => ({
      ...inv,
      permissions: JSON.parse(inv.permissions || '[]')
    }));
  }

  async getUserInvitations(email) {
    if (this.isWeb) {
      if (!this.mockData.invitations) {
        this.mockData.invitations = [];
      }

      const userInvitations = this.mockData.invitations.filter(inv => inv.email === email);

      // Add company details to invitations
      return userInvitations.map(inv => {
        const company = this.mockData.companies.find(c => c.id === inv.company_id);
        return {
          ...inv,
          company_name: company?.name || 'Unknown Company',
          company_registration: company?.registration || null
        };
      });
    }

    const invitations = await this.db.getAllAsync(`
      SELECT i.*, c.name as company_name, c.registration as company_registration
      FROM invitations i
      LEFT JOIN companies c ON i.company_id = c.id
      WHERE i.email = ?
      ORDER BY i.created_at DESC
    `, [email]);

    return invitations.map(inv => ({
      ...inv,
      permissions: JSON.parse(inv.permissions || '[]')
    }));
  }

  async declineInvitation(token) {
    if (this.isWeb) {
      if (!this.mockData.invitations) {
        this.mockData.invitations = [];
      }

      const invitationIndex = this.mockData.invitations.findIndex(inv => inv.token === token);
      if (invitationIndex !== -1) {
        this.mockData.invitations[invitationIndex].status = 'declined';
      }
      return;
    }

    await this.db.runAsync(
      'UPDATE invitations SET status = ? WHERE token = ?',
      ['declined', token]
    );
  }

  // Company methods
  async createCompany(companyData) {
    const {
      accountNumber,
      name,
      registration,
      capacity,
      currentOccupancy,
      capacityType,
      subscription_plan = 'trial',
      subscription_status = 'trial',
      subscription_start_date = null,
      subscription_end_date = null,
      trial_end_date = null,
      billing_cycle = 'monthly'
    } = companyData;

    // Check for duplicate registration number
    if (registration && registration.trim()) {
      const registrationExists = await this.checkRegistrationNumberExists(registration);
      if (registrationExists) {
        throw new Error('A company with this registration number already exists');
      }
    }

    // Set trial end date only if no subscription is provided
    let finalTrialEndDate = trial_end_date;
    if (!finalTrialEndDate && subscription_status === 'trial') {
      const trialEndDate = new Date();
      trialEndDate.setDate(trialEndDate.getDate() + 30);
      finalTrialEndDate = trialEndDate.toISOString();
    }

    if (this.isWeb) {
      const company = {
        id: this.mockData.companies.length + 1,
        account_number: accountNumber,
        name,
        registration,
        capacity,
        current_occupancy: currentOccupancy || 0,
        capacity_type: capacityType,
        subscription_plan,
        subscription_status,
        subscription_start_date,
        subscription_end_date,
        trial_end_date: finalTrialEndDate,
        billing_cycle,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      this.mockData.companies.push(company);
      return company.id;
    }

    const result = await this.db.runAsync(
      'INSERT INTO companies (account_number, name, registration, capacity, current_occupancy, capacity_type, subscription_plan, subscription_status, subscription_start_date, subscription_end_date, trial_end_date, billing_cycle) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
      [accountNumber, name, registration, capacity, currentOccupancy || 0, capacityType, subscription_plan, subscription_status, subscription_start_date, subscription_end_date, finalTrialEndDate, billing_cycle]
    );

    return result.lastInsertRowId;
  }

  async getCompanyById(id) {
    if (this.isWeb) {
      const company = this.mockData.companies.find(c => c.id === id);
      return company || null;
    }

    return await this.db.getFirstAsync(
      'SELECT * FROM companies WHERE id = ?',
      [id]
    );
  }

  async getAllCompanies() {
    if (this.isWeb) {
      return this.mockData.companies;
    }

    return await this.db.getAllAsync('SELECT * FROM companies');
  }

  async updateCompany(id, companyData) {
    const { name, registration, capacity, currentOccupancy, capacityType } = companyData;

    // Check for duplicate registration number (excluding current company)
    if (registration && registration.trim()) {
      const registrationExists = await this.checkRegistrationNumberExists(registration, id);
      if (registrationExists) {
        throw new Error('A company with this registration number already exists');
      }
    }

    if (this.isWeb) {
      const companyIndex = this.mockData.companies.findIndex(c => c.id === id);
      if (companyIndex !== -1) {
        this.mockData.companies[companyIndex] = {
          ...this.mockData.companies[companyIndex],
          name,
          registration,
          capacity,
          current_occupancy: currentOccupancy,
          capacity_type: capacityType,
          updated_at: new Date().toISOString()
        };
      }
      return;
    }

    await this.db.runAsync(
      'UPDATE companies SET name = ?, registration = ?, capacity = ?, current_occupancy = ?, capacity_type = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [name, registration, capacity, currentOccupancy, capacityType, id]
    );
  }

  async generateAccountNumber() {
    // Generate a unique 8-digit account number
    let accountNumber;
    let exists = true;

    while (exists) {
      accountNumber = Math.floor(******** + Math.random() * ********).toString();

      if (this.isWeb) {
        const existing = this.mockData.companies.find(c => c.account_number === accountNumber);
        exists = !!existing;
      } else {
        const existing = await this.db.getFirstAsync(
          'SELECT id FROM companies WHERE account_number = ?',
          [accountNumber]
        );
        exists = !!existing;
      }
    }

    return accountNumber;
  }

  async checkRegistrationNumberExists(registrationNumber, excludeCompanyId = null) {
    // Skip check if registration number is empty
    if (!registrationNumber || !registrationNumber.trim()) {
      return false;
    }

    const trimmedRegistration = registrationNumber.trim();

    if (this.isWeb) {
      const existing = this.mockData.companies.find(c =>
        c.registration &&
        c.registration.toLowerCase() === trimmedRegistration.toLowerCase() &&
        c.id !== excludeCompanyId
      );
      return !!existing;
    } else {
      let query = 'SELECT id FROM companies WHERE LOWER(registration) = LOWER(?)';
      let params = [trimmedRegistration];

      if (excludeCompanyId) {
        query += ' AND id != ?';
        params.push(excludeCompanyId);
      }

      const existing = await this.db.getFirstAsync(query, params);
      return !!existing;
    }
  }

  // Plan limits configuration
  getPlanLimits(planType) {
    const limits = {
      trial: {
        users: 5,
        benchmarks: 5,
        canUploadHistoricalData: true,
        canExportData: true
      },
      starter: {
        users: 5,
        benchmarks: 5,
        canUploadHistoricalData: false,
        canExportData: false
      },
      professional: {
        users: 10,
        benchmarks: 10,
        canUploadHistoricalData: true,
        canExportData: true
      },
      enterprise: {
        users: -1, // unlimited
        benchmarks: 10,
        canUploadHistoricalData: true,
        canExportData: true
      }
    };

    return limits[planType] || limits.trial;
  }

  async checkPlanLimits(companyId, limitType) {
    try {
      const company = await this.getCompanyById(companyId);
      if (!company) {
        throw new Error('Company not found');
      }

      const planLimits = this.getPlanLimits(company.subscription_plan);

      switch (limitType) {
        case 'users':
          if (planLimits.users === -1) return { allowed: true, limit: -1, current: 0 };

          const currentUsers = await this.getUsersByCompany(companyId);
          return {
            allowed: currentUsers.length < planLimits.users,
            limit: planLimits.users,
            current: currentUsers.length
          };

        case 'benchmarks':
          if (planLimits.benchmarks === -1) return { allowed: true, limit: -1, current: 0 };

          const currentBenchmarks = await this.getBenchmarksByCompany(companyId);
          return {
            allowed: currentBenchmarks.length < planLimits.benchmarks,
            limit: planLimits.benchmarks,
            current: currentBenchmarks.length
          };

        case 'uploadHistoricalData':
          return {
            allowed: planLimits.canUploadHistoricalData,
            limit: planLimits.canUploadHistoricalData ? 1 : 0,
            current: 0
          };

        case 'exportData':
          return {
            allowed: planLimits.canExportData,
            limit: planLimits.canExportData ? 1 : 0,
            current: 0
          };

        default:
          return { allowed: true, limit: -1, current: 0 };
      }
    } catch (error) {
      console.error('Error checking plan limits:', error);
      return { allowed: false, limit: 0, current: 0, error: error.message };
    }
  }

  // Subscription methods
  async updateCompanySubscription(companyId, subscriptionData) {
    const { plan, status, startDate, endDate, billingCycle } = subscriptionData;

    if (this.isWeb) {
      const companyIndex = this.mockData.companies.findIndex(c => c.id === companyId);
      if (companyIndex !== -1) {
        this.mockData.companies[companyIndex] = {
          ...this.mockData.companies[companyIndex],
          subscription_plan: plan,
          subscription_status: status,
          subscription_start_date: startDate,
          subscription_end_date: endDate,
          billing_cycle: billingCycle,
          updated_at: new Date().toISOString()
        };
      }
      return;
    }

    await this.db.runAsync(
      'UPDATE companies SET subscription_plan = ?, subscription_status = ?, subscription_start_date = ?, subscription_end_date = ?, billing_cycle = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [plan, status, startDate, endDate, billingCycle, companyId]
    );
  }

  async isSubscriptionActive(companyId) {
    const company = await this.getCompanyById(companyId);
    if (!company) return false;

    const now = new Date();

    // Check if trial is still active
    if (company.subscription_status === 'trial' && company.trial_end_date) {
      const trialEnd = new Date(company.trial_end_date);
      return now <= trialEnd;
    }

    // Check if paid subscription is active
    if (company.subscription_status === 'active' && company.subscription_end_date) {
      const subscriptionEnd = new Date(company.subscription_end_date);
      return now <= subscriptionEnd;
    }

    return false;
  }

  async getSubscriptionStatus(companyId) {
    const company = await this.getCompanyById(companyId);
    if (!company) return null;

    const now = new Date();
    const isActive = await this.isSubscriptionActive(companyId);

    return {
      plan: company.subscription_plan,
      status: company.subscription_status,
      isActive,
      trialEndDate: company.trial_end_date,
      subscriptionStartDate: company.subscription_start_date,
      subscriptionEndDate: company.subscription_end_date,
      billingCycle: company.billing_cycle
    };
  }

  // Check if any users exist (for first admin setup)
  async hasUsers() {
    if (this.isWeb) {
      return this.mockData.users.length > 0;
    }

    const result = await this.db.getFirstAsync('SELECT COUNT(*) as count FROM users');
    return result.count > 0;
  }

  // Benchmark methods
  async createBenchmark(benchmarkData) {
    const { companyId, name, amount, period, type, currency = 'USD' } = benchmarkData;

    console.log('DatabaseService.createBenchmark called with:', benchmarkData);

    // Check plan limits before creating benchmark
    const limitCheck = await this.checkPlanLimits(companyId, 'benchmarks');
    if (!limitCheck.allowed) {
      throw new Error(`Benchmark limit reached. Your plan allows ${limitCheck.limit} benchmarks and you currently have ${limitCheck.current}.`);
    }

    if (this.isWeb) {
      const benchmark = {
        id: this.mockData.benchmarks.length + 1,
        company_id: companyId,
        name,
        amount,
        period,
        type,
        currency,
        is_active: 1,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      this.mockData.benchmarks.push(benchmark);
      console.log('Created benchmark on web:', benchmark);
      console.log('Updated mockData.benchmarks:', this.mockData.benchmarks);
      return benchmark.id;
    }

    const result = await this.db.runAsync(
      'INSERT INTO benchmarks (company_id, name, amount, period, type, currency) VALUES (?, ?, ?, ?, ?, ?)',
      [companyId, name, amount, period, type, currency]
    );

    return result.lastInsertRowId;
  }

  async getBenchmarksByCompany(companyId) {
    if (this.isWeb) {
      const benchmarks = this.mockData.benchmarks
        .filter(b => b.company_id === companyId && b.is_active === 1)
        .sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
      console.log('Retrieved benchmarks for company on web:', benchmarks);
      return benchmarks;
    }

    return await this.db.getAllAsync(
      'SELECT * FROM benchmarks WHERE company_id = ? AND is_active = 1 ORDER BY created_at DESC',
      [companyId]
    );
  }

  async getBenchmarkById(id) {
    if (this.isWeb) {
      const benchmark = this.mockData.benchmarks.find(b => b.id === id);
      console.log('Retrieved benchmark by ID on web:', benchmark);
      return benchmark || null;
    }

    return await this.db.getFirstAsync(
      'SELECT * FROM benchmarks WHERE id = ?',
      [id]
    );
  }

  async updateBenchmark(id, benchmarkData) {
    const { name, amount, period, type, currency = 'USD' } = benchmarkData;

    console.log('DatabaseService.updateBenchmark called with:', { id, ...benchmarkData });

    if (this.isWeb) {
      const benchmarkIndex = this.mockData.benchmarks.findIndex(b => b.id === id);
      if (benchmarkIndex !== -1) {
        this.mockData.benchmarks[benchmarkIndex] = {
          ...this.mockData.benchmarks[benchmarkIndex],
          name,
          amount,
          period,
          type,
          currency,
          updated_at: new Date().toISOString()
        };
        console.log('Updated benchmark on web:', this.mockData.benchmarks[benchmarkIndex]);
      }
      return;
    }

    await this.db.runAsync(
      'UPDATE benchmarks SET name = ?, amount = ?, period = ?, type = ?, currency = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [name, amount, period, type, currency, id]
    );
  }

  async deactivateBenchmark(id) {
    await this.db.runAsync(
      'UPDATE benchmarks SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [id]
    );
  }

  // Actual spend methods
  async createActualSpend(spendData) {
    const { benchmarkId, amount, periodStart, periodEnd, periodType, createdBy, benchmarkAmountAtTime: customBenchmarkAmount, occupancyAtTime: customOccupancy } = spendData;

    console.log('DatabaseService.createActualSpend called with:', spendData);

    // Get benchmark to calculate variance
    const benchmark = await this.getBenchmarkById(benchmarkId);
    let variance = 0;
    let benchmarkAmountAtTime = customBenchmarkAmount || 0;
    let occupancyAtTime = customOccupancy || 1;

    if (benchmark) {
      // Get company for occupancy calculation (only if not provided)
      if (!customOccupancy) {
        const company = await this.getCompanyById(benchmark.company_id);
        occupancyAtTime = company ? company.current_occupancy : 1;
      }

      // Only calculate benchmark amount if not provided
      if (!customBenchmarkAmount) {
        // Convert benchmark amount to match period type
        benchmarkAmountAtTime = benchmark.amount;
        if (benchmark.period !== periodType) {
          benchmarkAmountAtTime = this.convertPeriodAmount(benchmark.amount, benchmark.period, periodType);
        }

        // Multiply by current occupancy
        const company = await this.getCompanyById(benchmark.company_id);
        if (company && company.current_occupancy) {
          benchmarkAmountAtTime = this.calculateBenchmarkWithOccupancy(benchmarkAmountAtTime, company.current_occupancy);
        }
      }

      variance = amount - benchmarkAmountAtTime;
      console.log('Calculated variance:', variance, 'from actual:', amount, 'vs benchmark (with occupancy):', benchmarkAmountAtTime);
    }

    if (this.isWeb) {
      const actualSpend = {
        id: this.mockData.actualSpends.length + 1,
        benchmark_id: benchmarkId,
        amount,
        period_start: periodStart,
        period_end: periodEnd,
        period_type: periodType,
        benchmark_amount_at_time: benchmarkAmountAtTime,
        occupancy_at_time: occupancyAtTime,
        variance,
        created_by: createdBy || 1,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      this.mockData.actualSpends.push(actualSpend);
      console.log('Created actual spend on web:', actualSpend);

      // Check if variance exceeds benchmark and create notification
      if (variance > 0) {
        await this.createNotification({
          companyId: benchmark.company_id,
          benchmarkId,
          type: benchmark.type === 'budget' ? 'budget_exceeded' : 'benchmark_exceeded',
          message: `${benchmark.type === 'budget' ? 'Budget' : 'Benchmark'} "${benchmark.name}" exceeded by ${variance.toFixed(2)}`,
          actualAmount: amount,
          benchmarkAmount: benchmarkAmountAtTime,
          variance: variance
        });
      }

      return actualSpend.id;
    }

    const result = await this.db.runAsync(
      'INSERT INTO actual_spends (benchmark_id, amount, period_start, period_end, period_type, benchmark_amount_at_time, occupancy_at_time, variance, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
      [benchmarkId, amount, periodStart, periodEnd, periodType, benchmarkAmountAtTime, occupancyAtTime, variance, createdBy || 1]
    );

    // Check if variance exceeds benchmark and create notification
    if (variance > 0) {
      await this.createNotification({
        companyId: benchmark.company_id,
        benchmarkId,
        type: benchmark.type === 'budget' ? 'budget_exceeded' : 'benchmark_exceeded',
        message: `${benchmark.type === 'budget' ? 'Budget' : 'Benchmark'} "${benchmark.name}" exceeded by ${variance.toFixed(2)}`,
        actualAmount: amount,
        benchmarkAmount: benchmarkAmountAtTime,
        variance: variance
      });
    }

    return result.lastInsertRowId;
  }

  async getActualSpendsByBenchmark(benchmarkId, limit = null) {
    if (this.isWeb) {
      let spends = this.mockData.actualSpends
        .filter(s => s.benchmark_id === benchmarkId)
        .sort((a, b) => new Date(b.period_start) - new Date(a.period_start));

      // Add user information to each spend
      spends = spends.map(spend => {
        const user = this.mockData.users.find(u => u.id === spend.created_by);
        return {
          ...spend,
          created_by_name: user ? user.name : 'Unknown User'
        };
      });

      if (limit) {
        spends = spends.slice(0, limit);
      }

      console.log('Retrieved actual spends for benchmark on web:', spends);
      return spends;
    }

    const query = limit
      ? `SELECT as.*, u.name as created_by_name
         FROM actual_spends as
         LEFT JOIN users u ON as.created_by = u.id
         WHERE as.benchmark_id = ?
         ORDER BY as.period_start DESC LIMIT ?`
      : `SELECT as.*, u.name as created_by_name
         FROM actual_spends as
         LEFT JOIN users u ON as.created_by = u.id
         WHERE as.benchmark_id = ?
         ORDER BY as.period_start DESC`;

    const params = limit ? [benchmarkId, limit] : [benchmarkId];

    return await this.db.getAllAsync(query, params);
  }

  async deleteActualSpend(spendId, userId) {
    try {
      console.log(`deleteActualSpend called with spendId: ${spendId}, userId: ${userId}`);
      console.log(`Platform: ${this.isWeb ? 'web' : 'mobile'}`);

      // Check if user is admin
      const user = await this.getUserById(userId);
      console.log('Retrieved user:', user);

      if (!user || !user.is_admin) {
        console.log('User is not admin or not found');
        throw new Error("Only admin users can delete historical data entries");
      }

      if (this.isWeb) {
        // For web/mock data
        console.log('Deleting from mock data...');
        console.log('Current actualSpends count:', this.mockData.actualSpends.length);

        const spendIndex = this.mockData.actualSpends.findIndex(s => s.id === spendId);
        console.log('Found spend at index:', spendIndex);

        if (spendIndex === -1) {
          throw new Error("Entry not found or already deleted");
        }

        this.mockData.actualSpends.splice(spendIndex, 1);
        console.log('New actualSpends count:', this.mockData.actualSpends.length);
        console.log(`Admin user ${userId} deleted actual spend entry ${spendId} (web)`);
        return { success: true, deletedId: spendId };
      }

      // For mobile/SQLite
      console.log('Deleting from SQLite...');
      const result = await this.db.runAsync(
        'DELETE FROM actual_spends WHERE id = ?',
        [spendId]
      );

      console.log('SQLite delete result:', result);

      if (result.changes === 0) {
        throw new Error("Entry not found or already deleted");
      }

      console.log(`Admin user ${userId} deleted actual spend entry ${spendId}`);
      return { success: true, deletedId: spendId };
    } catch (error) {
      console.error('Error deleting actual spend:', error);
      throw error;
    }
  }

  async getActualSpendsInDateRange(benchmarkId, startDate, endDate) {
    return await this.db.getAllAsync(
      'SELECT * FROM actual_spends WHERE benchmark_id = ? AND period_start >= ? AND period_end <= ? ORDER BY period_start DESC',
      [benchmarkId, startDate, endDate]
    );
  }

  // Utility method to convert amounts between periods
  convertPeriodAmount(amount, fromPeriod, toPeriod) {
    const dailyAmount = fromPeriod === 'daily' ? amount :
                      fromPeriod === 'weekly' ? amount / 7 :
                      amount / 30; // monthly to daily approximation

    return toPeriod === 'daily' ? dailyAmount :
           toPeriod === 'weekly' ? dailyAmount * 7 :
           dailyAmount * 30; // daily to monthly approximation
  }

  // Utility method to calculate benchmark amount with occupancy
  calculateBenchmarkWithOccupancy(benchmarkAmount, occupancy) {
    return benchmarkAmount * (occupancy || 1);
  }

  // Get benchmark amount adjusted for current occupancy
  async getBenchmarkAmountWithOccupancy(benchmarkId, targetPeriod = null) {
    const benchmark = await this.getBenchmarkById(benchmarkId);
    if (!benchmark) return 0;

    const company = await this.getCompanyById(benchmark.company_id);
    if (!company) return benchmark.amount;

    let amount = benchmark.amount;

    // Convert to target period if specified
    if (targetPeriod && targetPeriod !== benchmark.period) {
      amount = this.convertPeriodAmount(amount, benchmark.period, targetPeriod);
    }

    // Multiply by current occupancy
    return this.calculateBenchmarkWithOccupancy(amount, company.current_occupancy);
  }

  // Notification methods
  async createNotification(notificationData) {
    const { companyId, benchmarkId, type, message, actualAmount, benchmarkAmount, variance } = notificationData;

    console.log('Creating notification:', notificationData);

    if (this.isWeb) {
      const notification = {
        id: this.mockData.notifications.length + 1,
        company_id: companyId,
        benchmark_id: benchmarkId,
        type,
        message,
        is_read: 0,
        created_at: new Date().toISOString()
      };
      this.mockData.notifications.push(notification);
      console.log('Created notification on web:', notification);

      // Send email notifications to all company users (only for exceeded notifications)
      if (type === 'budget_exceeded' || type === 'benchmark_exceeded') {
        await this.sendEmailNotifications(companyId, benchmarkId, type, actualAmount, benchmarkAmount, variance);
      }

      return notification.id;
    }

    const result = await this.db.runAsync(
      'INSERT INTO notifications (company_id, benchmark_id, type, message) VALUES (?, ?, ?, ?)',
      [companyId, benchmarkId, type, message]
    );

    // Send email notifications to all company users (only for exceeded notifications)
    if (type === 'budget_exceeded' || type === 'benchmark_exceeded') {
      await this.sendEmailNotifications(companyId, benchmarkId, type, actualAmount, benchmarkAmount, variance);
    }

    return result.lastInsertRowId;
  }

  async getNotificationsByCompany(companyId, unreadOnly = false) {
    if (this.isWeb) {
      let notifications = this.mockData.notifications
        .filter(n => n.company_id === companyId)
        .sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

      if (unreadOnly) {
        notifications = notifications.filter(n => n.is_read === 0);
      }

      return notifications;
    }

    const query = unreadOnly
      ? 'SELECT * FROM notifications WHERE company_id = ? AND is_read = 0 ORDER BY created_at DESC'
      : 'SELECT * FROM notifications WHERE company_id = ? ORDER BY created_at DESC';

    return await this.db.getAllAsync(query, [companyId]);
  }

  async markNotificationAsRead(id) {
    if (this.isWeb) {
      const notificationIndex = this.mockData.notifications.findIndex(n => n.id === id);
      if (notificationIndex !== -1) {
        this.mockData.notifications[notificationIndex].is_read = 1;
      }
      return;
    }

    await this.db.runAsync(
      'UPDATE notifications SET is_read = 1 WHERE id = ?',
      [id]
    );
  }

  async sendEmailNotifications(companyId, benchmarkId, type, actualAmount, benchmarkAmount, variance) {
    try {
      // Import email service dynamically to avoid circular dependencies
      const EmailService = (await import('./emailService.js')).default;

      // Get all users for the company
      const users = await this.getUsersByCompany(companyId);

      // Get benchmark details
      const benchmark = await this.getBenchmarkById(benchmarkId);

      // Get company details
      const company = await this.getCompanyById(companyId);

      if (!benchmark || !company || users.length === 0) {
        console.log('Missing data for email notification:', { benchmark: !!benchmark, company: !!company, usersCount: users.length });
        return;
      }

      console.log(`Sending email notifications to ${users.length} users for ${type} exceeded`);

      // Prepare notification data for email
      const emailData = {
        benchmarkName: benchmark.name,
        actualAmount: actualAmount || 0,
        benchmarkAmount: benchmarkAmount || benchmark.amount,
        variance: variance || 0,
        type: type,
        companyName: company.name,
        currency: benchmark.currency || 'GBP'
      };

      // Send email to all company users
      await EmailService.sendSpendingExceededNotification(users, emailData);

      console.log('Email notifications sent successfully');
    } catch (error) {
      console.error('Failed to send email notifications:', error);
      // Don't throw error to avoid breaking the notification creation
    }
  }

  // Occupancy management methods
  async updateCompanyOccupancy(companyId, newOccupancy, changeReason, userId) {
    console.log('DatabaseService.updateCompanyOccupancy called with:', { companyId, newOccupancy, changeReason, userId });

    // Get current occupancy
    const company = await this.getCompanyById(companyId);
    if (!company) {
      throw new Error('Company not found');
    }

    const oldOccupancy = company.current_occupancy;

    // Don't create history record if occupancy hasn't changed
    if (oldOccupancy === newOccupancy) {
      return;
    }

    if (this.isWeb) {
      // Update company occupancy in mock data
      const companyIndex = this.mockData.companies.findIndex(c => c.id === companyId);
      if (companyIndex !== -1) {
        this.mockData.companies[companyIndex].current_occupancy = newOccupancy;
        this.mockData.companies[companyIndex].updated_at = new Date().toISOString();
      }

      // Add to occupancy history
      const historyRecord = {
        id: this.mockData.occupancyHistory.length + 1,
        company_id: companyId,
        old_occupancy: oldOccupancy,
        new_occupancy: newOccupancy,
        change_reason: changeReason || null,
        created_by: userId,
        created_at: new Date().toISOString()
      };
      this.mockData.occupancyHistory.push(historyRecord);

      console.log('Updated occupancy in mock data:', historyRecord);
      return historyRecord.id;
    }

    // Update company occupancy in database
    await this.db.runAsync(
      'UPDATE companies SET current_occupancy = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [newOccupancy, companyId]
    );

    // Insert occupancy history record
    const result = await this.db.runAsync(
      'INSERT INTO occupancy_history (company_id, old_occupancy, new_occupancy, change_reason, created_by) VALUES (?, ?, ?, ?, ?)',
      [companyId, oldOccupancy, newOccupancy, changeReason, userId]
    );

    console.log('Updated occupancy in database, history ID:', result.lastInsertRowId);
    return result.lastInsertRowId;
  }

  async getOccupancyHistory(companyId, limit = 50) {
    console.log('DatabaseService.getOccupancyHistory called with:', { companyId, limit });

    if (this.isWeb) {
      const history = this.mockData.occupancyHistory
        .filter(h => h.company_id === companyId)
        .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
        .slice(0, limit);

      // Add user names to history records
      const historyWithUsers = history.map(record => {
        const user = this.mockData.users.find(u => u.id === record.created_by);
        return {
          ...record,
          created_by_name: user ? user.name : 'Unknown User'
        };
      });

      console.log('Retrieved occupancy history from mock data:', historyWithUsers);
      return historyWithUsers;
    }

    const history = await this.db.getAllAsync(`
      SELECT
        oh.*,
        u.name as created_by_name
      FROM occupancy_history oh
      LEFT JOIN users u ON oh.created_by = u.id
      WHERE oh.company_id = ?
      ORDER BY oh.created_at DESC
      LIMIT ?
    `, [companyId, limit]);

    console.log('Retrieved occupancy history from database:', history);
    return history;
  }

  async getOccupancyHistoryByDateRange(companyId, startDate, endDate) {
    console.log('DatabaseService.getOccupancyHistoryByDateRange called with:', { companyId, startDate, endDate });

    if (this.isWeb) {
      const history = this.mockData.occupancyHistory
        .filter(h =>
          h.company_id === companyId &&
          new Date(h.created_at) >= new Date(startDate) &&
          new Date(h.created_at) <= new Date(endDate)
        )
        .sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

      // Add user names to history records
      const historyWithUsers = history.map(record => {
        const user = this.mockData.users.find(u => u.id === record.created_by);
        return {
          ...record,
          created_by_name: user ? user.name : 'Unknown User'
        };
      });

      return historyWithUsers;
    }

    const history = await this.db.getAllAsync(`
      SELECT
        oh.*,
        u.name as created_by_name
      FROM occupancy_history oh
      LEFT JOIN users u ON oh.created_by = u.id
      WHERE oh.company_id = ?
        AND DATE(oh.created_at) >= DATE(?)
        AND DATE(oh.created_at) <= DATE(?)
      ORDER BY oh.created_at DESC
    `, [companyId, startDate, endDate]);

    return history;
  }
}

export default new DatabaseService();
