import DatabaseService from './database';

/**
 * AdminService - Centralized service for admin user management functions
 * This service provides a clean interface for all admin operations
 */
class AdminService {
  
  /**
   * User Profile Management
   */
  static async updateUserProfile(userId, profileData) {
    const { name, email } = profileData;
    
    // Validate input
    if (!name?.trim() || !email?.trim()) {
      throw new Error('Name and email are required');
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new Error('Invalid email format');
    }

    return await DatabaseService.updateUserProfile(userId, { name: name.trim(), email: email.trim() });
  }

  /**
   * Password Management
   */
  static async resetUserPassword(userId, newPassword) {
    if (!newPassword?.trim()) {
      throw new Error('Password is required');
    }

    if (newPassword.length < 6) {
      throw new Error('Password must be at least 6 characters long');
    }

    return await DatabaseService.resetUserPassword(userId, newPassword);
  }

  /**
   * User Status Management
   */
  static async toggleUserStatus(userId, isActive, currentUserId) {
    if (userId === currentUserId) {
      throw new Error('You cannot change your own account status');
    }

    return await DatabaseService.toggleUserStatus(userId, isActive);
  }

  /**
   * Admin Rights Management
   */
  static async toggleAdminRights(userId, isAdmin, currentUserId) {
    if (userId === currentUserId) {
      throw new Error('You cannot change your own admin status');
    }

    const user = await DatabaseService.getUserById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    return await DatabaseService.updateUser(userId, {
      name: user.name,
      isAdmin,
      permissions: user.permissions
    });
  }

  /**
   * User Search and Filtering
   */
  static async searchUsers(companyId, searchTerm = '') {
    return await DatabaseService.searchUsers(companyId, searchTerm);
  }

  /**
   * Bulk Operations
   */
  static async bulkDeleteUsers(userIds, currentUserId) {
    if (userIds.includes(currentUserId)) {
      throw new Error('You cannot delete your own account');
    }

    if (userIds.length === 0) {
      throw new Error('No users selected for deletion');
    }

    return await DatabaseService.bulkDeleteUsers(userIds);
  }

  static async bulkToggleAdmin(userIds, isAdmin, currentUserId) {
    if (userIds.includes(currentUserId)) {
      throw new Error('You cannot change your own admin status');
    }

    if (userIds.length === 0) {
      throw new Error('No users selected');
    }

    return await DatabaseService.bulkToggleAdmin(userIds, isAdmin);
  }

  /**
   * User Statistics
   */
  static async getUserStats(companyId) {
    return await DatabaseService.getUserStats(companyId);
  }

  /**
   * User Creation with Validation
   */
  static async createUser(userData, companyId) {
    const { email, password, name, isAdmin = false } = userData;

    // Validate input
    if (!email?.trim() || !password?.trim() || !name?.trim()) {
      throw new Error('Email, password, and name are required');
    }

    if (password.length < 6) {
      throw new Error('Password must be at least 6 characters long');
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new Error('Invalid email format');
    }

    // Check if user already exists
    const existingUser = await DatabaseService.getUserByEmail(email);
    if (existingUser) {
      throw new Error('A user with this email already exists');
    }

    return await DatabaseService.createUser({
      email: email.trim(),
      password,
      name: name.trim(),
      isAdmin,
      companyId,
      permissions: []
    });
  }

  /**
   * User Deletion with Validation
   */
  static async deleteUser(userId, currentUserId) {
    if (userId === currentUserId) {
      throw new Error('You cannot delete your own account');
    }

    const user = await DatabaseService.getUserById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    return await DatabaseService.deleteUser(userId);
  }

  /**
   * Get All Company Users
   */
  static async getCompanyUsers(companyId) {
    return await DatabaseService.getUsersByCompany(companyId);
  }

  /**
   * Utility Functions
   */
  static formatLastLogin(lastLogin) {
    if (!lastLogin) return 'Never';
    const date = new Date(lastLogin);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }

  static validateAdminPermissions(currentUser) {
    if (!currentUser?.is_admin) {
      throw new Error('Admin permissions required');
    }
  }

  /**
   * Advanced User Management
   */
  static async getUserActivity(companyId) {
    const users = await DatabaseService.getUsersByCompany(companyId);
    return users.map(user => ({
      id: user.id,
      name: user.name,
      email: user.email,
      lastLogin: user.last_login,
      isActive: user.is_active,
      isAdmin: user.is_admin,
      createdAt: user.created_at
    }));
  }

  static async getInactiveUsers(companyId, daysSinceLastLogin = 30) {
    const users = await DatabaseService.getUsersByCompany(companyId);
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysSinceLastLogin);

    return users.filter(user => {
      if (!user.last_login) return true;
      const lastLogin = new Date(user.last_login);
      return lastLogin < cutoffDate;
    });
  }
}

export default AdminService;
