import { useState, useEffect } from 'react';
import { useClerkAuth } from '../contexts/ClerkAuthContext';
import DatabaseService from '../services/database';

export const useInvitations = () => {
  const { user } = useClerkAuth();
  const [invitations, setInvitations] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pendingCount, setPendingCount] = useState(0);

  const loadInvitations = async () => {
    if (!user?.email) return;

    try {
      setLoading(true);
      const userInvitations = await DatabaseService.getUserInvitations(user.email);
      setInvitations(userInvitations);
      
      // Count pending invitations
      const pending = userInvitations.filter(inv => inv.status === 'pending').length;
      setPendingCount(pending);
    } catch (error) {
      console.error('Error loading invitations:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadInvitations();
  }, [user?.email]);

  const refreshInvitations = () => {
    loadInvitations();
  };

  return {
    invitations,
    loading,
    pendingCount,
    refreshInvitations
  };
};

export default useInvitations;
