import React, { useState, useEffect } from 'react';
import { View, Alert } from 'react-native';
import { Button, Text } from 'react-native-paper';
import AdminService from '../services/adminService';
import { useClerkAuth } from '../contexts/ClerkAuthContext';

/**
 * Example component demonstrating how to use AdminService
 * This is for reference only - the actual implementation is in UserManagementScreen
 */
const AdminServiceExample = () => {
  const { user, company } = useClerkAuth();
  const [users, setUsers] = useState([]);
  const [userStats, setUserStats] = useState(null);

  useEffect(() => {
    if (user?.is_admin && company) {
      loadData();
    }
  }, [user, company]);

  const loadData = async () => {
    try {
      // Validate admin permissions
      AdminService.validateAdminPermissions(user);

      // Load users and stats
      const [companyUsers, stats] = await Promise.all([
        AdminService.getCompanyUsers(company.id),
        AdminService.getUserStats(company.id)
      ]);

      setUsers(companyUsers);
      setUserStats(stats);
    } catch (error) {
      Alert.alert('Error', error.message);
    }
  };

  // Example: Create a new user
  const handleCreateUser = async () => {
    try {
      AdminService.validateAdminPermissions(user);

      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'New User',
        isAdmin: false
      };

      await AdminService.createUser(userData, company.id);
      await loadData(); // Reload data
      Alert.alert('Success', 'User created successfully');
    } catch (error) {
      Alert.alert('Error', error.message);
    }
  };

  // Example: Reset user password
  const handleResetPassword = async (userId) => {
    try {
      AdminService.validateAdminPermissions(user);

      const newPassword = 'newpassword123';
      await AdminService.resetUserPassword(userId, newPassword);
      Alert.alert('Success', 'Password reset successfully');
    } catch (error) {
      Alert.alert('Error', error.message);
    }
  };

  // Example: Toggle user status
  const handleToggleUserStatus = async (userId, currentStatus) => {
    try {
      AdminService.validateAdminPermissions(user);

      await AdminService.toggleUserStatus(userId, !currentStatus, user.id);
      await loadData(); // Reload data
      Alert.alert('Success', 'User status updated');
    } catch (error) {
      Alert.alert('Error', error.message);
    }
  };

  // Example: Bulk delete users
  const handleBulkDelete = async (userIds) => {
    try {
      AdminService.validateAdminPermissions(user);

      await AdminService.bulkDeleteUsers(userIds, user.id);
      await loadData(); // Reload data
      Alert.alert('Success', `${userIds.length} users deleted`);
    } catch (error) {
      Alert.alert('Error', error.message);
    }
  };

  // Example: Search users
  const handleSearchUsers = async (searchTerm) => {
    try {
      AdminService.validateAdminPermissions(user);

      const results = await AdminService.searchUsers(company.id, searchTerm);
      setUsers(results);
    } catch (error) {
      Alert.alert('Error', error.message);
    }
  };

  // Example: Get inactive users
  const handleGetInactiveUsers = async () => {
    try {
      AdminService.validateAdminPermissions(user);

      const inactiveUsers = await AdminService.getInactiveUsers(company.id, 30);
      Alert.alert('Inactive Users', `Found ${inactiveUsers.length} inactive users`);
    } catch (error) {
      Alert.alert('Error', error.message);
    }
  };

  if (!user?.is_admin) {
    return (
      <View>
        <Text>Admin permissions required</Text>
      </View>
    );
  }

  return (
    <View style={{ padding: 20 }}>
      <Text variant="headlineSmall">Admin Service Examples</Text>
      
      {userStats && (
        <View style={{ marginVertical: 20 }}>
          <Text>Total Users: {userStats.total}</Text>
          <Text>Active Users: {userStats.active}</Text>
          <Text>Inactive Users: {userStats.inactive}</Text>
          <Text>Admins: {userStats.admins}</Text>
        </View>
      )}

      <Button mode="outlined" onPress={handleCreateUser} style={{ marginVertical: 5 }}>
        Create New User
      </Button>

      <Button mode="outlined" onPress={() => handleSearchUsers('john')} style={{ marginVertical: 5 }}>
        Search Users (john)
      </Button>

      <Button mode="outlined" onPress={handleGetInactiveUsers} style={{ marginVertical: 5 }}>
        Get Inactive Users
      </Button>

      {users.length > 0 && (
        <View style={{ marginTop: 20 }}>
          <Text variant="titleMedium">Users:</Text>
          {users.slice(0, 3).map(userItem => (
            <View key={userItem.id} style={{ marginVertical: 10, padding: 10, backgroundColor: '#f5f5f5' }}>
              <Text>{userItem.name} ({userItem.email})</Text>
              <Text>Status: {userItem.is_active ? 'Active' : 'Inactive'}</Text>
              <Text>Last Login: {AdminService.formatLastLogin(userItem.last_login)}</Text>
              
              <View style={{ flexDirection: 'row', marginTop: 10 }}>
                <Button 
                  mode="outlined" 
                  compact 
                  onPress={() => handleResetPassword(userItem.id)}
                  style={{ marginRight: 10 }}
                >
                  Reset Password
                </Button>
                
                <Button 
                  mode="outlined" 
                  compact 
                  onPress={() => handleToggleUserStatus(userItem.id, userItem.is_active)}
                >
                  {userItem.is_active ? 'Deactivate' : 'Activate'}
                </Button>
              </View>
            </View>
          ))}
        </View>
      )}
    </View>
  );
};

export default AdminServiceExample;
