import React from "react";
import { StyleSheet, Dimensions, View } from "react-native";
import { MD3LightTheme } from "react-native-paper";

const { width } = Dimensions.get("window");

export const colors = {
  // Primary colors - Blue theme
  primary: "#1976D2",
  primaryLight: "#42A5F5",
  primaryDark: "#1565C0",

  // Secondary colors - Blue variations
  secondary: "#2196F3",
  secondaryLight: "#64B5F6",
  secondaryDark: "#1976D2",

  // Accent colors - Blue variations
  accent: "#03A9F4",
  accentLight: "#4FC3F7",
  accentDark: "#0288D1",

  // Status colors - Blue theme with appropriate status colors
  success: "#2196F3",
  warning: "#FF9800",
  warningLight: "#FFB74D",
  error: "#F44336",
  info: "#42A5F5",

  // Neutral colors
  background: "#FAFBFC",
  surface: "#FFFFFF",
  surfaceVariant: "#F8F9FA",

  // Text colors
  text: "#1A1A1A",
  textSecondary: "#5A5A5A",
  textLight: "#8A8A8A",
  textOnPrimary: "#FFFFFF",
  textOnSurface: "#1A1A1A",

  // Border colors
  border: "#E8E9EA",
  borderLight: "#F2F3F4",

  // Shadow color
  shadow: "#000000",

  // Gradient colors - Blue theme
  gradientPrimary: ["#1976D2", "#42A5F5"],
  gradientSecondary: ["#2196F3", "#64B5F6"],
  gradientAccent: ["#03A9F4", "#4FC3F7"],

  // Industry specific colors - Blue theme
  careHome: "#2196F3",
  hotel: "#1976D2",
  hostel: "#42A5F5",
  hospital: "#03A9F4",
};

export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
  xxxl: 64,
  xxxxl: 80,
};

export const borderRadius = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 20,
  xxl: 24,
  round: 50,
  full: 9999,
};

export const elevation = {
  none: 0,
  xs: 1,
  sm: 2,
  md: 4,
  lg: 8,
  xl: 12,
  xxl: 16,
};

export const typography = {
  fontSizes: {
    xs: 11,
    sm: 13,
    md: 15,
    lg: 17,
    xl: 19,
    xxl: 22,
    xxxl: 26,
    xxxxl: 30,
  },
  fontWeights: {
    light: "300",
    normal: "400",
    medium: "500",
    semibold: "600",
    bold: "700",
  },
  lineHeights: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6,
  },
};

// Common component styles
export const commonStyles = StyleSheet.create({
  // Containers
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },

  scrollContainer: {
    flexGrow: 1,
    paddingBottom: spacing.xxxl,
    paddingHorizontal: spacing.xl,
    paddingTop: spacing.lg,
  },

  // Cards - Soldo-inspired modern design
  card: {
    margin: spacing.md,
    marginBottom: spacing.sm,
    backgroundColor: colors.surface,
    borderRadius: borderRadius.xl,
    elevation: elevation.md,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 16,
    padding: spacing.xl,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },

  cardLarge: {
    margin: spacing.md,
    marginBottom: spacing.lg,
    backgroundColor: colors.surface,
    borderRadius: 24, // Very obvious Soldo-style rounded corners
    elevation: elevation.lg,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.15,
    shadowRadius: 32,
    padding: spacing.xxxl,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },

  cardSection: {
    margin: spacing.sm,
    marginBottom: spacing.xs,
    backgroundColor: colors.surface,
    borderRadius: borderRadius.lg,
    elevation: elevation.sm,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 8,
    padding: spacing.lg,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },

  // Headers - Soldo-inspired clean design
  headerGradient: {
    paddingTop: spacing.xxxxl + 20,
    paddingBottom: spacing.xxxxl,
    paddingHorizontal: spacing.xl,
    borderBottomLeftRadius: 32, // Very obvious Soldo-style rounded corners
    borderBottomRightRadius: 32,
    elevation: elevation.lg,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
  },

  headerContent: {
    alignItems: "center",
    justifyContent: "center",
    maxWidth: 600,
    alignSelf: "center",
  },

  headerTitle: {
    color: colors.textOnPrimary,
    fontSize: 48, // Very large, Soldo-style typography
    fontWeight: "800", // Extra bold for Soldo-style impact
    textAlign: "center",
    marginBottom: spacing.lg,
    letterSpacing: -1,
    lineHeight: 52,
    textShadowColor: 'rgba(0,0,0,0.1)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },

  headerSubtitle: {
    color: colors.textOnPrimary,
    fontSize: 20, // Larger, more prominent subtitle
    textAlign: "center",
    opacity: 0.95,
    lineHeight: 28,
    maxWidth: 600,
    fontWeight: "500", // Medium weight for better readability
    textShadowColor: 'rgba(0,0,0,0.1)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },

  // Sections
  section: {
    marginVertical: spacing.lg,
  },

  sectionTitle: {
    fontSize: typography.fontSizes.xxl,
    fontWeight: typography.fontWeights.semibold,
    color: colors.text,
    marginBottom: spacing.md,
    lineHeight: typography.fontSizes.xxl * typography.lineHeights.tight,
  },

  sectionSubtitle: {
    fontSize: typography.fontSizes.md,
    color: colors.textSecondary,
    marginBottom: spacing.lg,
    lineHeight: typography.fontSizes.md * typography.lineHeights.relaxed,
  },

  // Buttons - Soldo-inspired modern buttons
  buttonPrimary: {
    backgroundColor: colors.primary,
    borderRadius: borderRadius.lg,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.xl,
    elevation: elevation.sm,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },

  buttonSecondary: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.lg,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.xl,
    borderWidth: 1,
    borderColor: colors.border,
    elevation: elevation.xs,
  },

  buttonText: {
    fontSize: typography.fontSizes.md,
    fontWeight: typography.fontWeights.medium,
    textAlign: "center",
    lineHeight: typography.fontSizes.md * typography.lineHeights.normal,
  },

  // Forms - Soldo-inspired clean forms
  inputContainer: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.lg,
    borderWidth: 1,
    borderColor: colors.border,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    marginBottom: spacing.md,
    elevation: elevation.xs,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },

  inputFocused: {
    borderColor: colors.primary,
    elevation: elevation.sm,
    shadowOpacity: 0.1,
  },

  // Lists - Soldo-inspired clean lists
  listItem: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.lg,
    padding: spacing.lg,
    marginBottom: spacing.sm,
    borderWidth: 1,
    borderColor: colors.borderLight,
    elevation: elevation.xs,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },

  // Status indicators - Soldo-inspired clean badges
  statusBadge: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.round,
    alignSelf: "flex-start",
  },

  statusText: {
    fontSize: typography.fontSizes.sm,
    fontWeight: typography.fontWeights.medium,
    lineHeight: typography.fontSizes.sm * typography.lineHeights.normal,
  },

  // Dividers - Soldo-inspired subtle dividers
  divider: {
    height: 1,
    backgroundColor: colors.borderLight,
    marginVertical: spacing.md,
  },

  // Loading states - Soldo-inspired clean loading
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: colors.background,
  },

  // Empty states - Soldo-inspired friendly empty states
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: spacing.xxl,
  },

  emptyText: {
    fontSize: typography.fontSizes.lg,
    color: colors.textSecondary,
    textAlign: "center",
    marginTop: spacing.md,
    lineHeight: typography.fontSizes.lg * typography.lineHeights.relaxed,
  },

  // Soldo-inspired layout components
  contentSection: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.xl,
    padding: spacing.xxl,
    margin: spacing.md,
    elevation: elevation.sm,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
  },

  gridContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: spacing.md,
    justifyContent: "space-between",
  },

  gridItem: {
    flex: 1,
    minWidth: 280,
    backgroundColor: colors.surface,
    borderRadius: borderRadius.lg,
    padding: spacing.lg,
    elevation: elevation.xs,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.06,
    shadowRadius: 4,
  },

  // Responsive
  responsiveContainer: {
    maxWidth: width > 768 ? 1200 : "100%",
    alignSelf: "center",
    width: "100%",
  },

  // Shadows
  shadowSmall: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: elevation.xs,
  },

  shadowMedium: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: elevation.md,
  },

  shadowLarge: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: elevation.lg,
  },

  // Compact Dialog Styles - Global styles for small dialogs
  compactDialog: {
    maxWidth: 400,
    alignSelf: "center",
    marginHorizontal: spacing.lg,
  },

  compactDialogTitle: {
    fontSize: typography.fontSizes.lg,
    fontWeight: typography.fontWeights.bold,
    textAlign: "center",
    paddingBottom: spacing.sm,
  },

  compactDialogContent: {
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
  },

  compactDialogText: {
    textAlign: "center",
    lineHeight: 22,
  },

  compactDialogActions: {
    justifyContent: "center",
    paddingTop: spacing.sm,
    paddingBottom: spacing.md,
    gap: spacing.sm,
  },

  compactDialogButton: {
    minWidth: 80,
    borderRadius: borderRadius.md,
  },

  // Soldo-inspired navigation and header styles
  navigationHeader: {
    backgroundColor: colors.surface,
    elevation: elevation.sm,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },

  // Soldo-inspired data visualization
  metricCard: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.lg,
    padding: spacing.lg,
    marginBottom: spacing.md,
    borderWidth: 1,
    borderColor: colors.borderLight,
    elevation: elevation.xs,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },

  metricValue: {
    fontSize: typography.fontSizes.xxxl,
    fontWeight: typography.fontWeights.bold,
    color: colors.text,
    lineHeight: typography.fontSizes.xxxl * typography.lineHeights.tight,
  },

  metricLabel: {
    fontSize: typography.fontSizes.sm,
    color: colors.textSecondary,
    marginTop: spacing.xs,
    fontWeight: typography.fontWeights.medium,
    lineHeight: typography.fontSizes.sm * typography.lineHeights.normal,
  },

  // Soldo-inspired progress indicators
  progressContainer: {
    backgroundColor: colors.surfaceVariant,
    borderRadius: borderRadius.round,
    height: 8,
    overflow: "hidden",
  },

  progressBar: {
    height: "100%",
    borderRadius: borderRadius.round,
  },

  // Soldo-inspired action buttons
  actionButton: {
    backgroundColor: colors.primary,
    borderRadius: borderRadius.lg,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.xl,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    elevation: elevation.sm,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },

  actionButtonText: {
    color: colors.textOnPrimary,
    fontSize: typography.fontSizes.md,
    fontWeight: typography.fontWeights.medium,
    marginLeft: spacing.sm,
    lineHeight: typography.fontSizes.md * typography.lineHeights.normal,
  },

  // Soldo-inspired floating action button
  fab: {
    position: "absolute",
    bottom: spacing.xl,
    right: spacing.xl,
    backgroundColor: colors.primary,
    borderRadius: borderRadius.round,
    width: 56,
    height: 56,
    justifyContent: "center",
    alignItems: "center",
    elevation: elevation.lg,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
  },

  // Soldo-inspired search and filter components
  searchContainer: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.lg,
    borderWidth: 1,
    borderColor: colors.border,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    marginBottom: spacing.lg,
    elevation: elevation.xs,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },

  // Soldo-inspired table styles
  tableContainer: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.lg,
    overflow: "hidden",
    elevation: elevation.xs,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },

  tableHeader: {
    backgroundColor: colors.surfaceVariant,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },

  tableRow: {
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },

  // Soldo-inspired modal and dialog styles
  modalContainer: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.xl,
    margin: spacing.xl,
    elevation: elevation.lg,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
  },

  modalHeader: {
    padding: spacing.xl,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },

  modalContent: {
    padding: spacing.xl,
  },

  modalActions: {
    flexDirection: "row",
    justifyContent: "flex-end",
    gap: spacing.md,
    padding: spacing.xl,
    borderTopWidth: 1,
    borderTopColor: colors.borderLight,
  },

  // Soldo-inspired notification styles
  notification: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.lg,
    padding: spacing.lg,
    margin: spacing.md,
    borderWidth: 1,
    borderColor: colors.borderLight,
    elevation: elevation.sm,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
  },

  notificationSuccess: {
    borderLeftWidth: 4,
    borderLeftColor: colors.success,
  },

  notificationWarning: {
    borderLeftWidth: 4,
    borderLeftColor: colors.warning,
  },

  notificationError: {
    borderLeftWidth: 4,
    borderLeftColor: colors.error,
  },

  notificationInfo: {
    borderLeftWidth: 4,
    borderLeftColor: colors.info,
  },
});

// Utility functions
export const getIndustryColor = (industry) => {
  switch (industry?.toLowerCase()) {
    case "care-home":
    case "care home":
      return colors.careHome;
    case "hotel":
      return colors.hotel;
    case "hostel":
      return colors.hostel;
    case "hospital":
      return colors.hospital;
    default:
      return colors.primary;
  }
};

export const getStatusColor = (status) => {
  switch (status?.toLowerCase()) {
    case "active":
    case "success":
      return colors.success;
    case "warning":
    case "pending":
      return colors.warning;
    case "error":
    case "failed":
      return colors.error;
    case "info":
    default:
      return colors.info;
  }
};

// Gradient component fallback
export const GradientView = ({
  colors: gradientColors,
  style,
  children,
  ...props
}) => {
  // Try to use LinearGradient if available, otherwise fallback to solid color
  let LinearGradient;
  try {
    LinearGradient = require("expo-linear-gradient").LinearGradient;
    return (
      <LinearGradient colors={gradientColors} style={style} {...props}>
        {children}
      </LinearGradient>
    );
  } catch (error) {
    // Fallback to solid color
    return (
      <View style={[style, { backgroundColor: gradientColors[0] }]} {...props}>
        {children}
      </View>
    );
  }
};

// React Native Paper theme configuration
export const paperTheme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    primary: colors.primary,
    primaryContainer: colors.primaryLight + '20',
    secondary: colors.secondary,
    secondaryContainer: colors.secondaryLight + '20',
    tertiary: colors.accent,
    tertiaryContainer: colors.accentLight + '20',
    surface: colors.surface,
    surfaceVariant: colors.surfaceVariant,
    background: colors.background,
    error: colors.error,
    onPrimary: colors.textOnPrimary,
    onSecondary: colors.textOnPrimary,
    onTertiary: colors.textOnPrimary,
    onSurface: colors.text,
    onSurfaceVariant: colors.textSecondary,
    onBackground: colors.text,
    outline: colors.border,
    outlineVariant: colors.borderLight,
    inverseSurface: colors.primary,
    inverseOnSurface: colors.textOnPrimary,
    inversePrimary: colors.primaryLight,
    shadow: colors.shadow,
    scrim: colors.shadow,
    surfaceDisabled: colors.borderLight,
    onSurfaceDisabled: colors.textLight,
    backdrop: colors.shadow + '50',
  },
};

export default {
  colors,
  spacing,
  borderRadius,
  elevation,
  typography,
  commonStyles,
  getIndustryColor,
  getStatusColor,
  GradientView,
  paperTheme,
};
