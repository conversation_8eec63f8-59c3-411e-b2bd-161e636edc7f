import AsyncStorage from "@react-native-async-storage/async-storage";

export const clearAllSessionData = async () => {
  try {
    await AsyncStorage.removeItem("userId");
    await AsyncStorage.removeItem("hasLoggedOut");
    await AsyncStorage.removeItem("selectedCompanyId");
    console.log("All session data cleared");
  } catch (error) {
    console.error("Error clearing session data:", error);
  }
};
