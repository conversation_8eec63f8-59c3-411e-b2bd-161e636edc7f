import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { PaperProvider } from 'react-native-paper';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Platform, View, Text } from 'react-native';
import { ClerkProvider } from '@clerk/clerk-expo';
import { tokenCache } from '@clerk/clerk-expo/token-cache';


import { SubscriptionProvider } from './src/contexts/SubscriptionContext';
import AppNavigator from './src/navigation/AppNavigator';
import { paperTheme } from './src/styles/theme';
import OverdueNotificationService from './src/services/overdueNotificationService';

// Get Clerk publishable key
const clerkPublishableKey = process.env.EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY;

if (!clerkPublishableKey) {
  throw new Error(
    'Missing Clerk Publishable Key. Please:\n' +
    '1. Create .env file in root directory\n' +
    '2. Add: EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY=your_key_here\n' +
    '3. Restart development server'
  );
}

// Validate key format
if (!clerkPublishableKey.startsWith('pk_test_') && !clerkPublishableKey.startsWith('pk_live_')) {
  throw new Error(
    'Invalid Clerk Publishable Key format. Key should start with pk_test_ or pk_live_'
  );
}

console.log('✅ Clerk Publishable Key loaded successfully');
console.log('Key prefix:', clerkPublishableKey.substring(0, 8) + '...');

// Web-specific styles
if (Platform.OS === 'web') {
  const style = document.createElement('style');
  style.textContent = `
    html, body, #root {
      height: 100%;
      overflow-x: hidden;
      overflow-y: auto;
    }

    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    }

    * {
      -webkit-overflow-scrolling: touch;
    }
  `;
  document.head.appendChild(style);
}

// Error Boundary Component
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('App Error Boundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#f0f0f0', padding: 20 }}>
          <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#d32f2f', marginBottom: 10 }}>
            Something went wrong
          </Text>
          <Text style={{ fontSize: 16, color: '#666', textAlign: 'center', marginBottom: 20 }}>
            {this.state.error?.message || 'An unexpected error occurred'}
          </Text>
          <Text style={{ fontSize: 14, color: '#999', textAlign: 'center' }}>
            Check the console for more details
          </Text>
        </View>
      );
    }

    return this.props.children;
  }
}

export default function App() {
  // Start overdue notification checking
  React.useEffect(() => {
    console.log('🔄 Starting overdue notification service...');

    // Start periodic checking every 24 hours
    const intervalId = OverdueNotificationService.startPeriodicCheck(24);

    // Cleanup on unmount
    return () => {
      OverdueNotificationService.stopPeriodicCheck(intervalId);
    };
  }, []);

  return (
    <ClerkProvider
      publishableKey={clerkPublishableKey}
      tokenCache={tokenCache}
    >
      <ErrorBoundary>
        <SafeAreaProvider>
          <PaperProvider theme={paperTheme}>
            <SubscriptionProvider>
              <AppNavigator />
              <StatusBar style="auto" />
            </SubscriptionProvider>
          </PaperProvider>
        </SafeAreaProvider>
      </ErrorBoundary>
    </ClerkProvider>
  );
}
